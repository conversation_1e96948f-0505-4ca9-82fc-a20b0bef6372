Citizen.CreateThread(function()
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end
	
	PlayerData = ESX.GetPlayerData()
	
end)

ObjectInFront = function(ped, pos)
	local entityWorld = GetOffsetFromEntityInWorldCoords(ped, 0.0, 1.5, 0.0)
	local car = CastRayPointToPoint(pos.x, pos.y, pos.z, entityWorld.x, entityWorld.y, entityWorld.z, 30, ped, 0)
	local _, _, _, _, result = GetRaycastResult(car)
	return result
end

local Cooldown_count = 0
	
	local function Cooldown(sec)
		CreateThread(function()
			Cooldown_count = sec
			while Cooldown_count ~= 0 do
				Cooldown_count = Cooldown_count - 1
				Wait(1000)
			end	
			Cooldown_count = 0
		end)	
	end

RegisterNetEvent('esx_misc2:Start_repair')
AddEventHandler('esx_misc2:Start_repair', function(ped, coords, veh)
	local dict
	local model = 'prop_carjack'
	local offset = GetOffsetFromEntityInWorldCoords(ped, 0.0, -2.0, 0.0)
	local headin = GetEntityHeading(ped)
	local vehicle   = ESX.Game.GetVehicleInDirection()
	local msg = '<font size=5 color=white>جاري إصلاح المركبة'
	FreezeEntityPosition(veh, true)
	TriggerEvent('esx_misc:hidehud', true)
	local vehpos = GetEntityCoords(veh)
	local vehjack = CreateObject(GetHashKey(model), vehpos.x, vehpos.y, vehpos.z - 0.5, true, true, true)
	disableAllControlActions = true	
	Citizen.CreateThread(function()
	while disableAllControlActions do
	Citizen.Wait(0)
	DisableAllControlActions(0) --disable all control (comment it if you want to detect how many time key pressed)
	EnableControlAction(0, 249, true)  -- N
	EnableControlAction(0, 311, true)  -- K
	EnableControlAction(0, 1, true) -- Disable pan
	EnableControlAction(0, 2, true) -- Disable tilt
	 end
	end)
	exports['pogressBar']:drawBar(15000, 'تصليح المركبة')
	TaskStartScenarioInPlace(ped, 'WORLD_HUMAN_HAMMERING', 0, true)
	Citizen.CreateThread(function()
		Citizen.Wait(15000-20000)
		FreezeEntityPosition(veh, false)
		DeleteObject(vehjack)
		SetVehicleFixed(veh)
		SetEntityCollision(veh, true, true)
		ClearPedTasksImmediately(ped)
		ESX.ShowNotification('تم إصلاح المركبة')
		TriggerEvent('esx_misc:hidehud', false)
		disableAllControlActions = false
	end)
end)

RegisterNetEvent('esx_misc2:repairveh')
AddEventHandler('esx_misc2:repairveh', function()
	local ped = PlayerPedId()
	local coords = GetEntityCoords(ped)
	local veh = ObjectInFront(ped, coords)
	if DoesEntityExist(veh) then
		if IsEntityAVehicle(veh) then
		if Cooldown_count == 0 then 
			ESX.UI.Menu.CloseAll()
				        Cooldown(75)
						SetEntityAsMissionEntity(veh, true, true)
						TriggerServerEvent('esx_misc2:RemoveItem_repair', ped, coords, veh)
		else
	    ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..Cooldown_count..' ثانية</font> لإصلاح المركبة')
	    end
		else
			ESX.ShowNotification('لاتوجد مركبة بالقرب منك') -- TRANSLATE THIS - THAT SAY WHEN YOU DON'T HAVE ANY VEHICLE IN THE NEAR
		end
	end
end)