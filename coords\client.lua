local display = false

RegisterCommand("vodka", function()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local playerHeading = GetEntityHeading(PlayerPedId())
    local x = string.sub(playerCoords.x, 0, 8) 
    local y = string.sub(playerCoords.y, 0, 8)
    local z = string.sub(playerCoords.z, 0, 6)
    local w = string.sub(playerHeading, 0, 5)
    GetCoords('Get', 'x = ' .. x .. ', y = ' .. y .. ', z = ' .. z)
    GetcoordsNormal('normal', x .. ', ' .. y .. ', ' .. z)
    GetcoordsVector3('vector3', 'vector3(' .. x .. ', ' .. y .. ', ' .. z .. ')')
    GetcoordsVector4('vector4', 'vector4(' .. x .. ', ' .. y .. ', ' .. z .. ', ' .. w .. ')')
    SetDisplay(not display)
end)

function GetCoords(type, text)
	SendNUIMessage({
        type = type,
		text = text
	})
end

function GetcoordsNormal(type, text)
    SendNUIMessage({
        type = type,
		text = text
	})
end

function GetcoordsVector3(type, text)
    SendNUIMessage({
        type = type,
		text = text
	})
end

function GetcoordsVector4(type, text)
    SendNUIMessage({
        type = type,
		text = text
	})
end

function SetDisplay(bool)
    display = bool
    SetNuiFocus(bool, bool)
    SendNUIMessage({
        type = "ui",
        status = bool,
    })
end

RegisterNUICallback("GetcloseButton", function(data)
    SetNuiFocus(false, false)
    SetDisplay(false)
end)

Citizen.CreateThread(function()
    while display do
        Citizen.Wait(0)
        DisableControlAction(0, 1, display)
        DisableControlAction(0, 2, display)
        DisableControlAction(0, 142, display)
        DisableControlAction(0, 18, display)
        DisableControlAction(0, 322, display)
        DisableControlAction(0, 106, display)
    end
end)