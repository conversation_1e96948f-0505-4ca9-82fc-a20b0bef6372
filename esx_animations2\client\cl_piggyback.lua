local piggyback = {
	InProgress = false,
	targetSrc = -1,
	type = "",
	personPiggybacking = {
		animDict = "anim@arena@celeb@flat@paired@no_props@",
		anim = "piggyback_c_player_a",
		flag = 49,
	},
	personBeingPiggybacked = {
		animDict = "anim@arena@celeb@flat@paired@no_props@",
		anim = "piggyback_c_player_b",
		attachX = 0.0,
		attachY = -0.07,
		attachZ = 0.45,
		flag = 33,
	}
}

local function drawNativeNotification(text)
    SetTextComponentFormat("STRING")
    AddTextComponentString(text)
    DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end

local function GetClosestPlayer(radius)
    local players = GetActivePlayers()
    local closestDistance = -1
    local closestPlayer = -1
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _,playerId in ipairs(players) do
        local targetPed = GetPlayerPed(playerId)
        if targetPed ~= playerPed then
            local targetCoords = GetEntityCoords(targetPed)
            local distance = #(targetCoords-playerCoords)
            if closestDistance == -1 or closestDistance > distance then
                closestPlayer = playerId
                closestDistance = distance
            end
        end
    end
	if closestDistance ~= -1 and closestDistance <= radius then
		return closestPlayer
	else
		return nil
	end
end

local function ensureAnimDict(animDict)
    if not HasAnimDictLoaded(animDict) then
        RequestAnimDict(animDict)
        while not HasAnimDictLoaded(animDict) do
            Wait(0)
        end        
    end
    return animDict
end

RegisterCommand("piggyback",function(source, args)
	if exports["esx_jail"]:isPlayerJailed() then return end
	local playerPed = GetPlayerPed(-1)
	if IsPedInAnyVehicle(playerPed, true) then
		ESX.ShowNotification('<font color=red>لايمكنك حمل شخص وانت داخل مركبة')
	else
		if not piggyback.InProgress then
			local closestPlayer = ESX.Game.GetClosestPlayer()
			local closestPlayerPed = GetPlayerPed(closestPlayer)
			
			if not IsPedDeadOrDying(closestPlayerPed, 1) and not IsEntityPlayingAnim(closestPlayerPed, "random@dealgonewrong", "idle_a", 3) then
			local closestPlayer_ = GetClosestPlayer(3)
			if closestPlayer_ then
				local targetSrc = GetPlayerServerId(closestPlayer)
				if targetSrc ~= -1 then
					piggyback.InProgress = true
					piggyback.targetSrc = targetSrc
					TriggerServerEvent("Piggyback:sync",targetSrc)
					ensureAnimDict(piggyback.personPiggybacking.animDict)
					piggyback.type = "piggybacking"
				else
					drawNativeNotification("<FONT FACE='A9eelsh'>ﻚﻨﻣ ﺏﺮﻘﻟﺎﺑ ﺐﻋﻻ ﺪﺟﻮﻳﻻ")
				end
			else
				drawNativeNotification("<FONT FACE='A9eelsh'>ﻚﻨﻣ ﺏﺮﻘﻟﺎﺑ ﺐﻋﻻ ﺪﺟﻮﻳﻻ")
			end
		else
			ESX.ShowNotification('<font color=red>لايمكن حمل شخص مسقط')
		end
		else
			piggyback.InProgress = false
			ClearPedSecondaryTask(PlayerPedId())
			DetachEntity(PlayerPedId(), true, false)
			TriggerServerEvent("Piggyback:stop",piggyback.targetSrc)
			piggyback.targetSrc = 0
		end
	end
end,false)

RegisterNetEvent("Piggyback:syncTarget")
AddEventHandler("Piggyback:syncTarget", function(targetSrc)
	local playerPed = PlayerPedId()
	local targetPed = GetPlayerPed(GetPlayerFromServerId(targetSrc))
	piggyback.InProgress = true
	ensureAnimDict(piggyback.personBeingPiggybacked.animDict)
	AttachEntityToEntity(PlayerPedId(), targetPed, 0, piggyback.personBeingPiggybacked.attachX, piggyback.personBeingPiggybacked.attachY, piggyback.personBeingPiggybacked.attachZ, 0.5, 0.5, 180, false, false, false, false, 2, false)
	piggyback.type = "beingPiggybacked"
end)

RegisterNetEvent("Piggyback:cl_stop")
AddEventHandler("Piggyback:cl_stop", function()
	piggyback.InProgress = false
	ClearPedSecondaryTask(PlayerPedId())
	DetachEntity(PlayerPedId(), true, false)
end)

Citizen.CreateThread(function()
	while true do
		if piggyback.InProgress then
			if piggyback.type == "beingPiggybacked" then
				if not IsEntityPlayingAnim(PlayerPedId(), piggyback.personBeingPiggybacked.animDict, piggyback.personBeingPiggybacked.anim, 3) then
					TaskPlayAnim(PlayerPedId(), piggyback.personBeingPiggybacked.animDict, piggyback.personBeingPiggybacked.anim, 8.0, -8.0, 100000, piggyback.personBeingPiggybacked.flag, 0, false, false, false)
				end
			elseif piggyback.type == "piggybacking" then
				if not IsEntityPlayingAnim(PlayerPedId(), piggyback.personPiggybacking.animDict, piggyback.personPiggybacking.anim, 3) then
					TaskPlayAnim(PlayerPedId(), piggyback.personPiggybacking.animDict, piggyback.personPiggybacking.anim, 8.0, -8.0, 100000, piggyback.personPiggybacking.flag, 0, false, false, false)
				end
			end
		else
			Citizen.Wait(3000)
		end
		Wait(1)
	end
end)