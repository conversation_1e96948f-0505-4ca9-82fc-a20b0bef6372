var oldNotification = []
var oldNotificationOpen = false

window.addEventListener('message', function (event) {
    switch(event.data.action) {
        case 'normal':
            Normal(event.data);
            break;
        case 'showOld':
            oldNotificationFunc();
            break;
        case 'yonel':
            for (let i = 0; i < 30; i++) {
                const data = oldNotification[i];
                if (data) {
                    if (data.alertId == parseInt(event.data.alertId)) {
                        if (data.yonel) {
                            data.yonel = data.yonel + ", " + event.data.name
                        } else {
                            data.yonel = event.data.name
                        }
                        const id = "#yonelen-"+event.data.alertId
                        $(id).css("display", "block");
                        $(id).html(data.yonel)
                        break
                    }
                } else {
                    break
                }
            }
            break;
    }
});

function Normal(data) {
    oldNotification.push(data)
    if (oldNotificationOpen) {
        htmlData(data, "Left Click and Mark The GPS, Sağ Tıklayarak Yönel", "#oldNotifi")
    } else {
        htmlData(data, "[E] لتحديد موقع البلاغ", "#notifi")
        setTimeout(function() { $("#notifi #bildirim-"+data.alertId).fadeOut(300, function() { $(this).remove(); }) }, 10000);
    }
}

function oldNotificationFunc() {
    oldNotification.reverse();
    $('#notifi').empty()
    $("#notifi").css("display", "none");
    $("#top-bar-oldNotifi").css("display", "flex");

    oldNotificationOpen = true
    for (let i = 0; i < 30; i++) {
        const data = oldNotification[i];
        if (data) {
            htmlData(data, "[Left Click] Mark On GPS, [Right Click] Heading To The Police Call", "#oldNotifi")
        } else {
            break
        }
    }
    oldNotification.reverse();
}

function htmlData(data, text, type) {
    let yonelStyle = "none"
    if (data.yonel) { yonelStyle = "block" }

    let newHtmlData = `
        <div class="notification-ic tamplate" id="bildirim-${data.alertId}" onClick="setCoords(${data.coords, data.coords})" oncontextmenu="yonel(${data.alertId})">
            <div class="yonelen" style="display:${yonelStyle};" id="yonelen-${data.alertId}">${data.yonel}</div>
            <div class="notifData">
                <div class="line-1">
                    <div class="kirmizi">Alert</div><div class="baslik" id="baslik">${data.alertName}</div>
                </div> 

                <div class="line-8">
                    <i class="fas fa-clock"></i><div class ="ikon-sagi" id="alertCoord">${data.date}</div>
                </div>

                <div class="line-2">
                    <i class="fas fa-globe-europe"></i><div class ="ikon-sagi" id="alertCoord">${data.street}</div>
                </div>

                <div class="line-3 gizle tgiann-arac-${data.alertId}">
                    <i class="fas fa-car"></i><div class ="ikon-sagi" id="arac">${data.arac}</div>
                </div>

                <div class="line-4 gizle tgiann-plate-${data.alertId}">
                    <i class="fas fa-closed-captioning"></i><div class ="ikon-sagi" id="plate">${data.plate}</div>
                </div>

                <div class="line-5 gizle tgiann-renk-${data.alertId}">
                    <i class="fas fa-palette"></i><div class ="ikon-sagi" id="renk">${data.renk}</div>
                </div>

                <div class="line-6">
                    <i class="fas fa-venus-mars"></i><div class="ikon-sagi" id="sex">${data.sex}</div>
                </div>
            </div>
            <div class="e-bas">${text}</div> 
        </div> 
    `
    if (type == "#oldNotifi") {
        $(type).append(newHtmlData);
    } else {
        $(type).prepend(newHtmlData);
    }

    if (data.arac != "yok" ) {
        $(".tgiann-arac-"+data.alertId).removeClass('gizle');
        $(".tgiann-plate-"+data.alertId).removeClass('gizle');
        $(".tgiann-renk-"+data.alertId).removeClass('gizle');
    } else {
        $(".tgiann-arac-"+data.alertId).addClass('gizle');
        $(".tgiann-plate-"+data.alertId).addClass('gizle');
        $(".tgiann-renk-"+data.alertId).addClass('gizle');
    }
}

function closeOldNotificationFunc() {
    if ( oldNotificationOpen ) {
        oldNotificationOpen = false
        $('#oldNotifi').empty()
        $("#notifi").css("display", "flex");
        $("#top-bar-oldNotifi").css("display", "none");
        $.post('https://tgiann-policealert/closeui');
    }
}

function setCoords(x, y) {
    closeOldNotificationFunc()
    $.post('https://tgiann-policealert/setCoords', JSON.stringify({x:x, y:y}));
}

function yonel(alertId) {
    $.post('https://tgiann-policealert/yonel', JSON.stringify({alertId:alertId}));
}

$(document).on('click', 'body', function(e){
    closeOldNotificationFunc()
});

$(document).on('keydown', function() {
    switch(event.keyCode) {
        case 90:
            closeOldNotificationFunc()
        break;
    }
});