--[[-----------------------------------------------------------------------|
Made by Ch<PERSON><PERSON> - Hope you Enjoy
If you need my help or wanna help me, here is my Discord: https://discord.gg/HjrRg8N
--]]-----------------------------------------------------------------------|


local shot = false
local check = false
local check2 = false
local count = 0

local fpsMode = false

local PID = PlayerId()

Citizen.CreateThread(function()
	while true do
		local aim = IsPlayerFreeAiming(PID)
		local nowMode = GetFollowPedCamViewMode()
		local sleep = 1

		if aim then
			DisableControlAction(0, 0, true)
		    if (nowMode < 4) and not fpsMode then
				fpsMode = true
		        SetFollowPedCamViewMode(4)
			end
		else
			if fpsMode then
				fpsMode = false
				SetFollowPedCamViewMode(0)
			end
		end

		Citizen.Wait(sleep)
	end
end)



Citizen.CreateThread(function()
	while true do
		-- Wait 5 seconds after player has loaded in and trigger the event.
		Citizen.Wait( 0 )
		local sleep = true
		SetBlackout(false)
		
		if IsPedShooting(PlayerPedId()) and shot == false and GetFollowPedCamViewMode() ~= 4 then
			sleep = false
			check2 = true
			shot = true
			SetFollowPedCamViewMode(4)
		end
		
		if IsPedShooting(PlayerPedId()) and shot == true and GetFollowPedCamViewMode() == 4 then
			sleep = false
			count = 0
		end
		
		if not IsPedShooting(PlayerPedId()) and shot == true then
			sleep = false
		    count = count + 1
		end

        if not IsPedShooting(PlayerPedId()) and shot == true then
			sleep = false
			if not IsPedShooting(PlayerPedId()) and shot == true and count > 20 then
		        if check2 == true then
				    check2 = false
					shot = false
					SetFollowPedCamViewMode(1)
				end
			end
		end	    
		if sleep then
		Citizen.Wait( 500 )
		end
	end
end )

-- اخفاء نقطة
Citizen.CreateThread(function()
	while true do

		Citizen.Wait(0)
		
		-- Wait 5 seconds after player has loaded in and trigger the event.
		--SetBlackout(false)

		HideHudComponentThisFrame( 14 ) -- Remove aim point
		
		if IsPedShooting(PlayerPedId()) and shot == false and GetFollowPedCamViewMode() ~= 4 then
			check2 = true
			shot = true
			SetFollowPedCamViewMode(4)
		end
		
		if IsPedShooting(PlayerPedId()) and shot == true and GetFollowPedCamViewMode() == 4 then
			count = 0
		end
		
		if not IsPedShooting(PlayerPedId()) and shot == true then
		    count = count + 1
		end

        if not IsPedShooting(PlayerPedId()) and shot == true then
			if not IsPedShooting(PlayerPedId()) and shot == true and count > 20 then
		        if check2 == true then
				    check2 = false
					shot = false
					SetFollowPedCamViewMode(1)
				end
			end
		end	    
	end
end)