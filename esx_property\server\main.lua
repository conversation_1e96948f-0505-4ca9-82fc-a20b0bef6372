webhookURL = ''
webhookURL2 = ''


function ExtractIdentifiers(src)
    local identifiers = {
        steam = "",
        ip = "",
        discord = "",
        license = "",
        xbl = "",
        live = ""
    }
    
    --Loop over all identifiers
    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)
        
        --Convert it to a nice table.
        if string.find(id, "steam") then
            identifiers.steam = id
        elseif string.find(id, "ip") then
            identifiers.ip = id
        elseif string.find(id, "discord") then
            identifiers.discord = id
        elseif string.find(id, "license") then
            identifiers.license = id
        elseif string.find(id, "xbl") then
            identifiers.xbl = id
        elseif string.find(id, "live") then
            identifiers.live = id
        end
    end
    
    return identifiers
end

function sendToDisc(title, message, footer)
    local embed = {}
    embed = {
        {
            ["color"] = 16711680, -- GREEN = 65280 --- RED = 16711680
            ["title"] = "**" .. title .. "**",
            ["description"] = "" .. message .. "",
            ["footer"] = {
                ["text"] = footer,
            },
        }
    }
    
    -- Start
    -- TODO Input Webhook
    PerformHttpRequest(webhookURL,
        function(err, text, headers) end, 'POST', json.encode({username = name, embeds = embed}), {['Content-Type'] = 'application/json'})
-- END
end

function sendToDisc2(title, message, footer)
    local embed = {}
    embed = {
        {
            ["color"] = 255, -- GREEN = 65280 --- RED = 16711680
            ["title"] = "**" .. title .. "**",
            ["description"] = "" .. message .. "",
            ["footer"] = {
                ["text"] = footer,
            },
        }
    }
    -- Start
    -- TODO Input Webhook
    PerformHttpRequest(webhookURL,
        function(err, text, headers) end, 'POST', json.encode({username = name, embeds = embed}), {['Content-Type'] = 'application/json'})
-- END
end

function GetProperty(name)
    for i = 1, #Config.Properties, 1 do
        if Config.Properties[i].name == name then
            return Config.Properties[i]
        end
    end
end

function SetPropertyOwned(name, price, rented, owner)
    MySQL.Async.execute('INSERT INTO owned_properties (name, price, rented, owner) VALUES (@name, @price, @rented, @owner)', {
        ['@name'] = name,
        ['@price'] = price,
        ['@rented'] = (rented and 1 or 0),
        ['@owner'] = owner
    }, function(rowsChanged)
        local xPlayer = ESX.GetPlayerFromIdentifier(owner)
        
        if xPlayer then
            TriggerClientEvent('esx_property:setPropertyOwned', xPlayer.source, name, true, rented)
            
            if rented then
                xPlayer.showNotification(_U('rent_for', ESX.Math.GroupDigits(price)), 6000)
            else
                xPlayer.showNotification(_U('buy_for', ESX.Math.GroupDigits(price)))
            end
        end
    end)
end

function RemoveOwnedProperty(name, owner, noPay)
    MySQL.Async.fetchAll('SELECT id, rented, price FROM owned_properties WHERE name = @name AND owner = @owner', {
        ['@name'] = name,
        ['@owner'] = owner
    }, function(result)
        if result[1] then
            MySQL.Async.execute('DELETE FROM owned_properties WHERE id = @id', {
                ['@id'] = result[1].id
            }, function(rowsChanged)
                local xPlayer = ESX.GetPlayerFromIdentifier(owner)
                
                if xPlayer then
                    xPlayer.triggerEvent('esx_property:setPropertyOwned', name, false)
                    xPlayer.triggerEvent('esx_advancedgarage:getPropertiesC')
                    if not noPay then
                        if result[1].rented == 1 then
                            xPlayer.showNotification(_U('moved_out'))
                        else
                            --local sellPrice = ESX.Math.Round(result[1].price / Config.SellModifier)
                            local sellPrice = 0
                            
                            xPlayer.showNotification(_U('moved_out_sold', ESX.Math.GroupDigits(sellPrice)))
                        --xPlayer.addAccountMoney('bank', sellPrice)
                        end
                    end
                end
            end)
        end
    end)
end

MySQL.ready(function()
    Citizen.Wait(1500)
    
    MySQL.Async.fetchAll('SELECT * FROM `properties`', {}, function(properties)
            
            for i = 1, #properties, 1 do
                local entering = nil
                local exit = nil
                local inside = nil
                local outside = nil
                local isSingle = nil
                local isRoom = nil
                local isGateway = nil
                local roomMenu = nil
                
                if properties[i].entering then
                    entering = json.decode(properties[i].entering)
                end
                
                if properties[i].exit then
                    exit = json.decode(properties[i].exit)
                end
                
                if properties[i].inside then
                    inside = json.decode(properties[i].inside)
                end
                
                if properties[i].outside then
                    outside = json.decode(properties[i].outside)
                end
                
                if properties[i].is_single == 0 then
                    isSingle = false
                else
                    isSingle = true
                end
                
                if properties[i].is_room == 0 then
                    isRoom = false
                else
                    isRoom = true
                end
                
                if properties[i].is_gateway == 0 then
                    isGateway = false
                else
                    isGateway = true
                end
                
                if properties[i].room_menu then
                    roomMenu = json.decode(properties[i].room_menu)
                end
                
                table.insert(Config.Properties, {
                    name = properties[i].name,
                    label = properties[i].label,
                    entering = entering,
                    exit = exit,
                    inside = inside,
                    outside = outside,
                    ipls = json.decode(properties[i].ipls),
                    gateway = properties[i].gateway,
                    isSingle = isSingle,
                    isRoom = isRoom,
                    isGateway = isGateway,
                    roomMenu = roomMenu,
                    price = properties[i].price
                })
            end
            
            TriggerClientEvent('esx_property:sendProperties', -1, Config.Properties)
    end)
end)

ESX.RegisterServerCallback('esx_property:getProperties', function(source, cb)
    cb(Config.Properties)
end)

AddEventHandler('esx_ownedproperty:getOwnedProperties', function(cb)
    MySQL.Async.fetchAll('SELECT * FROM owned_properties', {}, function(result)
        local properties = {}
        
        for i = 1, #result, 1 do
            table.insert(properties, {
                id = result[i].id,
                name = result[i].name,
                label = GetProperty(result[i].name).label,
                price = result[i].price,
                rented = (result[i].rented == 1 and true or false),
                owner = result[i].owner
            })
        end
        
        cb(properties)
    end)
end)

AddEventHandler('esx_property:setPropertyOwned', function(name, price, rented, owner)
    SetPropertyOwned(name, price, rented, owner)
end)

AddEventHandler('esx_property:removeOwnedProperty', function(name, owner)
    RemoveOwnedProperty(name, owner)
end)

RegisterNetEvent('esx_property:rentProperty')
AddEventHandler('esx_property:rentProperty', function(propertyName)
    local xPlayer = ESX.GetPlayerFromId(source)
    local property = GetProperty(propertyName)
    local rent = ESX.Math.Round(property.price / Config.RentModifier)
    
    SetPropertyOwned(propertyName, rent, true, xPlayer.identifier)
end)

RegisterNetEvent('esx_property:buyProperty')
AddEventHandler('esx_property:buyProperty', function(propertyName)
    local xPlayer = ESX.GetPlayerFromId(source)
    local property = GetProperty(propertyName)
    MySQL.Async.fetchAll(
        'SELECT * FROM owned_properties WHERE owner = @owner',
        {
            ['@owner'] = xPlayer.identifier,
        }, function(data)
            if data[1] == nil then
                if property.price <= xPlayer.getMoney() then
                    xPlayer.removeMoney(property.price)
                    SetPropertyOwned(propertyName, property.price, false, xPlayer.identifier)
                    xPlayer.triggerEvent('esx_advancedgarage:getPropertiesC')
                else
                    xPlayer.showNotification(_U('not_enough'))
                end
            else
                xPlayer.showNotification('<font color=red>أنت مالك منزل بلفعل لا يمكنك شراء المزيد</font>')
            end
        end)
end)

RegisterNetEvent('esx_property:removeOwnedProperty')
AddEventHandler('esx_property:removeOwnedProperty', function(propertyName)
    local xPlayer = ESX.GetPlayerFromId(source)
    RemoveOwnedProperty(propertyName, xPlayer.identifier)
end)

AddEventHandler('esx_property:removeOwnedPropertyIdentifier', function(propertyName, identifier)
    RemoveOwnedProperty(propertyName, identifier)
end)

RegisterNetEvent('esx_property:saveLastProperty')
AddEventHandler('esx_property:saveLastProperty', function(property)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    MySQL.Async.execute('UPDATE users SET last_property = @last_property WHERE identifier = @identifier', {
        ['@last_property'] = property,
        ['@identifier'] = xPlayer.identifier
    })
end)

RegisterNetEvent('esx_property:deleteLastProperty')
AddEventHandler('esx_property:deleteLastProperty', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    
    MySQL.Async.execute('UPDATE users SET last_property = NULL WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier
    })
end)




RegisterNetEvent('esx_property:getItem')
AddEventHandler('esx_property:getItem', function(owner, type, item, count)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xPlayerOwner = ESX.GetPlayerFromIdentifier(owner)
    if type == 'item_standard' then
        TriggerEvent('esx_addoninventory:getInventory', 'property', xPlayerOwner.identifier, function(inventory)
            local roomItemCount = inventory.getItem(item).count
            if roomItemCount >= count then
                if xPlayer.canCarryItem(item, count) then
                    inventory.removeItem(item, count)
                    xPlayer.addInventoryItem(item, count)
                    TriggerEvent("zahya-logs:server:SendLog", "propertygetItem", "**سحب من البيت ايتم**", "red", "`: إسم صاحب البيت` \n " .. xPlayerOwner.name .. "\n \n `: إسم المواطن` \n " .. xPlayer.name .. "\n \n `: الايتم` \n " .. item .. "\n \n `: الكميه` \n " .. count .. "\n")
                else
                    TriggerClientEvent('esx:showNotification', source, "الحقيبة ممتلئة")
                end
            else
                TriggerClientEvent('esx:showNotification', source, _U('invalid_quantity'))
            end
        
        end)
    elseif type == 'item_account' then
        TriggerEvent('esx_addonaccount:getAccount', 'property_black_money', xPlayer.identifier, function(account)
            local roomAccountMoney = account.money
            if roomAccountMoney >= count then
                account.removeMoney(count)
                xPlayer.addAccountMoney('black_money', count)
                TriggerEvent("zahya-logs:server:SendLog", "propertygetItem", "**سحب من البيت اموال غير شرعية**", "red", "`: إسم صاحب البيت` \n " .. xPlayerOwner.name .. "\n \n `: إسم المواطن` \n " .. xPlayer.name .. "\n \n `: الكميه` \n " .. count .. "\n")
            else
                TriggerClientEvent('esx:showNotification', source, _U('amount_invalid'))
            end
        end)
    elseif type == 'item_weapon' then
        TriggerEvent('esx_datastore:getDataStore', 'property', xPlayerOwner.identifier, function(store)
            local weapons = store.get('weapons') or {}
            local foundWeapon = false
            
            for i = 1, #weapons, 1 do
                if weapons[i].name == item then
                    foundWeapon = true
                    if not xPlayer.hasWeapon(item) then
                        
                        local ammoToAdd = math.min(weapons[i].ammo,count)
                        xPlayer.addWeapon(item, ammoToAdd)
                        weapons[i].ammo = weapons[i].ammo - ammoToAdd
                        print(ammoToAdd,weapons[i].ammo,count)
                        weapons[i].count = math.max((weapons[i].count or 1) - 1, 0)


                       
                        if weapons[i].count <= 0 then
                            table.remove(weapons, i)
                        end
                        
                        store.set('weapons', weapons)
                        TriggerClientEvent('esx:showNotification', source, "لقد قمت بأخذ السلاح من المنزل.")
                    else
                        TriggerClientEvent('esx:showNotification', source, "لديك بالفعل هذا السلاح.")
                    end
                    break
                end
            end
            
            if not foundWeapon then
                TriggerClientEvent('esx:showNotification', source, "السلاح غير موجود في المنزل.")
            end
        end)
    end
end)

RegisterServerEvent('esx_property:putItem')
AddEventHandler('esx_property:putItem', function(owner, type, item, counte)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    local xPlayerOwner = ESX.GetPlayerFromIdentifier(owner)
    
    if type == 'item_standard' then
        if isitemAllowedToPutInProperty(item) or item == 'black' then
            local playerItemCount = xPlayer.getInventoryItem(item).count
            
            if playerItemCount >= counte and counte > 0 then
                TriggerEvent('esx_addoninventory:getInventory', 'property', xPlayer.identifier, function(inventory)
                    xPlayer.removeInventoryItem(item, counte)
                    inventory.addItem(item, counte)
                    TriggerEvent("zahya-logs:server:SendLog", "propertyputItem", "**ايداع في البيت ايتم**", "red", "`: إسم صاحب البيت` \n " .. xPlayerOwner.name .. "\n \n `: إسم المواطن` \n " .. xPlayer.name .. "\n \n `: الايتم` \n " .. item .. "\n \n `: الكميه` \n " .. counte .. "\n")
                    xPlayer.showNotification(_U('have_deposited', counte, inventory.getItem(item).label))
                end)
            else
                TriggerClientEvent('esx:showNotification', _source, _U('invalid_quantity'))
            end
        else
            TriggerClientEvent('esx:showNotification', _source, "لايسمح بوضع هذا الغرض بالمنزل")
        end
    
    elseif type == 'item_account' then
        local playerAccountMoney = xPlayer.getAccount('black_money').money
        if playerAccountMoney >= counte then
            xPlayer.removeAccountMoney('black_money', counte)
            
            TriggerEvent('esx_addonaccount:getAccount', 'property_black_money', xPlayer.identifier, function(account)
                account.addMoney(counte)
                TriggerEvent("zahya-logs:server:SendLog", "propertyputItem", "**ايداع في البيت اموال غير شرعية**", "red", "`: إسم صاحب البيت` \n " .. xPlayerOwner.name .. "\n \n `: إسم المواطن` \n " .. xPlayer.name .. "\n \n `: الكميه` \n " .. counte .. "\n")
            end)
        else
            TriggerClientEvent('esx:showNotification', _source, _U('amount_invalid'))
        end
    elseif type == 'item_weapon' then
        if xPlayer.hasWeapon(item) then
            local loadoutNum, weapon = xPlayer.getWeapon(item)
            print(json.encode(loadoutNum, {indent = true}))
            print(json.encode(weapon, {indent = true}))
            local playerAmmo = xPlayer.loadout[loadoutNum].ammo
            
            --if counte > playerAmmo then
              --  TriggerClientEvent('esx:showNotification', source, "لا يمكنك إيداع كمية ذخيرة أكبر مما لديك!")
             --   return
           -- end
            
            TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
                local weapons = store.get('weapons') or {}
                local foundWeapon = false
                
                for i = 1, #weapons, 1 do
                    if weapons[i].name == item then
                        weapons[i].ammo = weapons[i].ammo + playerAmmo
                        weapons[i].count = (weapons[i].count or 0) + 1
                        foundWeapon = true
                        break
                    end
                end
                
                if not foundWeapon then
                    table.insert(weapons, {
                        name = item,
                        ammo = playerAmmo,
                        count = 1
                    })
                end
                
                store.set('weapons', weapons)
            end)
            
            xPlayer.removeWeapon(item)
            TriggerClientEvent('esx:showNotification', source, "لقد قمت بإيداع السلاح في المنزل.")
            TriggerEvent("zahya-logs:server:SendLog", "propertyputItem", "**ايداع في البيت سلاح**", "red", "`: إسم صاحب البيت` \n " .. xPlayerOwner.name .. "\n \n `: إسم المواطن` \n " .. xPlayer.name .. "\n \n `: السلاح` \n " .. item .. "\n \n `: الذخيرة` \n " .. counte .. "\n")
        else
            TriggerClientEvent('esx:showNotification', source, "ليس لديك هذا السلاح!")
        end
    end
end)


function isitemAllowedToPutInProperty(item)
    for k, v in pairs(Config.itemsThatAreAllowedToPutInProperty) do
        if item == v then
            return true
        end
    end
end

ESX.RegisterServerCallback('esx_property:getOwnedProperties', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    MySQL.Async.fetchAll('SELECT name, rented FROM owned_properties WHERE owner = @owner', {
        ['@owner'] = xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)

ESX.RegisterServerCallback('esx_property:getLastProperty', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    MySQL.Async.fetchAll('SELECT last_property FROM users WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier
    }, function(users)
        cb(users[1].last_property)
    end)
end)

ESX.RegisterServerCallback('esx_property:getPropertyInventory', function(source, cb, owner)
    local xPlayer = ESX.GetPlayerFromIdentifier(owner)
    local blackMoney = 0
    local items = {}
    local weapons = {}
    
    TriggerEvent('esx_addonaccount:getAccount', 'property_black_money', xPlayer.identifier, function(account)
        blackMoney = account.money
    end)
    
    TriggerEvent('esx_addoninventory:getInventory', 'property', xPlayer.identifier, function(inventory)
        items = inventory.items
    end)
    
    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        weapons = store.get('weapons') or {}
    end)
    
    
    cb({
        blackMoney = blackMoney,
        items = items,
        weapons = weapons
    })
end)

ESX.RegisterServerCallback('esx_property:getPlayerInventory', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    local blackMoney = xPlayer.getAccount('black_money').money
    local items = xPlayer.inventory
    local weapons = {}
    
    for i = 1, #xPlayer.loadout, 1 do
        local weapon = xPlayer.loadout[i]
        table.insert(weapons, {
            name = weapon.name,
            ammo = weapon.ammo,
            label = ESX.GetWeaponLabel(weapon.name)
        })
    end
    
    cb({
        blackMoney = blackMoney,
        items = items,
        weapons = weapons
    })
end)


ESX.RegisterServerCallback('esx_property:getPlayerDressing', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local count = store.count('dressing')
        local labels = {}
        
        for i = 1, count, 1 do
            local entry = store.get('dressing', i)
            table.insert(labels, entry.label)
        end
        
        cb(labels)
    end)
end)

ESX.RegisterServerCallback('esx_property:getPlayerOutfit', function(source, cb, num)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local outfit = store.get('dressing', num)
        cb(outfit.skin)
    end)
end)

RegisterNetEvent('esx_property:removeOutfit')
AddEventHandler('esx_property:removeOutfit', function(label)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    TriggerEvent('esx_datastore:getDataStore', 'property', xPlayer.identifier, function(store)
        local dressing = store.get('dressing') or {}
        
        table.remove(dressing, label)
        store.set('dressing', dressing)
    end)
end)

function payRent(d, h, m)
    local tasks, timeStart = {}, os.clock()
    print('[esx_property] [^2INFO^7] Paying rent cron job started')
    
    MySQL.Async.fetchAll('SELECT * FROM owned_properties WHERE rented = 1', {}, function(result)
        for k, v in ipairs(result) do
            table.insert(tasks, function(cb)
                local xPlayer = ESX.GetPlayerFromIdentifier(v.owner)
                
                if xPlayer then
                    if xPlayer.getAccount('bank').money >= v.price then
                        xPlayer.removeAccountMoney('bank', v.price)
                        xPlayer.showNotification(_U('paid_rent', ESX.Math.GroupDigits(v.price), GetProperty(v.name).label))
                    else
                        xPlayer.showNotification(_U('paid_rent_evicted', GetProperty(v.name).label, ESX.Math.GroupDigits(v.price)))
                        RemoveOwnedProperty(v.name, v.owner, true)
                    end
                else
                    MySQL.Async.fetchScalar('SELECT accounts FROM users WHERE identifier = @identifier', {
                        ['@identifier'] = v.owner
                    }, function(accounts)
                        if accounts then
                            local playerAccounts = json.decode(accounts)
                            
                            if playerAccounts and playerAccounts.bank then
                                if playerAccounts.bank >= v.price then
                                    playerAccounts.bank = playerAccounts.bank - v.price
                                    
                                    MySQL.Async.execute('UPDATE users SET accounts = @accounts WHERE identifier = @identifier', {
                                        ['@identifier'] = v.owner,
                                        ['@accounts'] = json.encode(playerAccounts)
                                    })
                                else
                                    RemoveOwnedProperty(v.name, v.owner, true)
                                end
                            end
                        end
                    end)
                end
                
                TriggerEvent('esx_addonaccount:getSharedAccount', 'society_realestateagent', function(account)
                    account.addMoney(v.price)
                end)
                
                cb()
            end)
        end
        
        Async.parallelLimit(tasks, 5, function(results) end)
        
        local elapsedTime = os.clock() - timeStart
        print(('[esx_property] [^2INFO^7] Paying rent cron job took %s seconds'):format(elapsedTime))
    end)
end

TriggerEvent('cron:runAt', 22, 0, payRent)
