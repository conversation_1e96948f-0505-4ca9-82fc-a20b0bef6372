if not ESX then
	TriggerEvent('esx:getSharedObjectp23Njd23byabd', function(obj) ESX = obj end)
end

-- Open ID card
RegisterServerEvent('jsfour-idcard:open')
AddEventHandler('jsfour-idcard:open', function(ID, targetID, type, data, sponseer)
	local xPlayer = ESX.GetPlayerFromId(ID)
	local identifier = xPlayer.identifier
	local _source 	 = ESX.GetPlayerFromId(targetID).source
	local show       = false
	
	MySQL.Async.fetchAll('SELECT * FROM users WHERE identifier = @identifier', {['@identifier'] = identifier},
	function (user)
		if (user[1] ~= nil) then
			MySQL.Async.fetchAll('SELECT type FROM user_licenses WHERE owner = @identifier', {['@identifier'] = identifier},
			function (licenses)
				if type ~= nil and type ~= 'job' then
					for i=1, #licenses, 1 do
						if type == 'driver' then
							if licenses[i].type == 'drive' or licenses[i].type == 'drive_bike' or licenses[i].type == 'drive_truck' then
								show = true
							end	
						elseif type =='market' then
							if licenses[i].type == 'market' then
								show = true
							end							
						elseif type =='weapon' then
							if licenses[i].type == 'weapon' then
								show = true
							end
						elseif type == 'sponser' then
							if licenses[i].type == 'sponser' then
								show = true
							end
						end
					end
				else
					show = true
				end
				user[1].jobname = ESX.GetPlayerFromId(ID).job.grade_label
				if show then
					if type == "sponser" then
						TriggerClientEvent('jsfour-idcard:open', _source, data, 'sponser', ID)
					else
						if user[1].phone_number == nil then
							-- charinfo = json.decode(user[1].charinfo)
							-- user[1].phone_number = charinfo.phone + **********
							local array = {
								user = user,
								licenses = licenses,
								jobname2 = xPlayer.job.name,
								job = xPlayer.job.label,
								gradelabel = xPlayer.job.grade_label
							}
							TriggerClientEvent('jsfour-idcard:open', _source, array, type, ID)
						else
							-- user[1].phone_number = user[1].phone_number + **********
							local array = {
								user = user,
								licenses = licenses,
								jobname2 = xPlayer.job.name,
								job = xPlayer.job.label,
								gradelabel = xPlayer.job.grade_label
							}
							TriggerClientEvent('jsfour-idcard:open', _source, array, type, ID)
						end
					end
				else
					TriggerClientEvent('pNotify:SendNotification', _source, {
						text = '<center><b style="color:#ea1f1f;font-size:20px;"> لاتمتلك هذا النوع من الرخص ', 
						type = "info", 
						timeout = 10000, 
						layout = "centerLeft"
					})
				end
			end)
		end
	end)
end)

RegisterServerEvent("wsh:jsfour-idcard:show")
AddEventHandler("wsh:jsfour-idcard:show", function(myid, hisid, type, data, type2)
	TriggerClientEvent('jsfour-idcard:open', hisid, data, type)
end)
