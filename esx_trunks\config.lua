Config	=	{}

Config.CheckOwnership = true -- If true, Only owner of vehicle can store items in trunk.
Config.AllowPolice = true -- If true, police will be able to search players' trunks.


Config.Locale   = 'en'

 -- Limit, unit can be whatever you want. Originally grams (as average people can hold 25kg)
Config.Limit = 150

-- Default weight for an item:
	-- weight == 0 : The item do not affect character inventory weight
	-- weight > 0 : The item cost place on inventory
	-- weight < 0 : The item add place on inventory. Smart people will love it.
Config.DefaultWeight = 100

Config.localWeight = {

    medikit     = 500, --limit : 15
	xanax 		= 250, --limit : 5
	
	bread 		= 250, --limit : 20
	breadbox 	= 5000, --20   
	
	water 		= 250, --limit : 20
	waterbox 	= 5000, --20 
	
	bandage 	= 250 , --limit : 10
	
		
	cigarett 	 = 50, --limit : 20
	cigarettbox = 1000, --20 
	
	lighter  	= 200, --limit : 1
	lighterbox = 800, --4
	
	--المعادن
	m_tool		= 5700, --limit : 7
	minerToolbox 	= 39900, --7 
	stone 			= 5700,
	washed_stone 	= 5700,
	iron 			= 336,
    copper 			= 336,	
    gold 			= 336,	 
    diamond 		= 500, 
	
	--النفط
	f_tool		= 1000, --limit : 48
	f_toolbox 	= 32000, --48  
	petrol 			= 1000,
	petrol_raffin 	= 2000,
    essence 		= 1000,
	
	--الغاز
    gazbottle 		= 1000,
	
	--الدواجن
	s_tool     = 2000, --limit : 20
	slaughtererToolbox = 40000, --20
	alive_chicken 		= 2000,
    slaughtered_chicken = 2000,
    packaged_chicken 	= 400,
	
	--الاخشاب
	l_tool		= 2000, --limit : 20
	lumberjackToolbox 	= 40000, --20
	wood 				= 2000,
    cutted_wood 		= 2000,
	packaged_plank 		= 400,
	
	--اقمشة
	t_tool		= 1000, --limit : 40
	tailorToolbox 	= 40000, --40
	wool 			= 1000,
	fabric 			= 500,
    clothe 			= 500,
	
	--سمك
	
	fish 			= 1000,
	shark 			= 10000,
	turtle 			= 4000,
	
	fishingrod 		= 500,
	fishingrodbox 	= 2000,
	
	turtlebait 		= 250,
	turtlebaitbox 	= 2500,
	
	fishbait 		= 250,
	fishbaitbox 	= 7500,
	grape_juice = 800,
	grape = 400,
	weed 		= 400,
	weed_pooch = 2000,
    coke 		= 400,
	coke_pooch = 2000,
	meth 		= 400,
	meth_pooch 	= 2000,
	opium 		= 400,
	opium_pooch = 2000,
		
    flashlight 	= 500,
	grip 		= 250,
	lowrider 	= 250,
	yusuf 		= 250,
	silent 		= 500,
	scope 		= 250,
	advanced_scope = 250,
	extended_magazine = 500,	 
    very_extended_magazine = 1000,
	
	clip 		= 2000, --limit : 2
	clip_box 	= 16000, --8 
	
	bulletproof = 1000,	--limit : 1
	bulletproof_box = 4000, --4
	
	oxygen_mask = 2000, --limit : 2
	oxygen_mask_box = 4000, --8
	
	drill 		= 10000, --limit : 1
	drill_box 	= 40000, --4  
	
	mask 		= 500, --limit : 1
	mask_box 	= 2000, --4
	
	headbag 	= 1000,  --limit : 1
	headbag_box = 4000,  --4
	
	jumelles = 3000, --limit : 1
	jumelles_box = 12000, --4
	
	speedcamera_detector = 2000, --limit : 1
	speedcamera_detector_box = 8000, --4 
	
	blowpipe 	= 1000,
	carokit 	= 1000,
    carotool 	= 1000,
	fixtool 	= 1000, 
	fixkit 		= 1000, --غدة تصليح
	
    raisin 		= 400, --grape عنب
	jus_raisin 	= 800, --grape juice عصير عنب
	vine 		= 800, --wine زجاجة خمر
	grand_cru 	= 4000, --vintage wine زجاجة خمر فاخر
	
	stock_org 	= 20000, --بضاعة منظمة
	
	radio = 2000,
	radio_box = 10000,
	
	bag = 1000,
	bag_box = 4000,
	
	dia_box =500,
	gold_bar =1000,
	lockpick =500,
	laptop_h_box =20000,
	laptop_h =5000,
	keycard =250,
	thermal_charge =5000,
	thermal_charge_box =20000,
	
	sim_card = 250,
	sim_card_box = 5000,
	
    WEAPON_KNIFE = 5000,
    WEAPON_NIGHTSTICK = 5000,
	WEAPON_HAMMER = 5000,
	WEAPON_BAT = 5000,
    WEAPON_GOLFCLUB = 5000,
	WEAPON_CROWBAR = 5000,
	WEAPON_PISTOL = 20000,
	WEAPON_COMBATPISTOL = 20000,
	WEAPON_APPISTOL = 100000,
	WEAPON_PISTOL50 = 20000,
	WEAPON_MICROSMG = 20000,
	WEAPON_SMG = 100000,
	WEAPON_ASSAULTSMG = 100000,
	WEAPON_ASSAULTRIFLE = 100000,
	WEAPON_CARBINERIFLE = 20000,
	WEAPON_ADVANCEDRIFLE = 20000,
	WEAPON_MG = 100000,
	WEAPON_COMBATMG = 20000,
	WEAPON_PUMPSHOTGUN = 20000,
	WEAPON_SAWNOFFSHOTGUN = 100000,
	WEAPON_ASSAULTSHOTGUN = 100000,
	WEAPON_BULLPUPSHOTGUN = 100000,
	WEAPON_STUNGUN = 10000,
	WEAPON_SNIPERRIFLE = 35000,
	WEAPON_HEAVYSNIPER = 40000,
	WEAPON_REMOTESNIPER = 100000,
	WEAPON_GRENADELAUNCHER = 100000,
	WEAPON_RPG = 100000,
	WEAPON_STINGER = 100000,
	WEAPON_MINIGUN = 100000,
	WEAPON_GRENADE = 100000,
	WEAPON_STICKYBOMB = 100000,
	WEAPON_SMOKEGRENADE = 35000,
	WEAPON_BZGAS = 10000,
	WEAPON_MOLOTOV = 10000,
	WEAPON_FIREEXTINGUISHER = 5000,
	WEAPON_PETROLCAN = 15000,
	WEAPON_DIGISCANNER = 100000,
	WEAPON_BALL = 1000,
	WEAPON_SNSPISTOL = 20000,
	WEAPON_BOTTLE = 1000,
	WEAPON_GUSENBERG = 100000,
	WEAPON_SPECIALCARBINE = 20000,
	WEAPON_HEAVYPISTOL = 20000,
	WEAPON_BULLPUPRIFLE = 100000,
	WEAPON_DAGGER = 100000,
	WEAPON_VINTAGEPISTOL = 20000,
	WEAPON_FIREWORK = 35000,
	WEAPON_MUSKET = 100000,
	WEAPON_HEAVYSHOTGUN = 100000,
	WEAPON_MARKSMANRIFLE = 100000,
	WEAPON_HOMINGLAUNCHER = 100000,
	WEAPON_PROXMINE = 100000,
	WEAPON_SNOWBALL = 1000,
	WEAPON_FLAREGUN = 3000,
	WEAPON_GARBAGEBAG = 100000,
	WEAPON_HANDCUFFS = 100000,
	WEAPON_COMBATPDW = 100000,
	WEAPON_MARKSMANPISTOL = 3000,
	WEAPON_KNUCKLE = 3000,
	WEAPON_HATCHET = 3000,
	WEAPON_RAILGUN = 100000,
	WEAPON_MACHETE = 3000,
	WEAPON_MACHINEPISTOL = 100000,
	WEAPON_SWITCHBLADE = 3000,
	WEAPON_REVOLVER = 20000,
	WEAPON_DBSHOTGUN = 100000,
	WEAPON_COMPACTRIFLE = 20000,
	WEAPON_AUTOSHOTGUN = 100000,
	WEAPON_BATTLEAXE = 3000,
	WEAPON_COMPACTLAUNCHER = 100000,
	WEAPON_MINISMG = 20000,
	WEAPON_PIPEBOMB = 100000,
	WEAPON_POOLCUE = 5000,
	WEAPON_WRENCH = 5000,
	WEAPON_FLASHLIGHT = 5000,
	GADGET_NIGHTVISION = 5000,
	GADGET_PARACHUTE = 20000,
	WEAPON_FLARE = 3000,
	WEAPON_SNSPISTOL_MK2 = 100000,
	WEAPON_REVOLVER_MK2 = 100000,
	WEAPON_DOUBLEACTION = 20000,
	WEAPON_SPECIALCARBINE_MK2 = 100000,
	WEAPON_BULLPUPRIFLE_MK2 = 100000,
	WEAPON_PUMPSHOTGUN_MK2 = 100000,
	WEAPON_MARKSMANRIFLE_MK2 = 100000,
	WEAPON_ASSAULTRIFLE_MK2 = 100000,
	WEAPON_CARBINERIFLE_MK2 = 100000,
	WEAPON_COMBATMG_MK2 = 100000,
	WEAPON_HEAVYSNIPER_MK2 = 100000,
	WEAPON_PISTOL_MK2 = 100000,
	WEAPON_SMG_MK2 = 100000,
}

Config.VehicleLimit = {
    [0] = 15000, --Compact
    [1] = 30000, --Sedan
    [2] = 80000, --SUV
    [3] = 20000, --Coupes
    [4] = 25000, --Muscle
    [5] = 10000, --Sports Classics
    [6] = 15000, --Sports
    [7] = 15000, --Super
    [8] = 5000, --Motorcycles
    [9] = 40000, --Off-road
    [10] = 250000, --Industrial
    [11] = 350000, --Utility
    [12] = 40000, --Vans
    [13] = 0, --Cycles
    [14] = 70000, --Boats
    [15] = 20000, --Helicopters
    [16] = 450000, --Planes
    [17] = 40000, --Service
    [18] = 40000, --Emergency
    [19] = 0, --Military
    [20] = 40000, --Commercial
    [21] = 40000 --Trains

}

Config.VehicleModel = {
	--  ['taskss'] = 40000,
	['Jetmax'] = 120000,
	['speedo5'] = 40000,
	['brick'] = 120000,
	taskss = 40000,
	
	cb90 = 40000,
	Suntrap = 120000,
	['6rp124'] = 60000,
	['6rp88'] = 80000,
	['6rp90'] = 60000,
	['6rp106'] = 80000,
	['6RP115'] = 80000,
	['6RP43'] = 80000,

	gmc1986 = 120000,
	
	

	


	


	
	

    ------  ------- 
	
	youga3 = 40000,
	UTILTRUC = 100000,
	REBEL02 = 60000,
	nissantitan = 60000,
	burrito3 = 40000,
	burrito2 = 40000,
	burrito = 40000,
	gruppe1 = 40000,
	gruppe3 = 40000,
	gruppe17 = 40000,
	gruppe2 = 40000,
	gruppe16 = 40000,
	gruppe15 = 40000,
	gruppe18 = 40000,
	gruppe6 = 40000,
	polkuw11 = 40000,
	polkuw21 = 40000,
	polkuw13 = 40000,
	polkuw22 = 40000,
	polkuw00 = 40000,
	polkuw66 = 40000,
	polkuw77 = 40000,
	polkuw88 = 40000,
	polkuw35 = 40000,
	polkuw33 = 40000,
	polkuw06 = 40000,
	polkuw77 = 40000,
	polkuw30 = 40000,
	polkuw31 = 40000,
	polkuw32 = 40000,
	polkuw34 = 40000,
	polkuw02 = 40000,
	polkuw16 = 40000,
	polkuw56 = 40000,
	polkuw18 = 40000,
	unmarked11 = 40000,
	unmarked15 = 40000,
	unmarked17 = 40000,
	unmarked13 = 40000,
	unmarked14 = 40000,
	unmarked10 = 40000,
	unmarked8 = 40000,
	pol3 = 40000,
	pol5 = 40000,
	polchall70 = 40000,
	riot = 40000,
	riot4 = 40000,
	sspres = 40000,
	panto99 = 40000,
	FBI = 40000,
	FIBjackal = 40000,
	C10 = 40000,
	["65C10"] = 40000,
	f100 = 40000,
	hilux1 = 40000,
	ems3 = 40000,
	ems4 = 40000,
	ems5 = 40000,
	ems6 = 40000,
	ems7 = 40000,
	ems8 = 40000,
	ems9 = 100000,
	['66fastback'] = 40000,
	stafford = 40000,
	beetle74 = 40000,
	f620 = 40000,
	felon = 40000,
	felon2 = 40000,
	bcgeneral = 40000,
	jackal = 40000,
	oracle2 = 40000,
	sentinel = 40000,
	windsor = 40000,
	windsor2 = 40000,
	Zion = 40000,
	Zion2 = 40000,
	cherokee1 = 40000,
	bfinjection = 40000,
	fjcruiser = 40000,
	freecrawler = 40000,
	patrolsafari = 40000,
	jeep2012 = 40000,
	['750li'] = 40000,
	['96impala'] = 40000,
	asea = 40000,
	benzc32 = 40000,
	benzs600 = 40000,
	bmwe38 = 40000,
	bmwe65 = 40000,
	camry55 = 40000,
	caprice13 = 40000,
	lex350 = 40000,
	lex500 = 40000,
	glendale = 40000,
	nisaltima = 40000,
	premier = 40000,
	primo2 = 40000,
	schafter3 = 40000,
	regina = 40000,
	stanier = 40000,
	tailgater = 40000,
	warrener = 40000,
	rmode63s = 40000,
	['4444'] = 40000,
	gle53 = 40000,
	bentayga17 = 40000,
	bronco80 = 40000,
	CAVCADE = 120000,
	cayenne = 40000,
	evoque = 40000,
	G65 = 40000,
	granger = 40000,
	h6 = 40000,
	lex570 = 40000,
	mesa = 40000,
	patriot = 40000,
	r50 = 40000,
	srt8 = 40000,
	suburban = 40000,
	tahoe = 40000,
	toyotaland = 40000,
	vxr = 40000,
	x6m = 40000,
	contender = 40000,
	brutus = 40000,
	test = 40000,

	-- حمولة 5 كجم --
	bmx = 5000,
	cruiser = 5000,
	fixter = 5000,
	scorcher = 5000,
	tribike2 = 5000,
	tribike3 = 5000,
	test = 5000,
	test = 5000,

	-- حمولة 10 كجم --
	yzfr6 = 10000,
	hcbr17 = 10000,
	gsx1000 = 10000,
	sovereign = 10000,
	shotaro = 10000,
	sanctus = 10000,
	akuma = 10000,
	avarus = 10000,
	bagger = 10000,
	bati = 10000,
	bati2 = 10000,
	bf400 = 10000,
	carbonrs = 10000,
	chimera = 10000,
	daemon = 10000,
	daemon2 = 10000,
	defiler = 10000,
	double = 10000,
	enduro = 10000,
	esskey = 10000,
	faggio = 10000,
	faggio2 = 10000,
	gargoyle = 10000,
	hakuchou2 = 10000,
	hexer = 10000,
	innovation = 10000,
	manchez = 10000,
	nemesis = 10000,
	nightblade = 10000,
	pcj = 10000,
	brawler = 10000,
	dune = 10000,
	trophytruck = 10000,
	trophytruck2 = 10000,
	test = 10000,
	test = 10000,
	test = 10000,
	test = 10000,

	-- حمولة 20 كجم --
	buccaneer2 = 20000,
	chino2 = 20000,
	faction2 = 20000,
	faction3 = 20000,
	sabregt2 = 20000,
	slamvan = 20000,
	voodoo = 20000,
	yosemite = 20000,
	baja = 20000,
	bcbansheed = 20000,
	bifta = 20000,
	bcscrambler = 10000,
	blazer = 20000,
	blazer3 = 20000,
	blazer4 = 20000,
	can = 20000,
	blista = 20000,
	blista2 = 20000,
	brioso = 20000,
	issi2 = 20000,
	panto = 20000,
	prairie = 20000,
	trans69 = 20000,
	vigero = 20000,
	['442'] = 20000,
	amcj = 20000,
	blade = 20000,
	buccaneer = 20000,
	chino2 = 20000,
	coquette3 = 20000,
	dukes = 20000,
	faction = 20000,
	gauntlet = 20000,
	hermes = 20000,
	hotknife = 20000,
	hustler = 20000,
	nightshade = 20000,
	phoenix = 20000,
	['16charger'] = 40000,
	['2019M5'] = 40000,
	['2020ss'] = 40000,
	['911r'] = 40000,
	ast = 40000,
	benzsl63 = 40000,
	jrl1 = 40000,
	jrl2 = 40000,
	jrl3 = 40000,
	jrl4 = 40000,
	jrl5 = 40000,
	jrl6 = 40000,
	jrl7 = 40000,
	jrl8 = 40000,
	jrl9 = 40000,
	jrl10 = 40000,
	jrl11 = 40000,
	jrl12 = 40000,
	jrl13 = 40000,
	jrl14 = 40000,
	jrl15 = 40000,
	jrl16 = 40000,
	jrl17 = 40000,
	jrl18 = 40000,
	jrl19 = 40000,
	jrl20 = 40000,
	jrl21 = 120000,
	jrl22 = 40000,
	jrl23 = 40000,
	jrl24 = 40000,
	jrl25 = 40000,
	jrl26 = 40000,
	jrl27 = 40000,
	jrl28 = 40000,
	jrl29 = 80000,
	jrl30 = 120000,
	jrl31 = 40000,
	jrl32 = 40000,
	jrl33 = 40000,
	jrl34 = 40000,
	jrl35 = 40000,
	jrl36 = 40000,
	jrl37 = 40000,
	jrl38 = 40000,
	jrl39 = 40000,
	jrl40 = 40000,
	jrl41 = 60000,
	jrl42 = 60000,
	jrl43 = 40000,
	jrl44 = 40000,
	jrl45 = 40000,
	jrl46 = 40000,
	jrl47 = 60000,
	jrl48 = 40000,
	jrl49 = 40000,
	jrl51 = 40000,
	jrl52 = 40000,
	jrl53 = 40000,
	jrl54 = 40000,
	jrl55 = 40000,
	jrl56 = 40000,
	jrl57 = 40000,
	jrl58 = 140000,
	jrl59 = 40000,
	jrl60 = 40000,
	c6z06 = 20000,
	c7z06 = 20000,
	c8 = 20000,
	comet5 = 20000,
	czr1 = 20000,
	furoregt = 20000,
	game718 = 20000,
	gamea45 = 20000,
	rmode63s = 40000,
	kuruma = 40000,
	inf = 20000,
	m2 = 20000,
	m3e46 = 20000,
	m3e92 = 20000,
	neon = 20000,
	ninef = 40000,
	p7 = 20000,
	p993t = 20000,
	rm3e36 = 20000,
	rmodbmwi8 = 20000,
	rmodfordgt = 20000,
	rmodgtr = 20000,
	rmodm4 = 20000,
	gtr = 20000,
	rs6 = 20000,
	S63W222 = 20000,
	shelbygt500 = 20000,
	stiwrc = 20000,
	supra2 = 20000,
	z48 = 20000,
	zl12017 = 20000,
	terminator = 20000,
	btype = 20000,
	btype2 = 20000,
	btype3 = 20000,
	DODO = 350000,
	cavalcade = 40000,
	CAVCADE = 40000,
	c10custom = 20000,
	caprice89 = 20000,
	casco = 20000,
	coquette2 = 20000,
	firebird = 20000,
	gt500 = 20000,
	STINGERG = 20000,
	ROOSEVELT = 20000,
	ROOSEVELT2 = 20000,
	ztype = 20000,
	test = 20000,
	test = 20000,
	test = 20000,
	test = 20000,
	test = 20000,
	test = 20000,

	-- حمولة 60 كجم --
	ddsn15 = 60000,
	landv62 = 60000,
	sierra88 = 60000,
	silv1560 = 60000,
	silv5860 = 60000,
	gmc1500 = 60000,
	landv6 = 60000,
	silv20 = 60000,
	f15078 = 60000,
	f1501 = 60000,
	czr2 = 60000,
	ddsn19 = 60000,
	bc21tahoe = 60000,
	outlaw = 60000,
	['650s'] = 60000,
	['911rwb'] = 60000,
	c7r = 60000,
	c8r = 60000,
	dvgtsr = 60000,
	m3gtr = 60000,
	m8gte = 60000,
	majimalm = 60000,
	['570s'] = 60000,
	countach = 60000,
	eb110 = 60000,
	aimgainnsx = 60000,
	gamef430s = 60000,
	apolloie = 60000,
	it18 = 60000,
	mp412c = 60000,
	kamacho = 60000,
	landv6 = 60000,
	nissantitan17 = 60000,
	rebel2 = 40000,
	rancherxl = 40000,
	riata = 60000,
	sandking = 60000,
	toyotahilux = 60000,
	test = 60000,
	test = 60000,

	-- حمولة 80 كجم --
	f25020 = 80000,
	nissanditsun = 80000,
	gmc9080 = 80000,
	silv2080 = 80000,
	silv6580 = 80000,
	silv86 = 80000,
	ram2500 = 80000,
	["86k30"] = 80000,
	sierra06 = 80000,
	denali18 = 80000,
	sdgmc = 80000,
	gmcat42 = 80000,
	['1500dj'] = 80000,
	['19tundra'] = 80000,
	['20f250'] = 80000,
	['20trailboss'] = 80000,
	czr2 = 80000,
	f150 = 80000,
	gmcat4 = 80000,
	silverado = 80000,
	sadler = 80000,
	test = 80000,

	-- حمولة 120 كجم --
	ford20120 = 120000,
	g63amg6x6 = 120000,
	patriot2 = 120000,
	stretch = 120000,
	['2018s650p'] = 120000,
	ben17 = 100000,
	rrphantom = 100000,
	cullinanbb = 100000,
	['20denalihd'] = 120000,
	['20silv3500work'] = 120000,
	['20silv3500w'] = 120000,
	bobcatxl = 120000,
	denalihd = 120000,
	silv2500hd = 120000,
	transformer = 120000,
	['10ram'] = 120000,
	['20denalihdg'] = 120000,
	['20silv3500'] = 120000,
	test = 120000,
	["18f350d"] = 80000,
	bad250 = 120000,
	bc20kodiak = 120000,
	bcal450 = 120000,
	["3500sd"] = 120000,
	ram10 = 120000,
	sprinter211 = 120000,
	e15082 = 120000,
	youga = 120000,
	speedo = 120000,
	f350d18 = 120000,
	g63amg6x6 = 120000,
	silvr20120 = 120000,
	type262 = 120000,
	moonbeam2 = 120000,
	
	gburrito = 120000,
	gburrito2 = 120000,
	paradise = 120000,
	rumpo = 120000,
	rumpo3 = 120000,
	surfer = 120000,
	surfer2 = 120000,
	youga = 120000,
	youga2 = 120000,
	
	-- حمولة 160 كجم --
	bcf650 = 160000,
	['relax933'] = 120000,
	['18ram'] = 160000,
	brokenet = 160000,
	['80silverado'] = 160000,
	['00excursion'] = 160000,
	['19ram'] = 160000,
	bc20hd = 160000,
	bc5506d = 160000,
	bcal450 = 160000,
	bc052500 = 160000,
	bc01sierrah = 160000,
	bc06bagged = 160000,
	bcbaggedram = 160000,
	test = 160000,
	test = 160000,

	-- حمولة 250 كجم --
	moonbeam2 = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,


	-- حمولة 350 كجم --
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	dnh = 400000,


	-- حمولة 520 كجم --
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,

	-- حمولة 550 كجم
	Dump = 550000,

	-- حمولات مركبات سبورت--
	fmb30213 = 20000,
	amggt63s = 20000,
	challenger16 = 20000,
	-- حمولات مركبات سيدان--
	a6 = 20000,
	avalon = 20000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات سوبر--
	c7 = 20000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات صالون--
	benzs600 = 40000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات فخمة--
	ben17 = 20000,
	bfs14 = 20000,
	binkshf = 20000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات كبير--
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات مركبات كلاسيكي--
	boss302 = 30000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	test = 120000,
	-- حمولات دراجات نارية--

	seashark = 20000,
	gmcyd = 40000,
	rrst = 40000,
	x5e53 = 40000,
	gmt900escalade = 30000,
	urus = 30000,
	-- حمولة
	contender = 40000,
	amarok = 60000,
	denalihd = 120000,
	hd193500 = 120000,
	sandkingswb = 60000,
	d863500 = 120000,
	silv203500 = 120000,
	rumpo3 = 120000,
	e15082 = 160000,
	d00f350d = 160000,
	raptor2017 = 160000,
	silverado99 = 160000,
	silv20 = 80000,
	f150 = 80000,
	trailboss20 = 80000,
	f25020 = 80000,
	jrl10 = 40000,
	--معرض الشاحنات
	unimog = 250000,
	vnl780 = 40000,
	phantom = 40000,
	ct660 = 40000,
	scaniar730 = 40000,
	trans_mbenzarocs = 40000,
	ct660dump = 350000,
		
	tiptruck = 350000,
	
	dumptr = 350000,
	mule2 = 250000,
	botdumptr = 250000,
	benson = 250000,
	tourbus = 40000,
	rubble = 250000,
	boxville = 250000,
	--معرض الشاحنات END
	seashark = 20000,


	sr650fly = 200000,
	toro = 70000,
	MARQUIS = 150000,
	predator = 30000,
	tug = 20000,
	w900 = 60000,

TrailerLogs = 350000,
TrailerLogs2 = 350000,
Dump = 550000,
TrailerLarge = 550000,
Trailers3 = 250000,
Trailers2 = 350000,
TVTrailer = 350000,
Tanker = 250000,
Tanker2 = 350000,
DockTrailer = 350000,
Burrito = 120000,
Rumpo3 = 120000,
Surfer2 = 120000,
Flatbed = 120000,
Bison = 120000,
Biff = 250000,
Mule = 120000,
Packer = 40000,
foxanthem1 = 40000,
Actros = 40000,
Hauler = 40000,
POUNDER = 350000,
Guardian = 120000, --مزاد
--
Tractor2 = 70000,
GRAINTRAILE = 120000,
BaleTrailer = 250000,
oiltanker = 250000,
bus = 250000,

Dinghy2 = 40000,

DINGHY = 40000,
DUSTER = 250000,
microlight = 80000,
POLMAV = 250000,
SVOLITO = 250000,
polmav2 = 350000,
SWIFT2 = 350000,
BUZZARD2 = 250000,
Havok = 120000,
towtruck3 = 120000,
['Dinghy3'] = 40000,
Dinghy4 = 60000,
SPEEDER = 250000, --مزاد
Speeder2 = 350000,-- مزاد
Marquis = 350000,-- مزاد
Toro = 250000,-- مزاد
Squalo = 80000,
Tug = 550000,-- مزاد

Burrito2 = 40000,
nissanditsu = 40000,
fordtanker = 40000,
dinghy2 = 40000,
} 

Config.VehiclePlate = {
	taxi        = "TAXI",
	cop         = "LSPD",
	ambulance   = "EMS0",
	mecano	    = "MECA",
}

-- DEMO: لا تحذف شي الله يرحم والديك
Config.blacklisted_model = {

}