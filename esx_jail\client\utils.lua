local Teleports = {
	--[[
		["Prison Work"] = { 
			["x"] = 992.51770019531, 
			["y"] = -3097.8413085938, 
			["z"] = -38.995861053467, 
			["h"] = 81.15771484375, 
			["goal"] = { 
				"Jail" 
			} 
		},
		--]]
		
		["إستراحة السجن"] = { 
			["x"] = 1724.94, 
			["y"] = 2582.86, 
			["z"] = 45.63, 
			["h"] = 270.8, 
			["goal"] = { 
				"Jail" 
			} 
		},
		
		
		["Boiling Broke"] = {  --vector4(1849.74, 2586.379, 45.67205, 273.0874)
			["x"] = 1849.74, 
			["y"] = 2586.379, 
			["z"] = 45.67205, 
			["h"] = 273.0874, 
			["goal"] = { 
				"Security" 
			} 
		},
		
	
		["Jail"] = { 
			["x"] = 1736.7, 
			["y"] = 2622.97, 
			["z"] = 45.96, 
			["h"] = 0.11, 
			["goal"] = { 
				"إستراحة السجن", 
				"خارج السجن"
				--"Visitor" 
			} 
		},
		
		["داخل السجن"] = { 
			["x"] = 1734.09, 
			["y"] = 2623.25, 
			["z"] = 45.96, 
			["h"] = 0.11, 
			["goal"] = { 
				"خارج السجن", 
				"داخل السجن"
				--"Security"
				--"Visitor" 
			} 
		},
	
		["خارج السجن"] = { 
			["x"] = 1846.04,
			["y"] = 2585.87, 
			["z"] = 45.67, 
			["h"] = 275.06, 
			["goal"] = { 
				"داخل السجن",
				"خارج السجن"
				--"Boiling Broke"
			} 
		},
	
		["Visitor"] = {
			["x"] = 1699.7196044922, 
			["y"] = 2574.5314941406, 
			["z"] = -69.403930664063, 
			["h"] = 169.65020751953, 
			["goal"] = { 
				"Jail" 
			} 
		}
	}

RegisterCommand("jailmenu", function(source, args)

	if PlayerData.job.name == "police" or PlayerData.job.name == "agent" or PlayerData.job.name == "admin" then
		OpenJailMenu()
	else
		ESX.ShowNotification("غير مصرح لك")
	end
end)

function LoadAnim(animDict)
	RequestAnimDict(animDict)

	while not HasAnimDictLoaded(animDict) do
		Citizen.Wait(10)
	end
end

function LoadModel(model)
	RequestModel(model)

	while not HasModelLoaded(model) do
		Citizen.Wait(10)
	end
end

function HideHUDThisFrame()
	HideHelpTextThisFrame()
	HideHudAndRadarThisFrame()
	HideHudComponentThisFrame(1)
	HideHudComponentThisFrame(2)
	HideHudComponentThisFrame(3)
	HideHudComponentThisFrame(4)
	HideHudComponentThisFrame(6)
	HideHudComponentThisFrame(7)
	HideHudComponentThisFrame(8)
	HideHudComponentThisFrame(9)
	HideHudComponentThisFrame(13)
	HideHudComponentThisFrame(11)
	HideHudComponentThisFrame(12)
	HideHudComponentThisFrame(15)
	HideHudComponentThisFrame(18)
	HideHudComponentThisFrame(19)
end

function Cutscene()
	DoScreenFadeOut(100)

	Citizen.Wait(250)

	local Male = GetHashKey("mp_m_freemode_01")

	TriggerEvent('skinchanger:getSkin', function(skin)
		if GetHashKey(GetEntityModel(PlayerPedId())) == Male then
			local clothesSkin = {
				['tshirt_1'] = 15, ['tshirt_2'] = 0,
				['torso_1'] = 273, ['torso_2'] = 0,
				['arms'] = 0,
				['pants_1'] = 27, ['pants_2'] = 2,
				['shoes_1'] = 6, ['shoes_2'] = 0,
			}
			TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)

		else
			local clothesSkin = {
				['tshirt_1'] = 15, ['tshirt_2'] = 0,
				['torso_1'] = 273, ['torso_2'] = 0,
				['arms'] = 0,
				['pants_1'] = 27, ['pants_2'] = 2,
				['shoes_1'] = 6, ['shoes_2'] = 0,
			}
			TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)
		end
	end)

	LoadModel(-1320879687)

	local PolicePosition = Config.Cutscene["PolicePosition"]
	local Police = CreatePed(5, -1320879687, PolicePosition["x"], PolicePosition["y"], PolicePosition["z"], PolicePosition["h"], false)
	TaskStartScenarioInPlace(Police, "WORLD_HUMAN_PAPARAZZI", 0, false)

	local PlayerPosition = Config.Cutscene["PhotoPosition"]
	local PlayerPed = PlayerPedId()
	SetEntityCoords(PlayerPed, PlayerPosition["x"], PlayerPosition["y"], PlayerPosition["z"] - 1)
	SetEntityHeading(PlayerPed, PlayerPosition["h"])
	FreezeEntityPosition(PlayerPed, true)

	Cam()

	Citizen.Wait(1000)

	DoScreenFadeIn(100)

	Citizen.Wait(10000)

	DoScreenFadeOut(250)
	local jailnumber = math.random(1, 16)
	local JailPosition = Config.JailPositions[jailnumber]
	SetEntityCoords(PlayerPed, JailPosition.x, JailPosition.y, JailPosition.z)
	SetEntityHeading(PlayerPed, JailPosition.w)
	DeleteEntity(Police)
	SetModelAsNoLongerNeeded(-1320879687)

	Citizen.Wait(1000)

	DoScreenFadeIn(250)

	TriggerServerEvent("InteractSound_SV:PlayOnSource", "cell", 0.3)

	RenderScriptCams(false,  false,  0,  true,  true)
	FreezeEntityPosition(PlayerPed, false)
	DestroyCam(Config.Cutscene["CameraPos"]["cameraId"])

	InJail()
end

function Cam()
	local CamOptions = Config.Cutscene["CameraPos"]

	CamOptions["cameraId"] = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)

    SetCamCoord(CamOptions["cameraId"], CamOptions["x"], CamOptions["y"], CamOptions["z"])
	SetCamRot(CamOptions["cameraId"], CamOptions["rotationX"], CamOptions["rotationY"], CamOptions["rotationZ"])

	RenderScriptCams(true, false, 0, true, true)
end

function TeleportPlayer(pos)

	local Values = pos

	if #Values["goal"] > 1 then

		local elements = {}

		for i, v in pairs(Values["goal"]) do
			table.insert(elements, { label = v, value = v })
		end

		ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'teleport_jail',
			{
				title    = "إختر المكان",
				align    = 'center',
				elements = elements
			},
		function(data, menu)

			local action = data.current.value
			local position = Teleports[action]
           
			if action == "Boiling Broke" or action == "خارج السجن" or action == "داخل السجن" then
				print(PlayerData.job.name)
				if not (PlayerData.job.name == "police" or PlayerData.job.name == "admin" or PlayerData.job.name == "agent") then
					ESX.ShowNotification("غير مصرح لك")
					return
				end
			end

			menu.close()

			DoScreenFadeOut(100)

			Citizen.Wait(250)

			SetEntityCoords(PlayerPedId(), position["x"], position["y"], position["z"])

			Citizen.Wait(250)

			DoScreenFadeIn(100)
			
		end,

		function(data, menu)
			menu.close()
		end)
	else
		local position = Teleports[Values["goal"][1]]

		DoScreenFadeOut(100)

		Citizen.Wait(250)

		SetEntityCoords(PlayerPedId(), position["x"], position["y"], position["z"])

		Citizen.Wait(250)

		DoScreenFadeIn(100)
	end
end

Citizen.CreateThread(function()
	local blip = AddBlipForCoord(Teleports["Boiling Broke"]["x"], Teleports["Boiling Broke"]["y"], Teleports["Boiling Broke"]["z"])

    SetBlipSprite (blip, 188)
    SetBlipDisplay(blip, 4)
    SetBlipScale  (blip, 0.8)
    SetBlipColour (blip, 49)
    SetBlipAsShortRange(blip, true)

    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("<FONT FACE = 'A9eelsh'>"..'ﻱﺰﻛﺮﻤﻟﺍ ﻦﺠﺴﻟﺍ')
    EndTextCommandSetBlipName(blip)
end)