----------------------------------------------------------------
-- Copyright © 2019 by <PERSON>
-- Made By: Guy293
-- GitHub: https://github.com/Guy293
-- Fivem Forum: https://forum.fivem.net/u/guy293/
-- Tweaked by: <PERSON><PERSON><PERSON><PERSON> & <PERSON>
----------------------------------------------------------------

--- DO NOT EDIT THIS --
local holstered  = true
local blocked	 = false
local PlayerData = {}
------------------------
WaitTime = 1500

Citizen.CreateThread(function()
	
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	PlayerData = ESX.GetPlayerData()
	if PlayerData.job.name == 'admin' or PlayerData.job.name == 'police' or PlayerData.job.name == 'agent' then
		WaitTime = 1000
	else
		WaitTime = 1500
	end
end)


RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
	if PlayerData.job.name == 'admin' or PlayerData.job.name == 'police' or PlayerData.job.name == 'agent' then
		WaitTime = 1000
	else
		WaitTime = 1500
	end
end)

docaranim = true
RegisterNetEvent('esx_hamada:setPolice', function(setWeapon)
	if setWeapon == GetHashKey("WEAPON_UNARMED") then
		docaranim = true
	else
		holstered = false
		nowweapon = setWeapon
		docaranim = false
	end
end)

RegisterNetEvent('esx_hamada:justleft', function()
	-- Wait(1500)
	local ped = PlayerPedId()
	local retval, weaponHash = GetCurrentPedWeapon(ped, 1)
	if weaponHash == GetHashKey("WEAPON_UNARMED") then
	elseif docaranim == false then
		holstered = false
		docaranim = true
	end
end)


	nowweapon = nil
 Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		loadAnimDict("rcmjosh4")
		loadAnimDict("reaction@intimidation@cop@unarmed")
		loadAnimDict("reaction@intimidation@1h")
		local ped = PlayerPedId()
			if not IsPedInAnyVehicle(ped, false) and docaranim then
				if GetVehiclePedIsTryingToEnter (ped) == 0 and (GetPedParachuteState(ped) == -1 or GetPedParachuteState(ped) == 0) and not IsPedInParachuteFreeFall(ped) then
					if CheckWeapon(ped) then
						--if IsPedArmed(ped, 4) then
						if holstered then
							blocked   = true
						    local selectedweapon = GetSelectedPedWeapon(ped)
							if docaranim then
								if selectedweapon == 487013001 or selectedweapon == -2084633992 then
									TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 8.0, 2.0, -1, 50, 2.0, 0, 0, 0 )
								else
									SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
									TaskPlayAnim(ped, "reaction@intimidation@1h", "intro", 5.0, 1.0, -1, 50, 0, 0, 0, 0 )
								end
								--TaskPlayAnim(ped, "reaction@intimidation@1h", "intro", 5.0, 1.0, -1, 30, 0, 0, 0, 0 ) Use this line if you want to stand still when removing weapon
									Citizen.Wait(WaitTime)
								SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
									Citizen.Wait(WaitTime)
									if selectedweapon == 487013001 or selectedweapon == -2084633992 then
										TaskPlayAnim(ped, "rcmjosh4", "josh_leadout_cop2", 8.0, 2.0, -1, 48, 10, 0, 0, 0 )
									end
								Citizen.Wait(400)
								ClearPedTasks(ped)
							end
							holstered = false
							nowweapon = selectedweapon
						else
							blocked = false
						end
					else
					--elseif not IsPedArmed(ped, 4) then
						if not holstered then
							if nowweapon == 487013001 or nowweapon == -2084633992 then
								TaskPlayAnim(ped, "weapons@pistol@", "aim_2_holster", 8.0, 2.0, -1, 48, 10, 0, 0, 0 )
							else
								TaskPlayAnim(ped, "reaction@intimidation@1h", "outro", 8.0, 3.0, -1, 50, 0, 0, 0.125, 0 ) -- Change 50 to 30 if you want to stand still when holstering weapon
							end
								--TaskPlayAnim(ped, "reaction@intimidation@1h", "outro", 8.0, 3.0, -1, 30, 0, 0, 0.125, 0 ) Use this line if you want to stand still when holstering weapon
									Citizen.Wait(1700)
								ClearPedTasks(ped)
								nowweapon = nil
							holstered = true
						end
					end
				else
					SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
				end
			else
				holstered = true
			end
	end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if blocked then
			DisableControlAction(1, 25, true )
			DisableControlAction(1, 140, true)
			DisableControlAction(1, 141, true)
			DisableControlAction(1, 142, true)
			DisableControlAction(1, 23, true)
			DisableControlAction(1, 37, true) -- Disables INPUT_SELECT_WEAPON (TAB)
			DisablePlayerFiring(ped, true) -- Disable weapon firing
		end
	end
end)


function CheckWeapon(ped)
	--[[if IsPedArmed(ped, 4) then
		return true
	end]]
	if IsEntityDead(ped) then
		blocked = false
			return false
		else
			for i = 1, #Config.Weapons do
				if GetHashKey(Config.Weapons[i]) == GetSelectedPedWeapon(ped) then
					return true
				end
			end
		return false
	end
end


function loadAnimDict(dict)
	while ( not HasAnimDictLoaded(dict)) do
		RequestAnimDict(dict)
		Citizen.Wait(0)
	end
end
RegisterNetEvent('abdulrhman:esx_animations2:holsterweapon:fix_blocked')  -- يصلح مشكلة ركوب السيارة و إخراج السلاح
AddEventHandler('abdulrhman:esx_animations2:holsterweapon:fix_blocked', function() -- يستعمل في كوماند -- /LAG : esx_misc
    blocked = false
end)

------