


--Drugs Effects

--Weed

RegisterNetEvent('esx_drugeffects:onWeed')
AddEventHandler('esx_drugeffects:onWeed', function()
  local playerPed = PlayerPedId()
    RequestAnimSet("move_m@hipster@a") 
    while not HasAnimSetLoaded("move_m@hipster@a") do
      Citizen.Wait(100)
    end    

    TaskStartScenarioInPlace(playerPed, "WORLD_HUMAN_SMOKING_POT", 0, 1)
    disableAllControlActions = true
    Citizen.CreateThread(function()
            while disableAllControlActions do
              Citizen.Wait(0)
              DisableAllControlActions(0) 
              EnableControlAction(0, 249, true)
              EnableControlAction(0, 311, true)
              EnableControlAction(0, 45, true)
              EnableControlAction(0, 1, true)
              EnableControlAction(0, 2, true)
            end
          end)

    Citizen.Wait(6000)
    disableAllControlActions = false
    SetRunSprintMultiplierForPlayer(PlayerId(), 1.23)
    ClearPedTasksImmediately(playerPed)    

end)




--Opium
RegisterNetEvent('esx_drugeffects:onOpium')
AddEventHandler('esx_drugeffects:onOpium', function()
  
  local playerPed = PlayerPedId()
  
        RequestAnimSet("move_m@drunk@moderatedrunk") 
    while not HasAnimSetLoaded("move_m@drunk@moderatedrunk") do
      Citizen.Wait(100)
    end    

    TaskStartScenarioInPlace(playerPed, "WORLD_HUMAN_SMOKING_POT", 0, 1)
    disableAllControlActions = true
    Citizen.CreateThread(function()
            while disableAllControlActions do
              Citizen.Wait(0)
              DisableAllControlActions(0)
              EnableControlAction(0, 249, true)
              EnableControlAction(0, 311, true)
              EnableControlAction(0, 45, true)
              EnableControlAction(0, 1, true)
              EnableControlAction(0, 2, true)
            end
          end)
    Citizen.Wait(6000)
    SetRunSprintMultiplierForPlayer(PlayerId(), 1.36)
    disableAllControlActions = false
 end)

--Meth
RegisterNetEvent('esx_drugeffects:onMeth')
AddEventHandler('esx_drugeffects:onMeth', function()
  
  local playerPed = PlayerPedId()
  local maxHealth = GetEntityMaxHealth(playerPed)

        RequestAnimSet("move_injured_generic") 
    while not HasAnimSetLoaded("move_injured_generic") do
      Citizen.Wait(100)
    end    

    TaskStartScenarioInPlace(playerPed, "WORLD_HUMAN_SMOKING_POT", 0, 1)
    disableAllControlActions = true
    Citizen.CreateThread(function()
            while disableAllControlActions do
              Citizen.Wait(0)
              DisableAllControlActions(0)
              EnableControlAction(0, 249, true)
              EnableControlAction(0, 311, true)
              EnableControlAction(0, 45, true)
              EnableControlAction(0, 1, true)
              EnableControlAction(0, 2, true)
            end
          end)
    Citizen.Wait(6000)
    disableAllControlActions = false
    
end)

--Coke
RegisterNetEvent('esx_drugeffects:onCoke')
AddEventHandler('esx_drugeffects:onCoke', function()
  
  local playerPed = PlayerPedId()
  local maxHealth = GetEntityMaxHealth(playerPed)

        RequestAnimSet("move_m@hurry_butch@a") 
    while not HasAnimSetLoaded("move_m@hurry_butch@a") do
      Citizen.Wait(100)
    end    

    TaskStartScenarioInPlace(playerPed, "WORLD_HUMAN_SMOKING_POT", 0, 1)
    disableAllControlActions = true
    Citizen.CreateThread(function()
            while disableAllControlActions do
              Citizen.Wait(0)
              DisableAllControlActions(0)
              EnableControlAction(0, 249, true)
              EnableControlAction(0, 311, true)
              EnableControlAction(0, 45, true)
              EnableControlAction(0, 1, true)
              EnableControlAction(0, 2, true)
            end
          end)
    Citizen.Wait(6000)
    disableAllControlActions = false
    
end)