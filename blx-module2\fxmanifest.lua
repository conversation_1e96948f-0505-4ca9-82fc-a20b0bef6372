shared_script '@najm/ai_module_fg-obfuscated.js'
shared_script '@najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.js'
shared_script 'najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.js'
shared_script 'najm/ai_module_fg-obfuscated.lua'
shared_script '@fiiveguard/ai_module_fg-obfuscated.js'
 
 
shared_script '@fiveguard/ai_module_fg-obfuscated.js'
  
shared_script '@fiveguardd/ai_module_fg-obfuscated.js'
 
 
--
--8888888888P          888                            8888888b.                    
--      d88P           888                            888  "Y88b                   
--     d88P            888                            888    888                   
--    d88P     8888b.  88888b.  888  888  8888b.      888    888  .d88b.  888  888 
--   d88P         "88b 888 "88b 888  888     "88b     888    888 d8P  Y8b 888  888 
--  d88P      .d888888 888  888 888  888 .d888888     888    888 88888888 Y88  88P 
-- d88P       888  888 888  888 Y88b 888 888  888     888  .d88P Y8b.      Y8bd8P  
--d8888888888 "Y888888 888  888  "Y88888 "Y888888     8888888P"   "Y8888    Y88P   
--                                   888                                           
--                              Y8b d88P                                           
--                               "Y88P"                                            
--
--Thank you for using Zahya Dev Files V1 : https://discord.gg/aFFMpFcKuZ^7
fx_version 'bodacious'
game 'common'
client_script 'dist/client.js'
server_script 'dist/server.js'
files {
    'dist/ui.html'
}
ui_page 'dist/ui.html'
-- client_script 'cLfoFRAnPTTF.lua'
-- client_script 'otSIIHpflwxT.lua'
shared_script '@es_extended/imports.lua'