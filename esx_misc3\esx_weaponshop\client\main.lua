local ESXDATA = {}
local HasAlreadyEnteredMarker = false
local LastZone = nil
local CurrentAction = nil
local CurrentActionMsg = ''
local CurrentActionData = {}
local ShopOpen = false

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end

	
	ESX.PlayerData = ESX.GetPlayerData()

	ESX.TriggerServerCallback('esx_weaponshop:getShop', function(shopItems)
		for k,v in pairs(shopItems) do
			Config_esx_weaponshop.Zones[k].Items = v
		end
	end)
end)

RegisterNetEvent('esx_weaponshop:sendShop')
AddEventHandler('esx_weaponshop:sendShop', function(shopItems)
	for k,v in pairs(shopItems) do
		Config_esx_weaponshop.Zones[k].Items = v
	end
end)

--[[Citizen.CreateThread(function()
	local sleep = 3000
	while true do
		ESX.TriggerServerCallback('esx_adminjob:checkJobPlayer', function(job_player_what)
			sleep = 1000
			local ped = PlayerPedId()
			local _,pedWeapon = GetCurrentPedWeapon(ped, true)
			if job_player_what == 'police' or job_player_what == 'admin' or job_player_what == 'agent' then
				local sdjvhbsdjvh = nil
			else
				if not leojob then
					if IsPedArmed(ped, 6) and GetHashKey('WEAPON_UNARMED') ~= pedWeapon then
						for i=1, #Config.WeaponLevel, 1 do
							local item = Config.WeaponLevel[i]
							if pedWeapon == GetHashKey(item.item) then
								if not checkRequiredXPlevel(item.level) then 
									SetCurrentPedWeapon(ped, 'WEAPON_UNARMED', 1)
									ESX.ShowNotification('المستوى المطلوب لسلاح <font color=orange>'..item.label..'</font> <font color=red>'..item.level)
									blocked	 = false
									TriggerEvent('abdulrhman:esx_animations2:holsterweapon:fix_blocked') -- يصلح مشكلة ركوب السيارة و إخراج السلاح
								end
							end					
						end	
					end
				end
			end
		end, ESX.PlayerData.identifier)
		Citizen.Wait(sleep)
	end
end)--]]


-- function OpenBuyLicenseMenu(zone)
-- 	ESX.UI.Menu.CloseAll()

-- 	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shop_license', {
-- 		title = _U('buy_license_esx_weaponshop'),
-- 		align = 'top-left',
-- 		elements = {
-- 			{label = _U('no_esx_weaponshop'), value = 'no'},
-- 			{label = _U('yes_esx_weaponshop', ('<span style="color: green;">%s</span>'):format((_U('shop_menu_item', ESX.Math.GroupDigits(Config_esx_weaponshop.LicensePrice))))), value = 'yes'},
-- 		}
-- 	}, function(data, menu)
-- 		if data.current.value == 'yes' then
-- 			ESX.TriggerServerCallback('esx_weaponshop:buyLicense', function(bought)
-- 				if bought then
-- 					menu.close()
-- 					OpenShopMenu(zone)
-- 				end
-- 			end)
-- 		end
-- 	end, function(data, menu)
-- 		menu.close()
-- 	end)
-- end

function checkRequiredXPlevel(required)
	local xp = exports.zahya_xplevel.ESXP_GetXP()
	local level = exports.zahya_xplevel.ESXP_GetRank()

	
	if level >= required then
		return true
	else
		return false
	end
end

function DisplayScaleform22(title, description, time)
    if time == nil then time = 4000 end
    Citizen.CreateThread(function()
      local scaleform = RequestScaleformMovie("mp_big_message_freemode")
      while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
      end
    
      BeginScaleformMovieMethod(scaleform, "SHOW_SHARD_WASTED_MP_MESSAGE")
      PushScaleformMovieMethodParameterString(title)
      PushScaleformMovieMethodParameterString(description)
      PushScaleformMovieMethodParameterInt(5)
      EndScaleformMovieMethod()
      
      local show = true
      Citizen.SetTimeout(6000, function()
        show = false
      end)
    
      while show do
        Citizen.Wait(0)
        DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255) -- Draw the scaleform fullscreen
      end
    end)
end

function OpenShopMenu(zone)
	local elements = {}
	ShopOpen = true

	for i=1, #Config_esx_weaponshop.Zones[zone].Items, 1 do
		local item = Config_esx_weaponshop.Zones[zone].Items[i]
		--print(item.level)
		if checkRequiredXPlevel(item.level) then 
			table.insert(elements, {
				label = ('%s - <span style="color:00EE4F;">%s</span>'):format(item.label, _U('shop_menu_item', ESX.Math.GroupDigits(item.price))),
				price = item.price,
				ammo = item.ammo,
				weaponName = item.item,
				level = true
			})
		else
			table.insert(elements, {
				label = item.label..' <font color=red>|</font> <font color=gray>متاح من مستوى</font><font color=orange> '..item.level..'</font>',
				price = item.price,
				weaponName = item.item,
				level = false
			})
		end
	end

	ESX.UI.Menu.CloseAll()
	PlaySoundFrontend(-1, 'BACK', 'HUD_AMMO_SHOP_SOUNDSET', false)

	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shop', {
		title = _U('shop_menu_title'),
		align = 'top-left',
		elements = elements
	}, function(data, menu)
		if data.current.level then
			ESX.TriggerServerCallback('esx_weaponshop:buyWeapon', function(bought)
				if bought then
					--DisplayBoughtScaleform(data.current.weaponName, data.current.price)
					DisplayScaleform22("<FONT FACE='A9eelsh'>~g~ﻞﺑﺎﻘﻣ ﺡﻼﺳ ﺀﺍﺮﺷ ﻢﺗ", "<FONT FACE='A9eelsh'>~b~$"..data.current.price.."</font>", 6000)
				else
					PlaySoundFrontend(-1, 'ERROR', 'HUD_AMMO_SHOP_SOUNDSET', false)
				end
			end, data.current.weaponName, zone, data.current.ammo)
		end
	end, function(data, menu)
		PlaySoundFrontend(-1, 'BACK', 'HUD_AMMO_SHOP_SOUNDSET', false)
		ShopOpen = false
		menu.close()

		CurrentAction     = 'shop_menu'
		CurrentActionMsg  = _U('shop_menu_prompt')
		CurrentActionData = { zone = zone }
	end, function(data, menu)
		PlaySoundFrontend(-1, 'NAV', 'HUD_AMMO_SHOP_SOUNDSET', false)
	end)
end

function DisplayBoughtScaleform(weaponName, price)
	local scaleform = ESX.Scaleform.Utils.RequestScaleformMovie('MP_BIG_MESSAGE_FREEMODE')
	local sec = 4

	BeginScaleformMovieMethod(scaleform, 'SHOW_WEAPON_PURCHASED')

	PushScaleformMovieMethodParameterString("<FONT FACE='A9eelsh'>~g~ﻞﺑﺎﻘﻣ ﺀﺍﺮﺷ ﻢﺗ", "<FONT FACE='A9eelsh'>~b~$"..ESX.Math.GroupDigits(price).."</font>")
	PushScaleformMovieMethodParameterString(ESX.GetWeaponLabel(weaponName))
	PushScaleformMovieMethodParameterInt(GetHashKey(weaponName))
	PushScaleformMovieMethodParameterString('')
	PushScaleformMovieMethodParameterInt(100)

	EndScaleformMovieMethod()

	PlaySoundFrontend(-1, 'WEAPON_PURCHASE', 'HUD_AMMO_SHOP_SOUNDSET', false)

	Citizen.CreateThread(function()
		while sec > 0 do
			Citizen.Wait(0)
			sec = sec - 0.01
	
			DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255)
		end
	end)
end

AddEventHandler('esx_weaponshop:hasEnteredMarker', function(zone)
	if zone == 'GunShop' or zone == 'BlackWeashop' then
		CurrentAction     = 'shop_menu'
		CurrentActionMsg  = _U('shop_menu_prompt')
		CurrentActionData = { zone = zone }
	end
end)

AddEventHandler('esx_weaponshop:hasExitedMarker', function(zone)
	CurrentAction = nil
	ESX.UI.Menu.CloseAll()
end)

AddEventHandler('onResourceStop', function(resource)
	if resource == GetCurrentResourceName() then
		if ShopOpen then
			ESX.UI.Menu.CloseAll()
		end
	end
end)

-- Create Blips
Citizen.CreateThread(function()
	for k,v in pairs(Config_esx_weaponshop.Zones) do
		if v.Legal then
			for i = 1, #v.Locations, 1 do
				local blip = AddBlipForCoord(v.Locations[i])

				SetBlipSprite (blip, 110)
				SetBlipDisplay(blip, 4)
				SetBlipScale  (blip, 1.0)
				SetBlipColour (blip, 81)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentSubstringPlayerName(_U('map_blip'))
				EndTextCommandSetBlipName(blip)
			end
		end
	end
end)

-- Display markers
Citizen.CreateThread(function()
	while true do
		local sleep = 1500

		local coords = GetEntityCoords(PlayerPedId())

		for k,v in pairs(Config_esx_weaponshop.Zones) do
			for i = 1, #v.Locations, 1 do
				if (Config_esx_weaponshop.Type ~= -1 and GetDistanceBetweenCoords(coords, v.Locations[i], true) < Config_esx_weaponshop.DrawDistance) then
					DrawMarker(Config_esx_weaponshop.Type, v.Locations[i], 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config_esx_weaponshop.Size.x, Config_esx_weaponshop.Size.y, Config_esx_weaponshop.Size.z, Config_esx_weaponshop.Color.r, Config_esx_weaponshop.Color.g, Config_esx_weaponshop.Color.b, 100, false, true, 2, false, false, false, false)
					sleep = 0
				end
			end
		end
		Citizen.Wait(sleep)
	end
end)

-- Enter / Exit marker events
Citizen.CreateThread(function()
	while true do
		local sleep = 1500
		local coords = GetEntityCoords(PlayerPedId())
		local isInMarker, currentZone = false, nil

		for k,v in pairs(Config_esx_weaponshop.Zones) do
			for i=1, #v.Locations, 1 do
				if GetDistanceBetweenCoords(coords, v.Locations[i], true) < Config_esx_weaponshop.Size.x then
					isInMarker, ShopItems, currentZone, LastZone = true, v.Items, k, k
					sleep = 0
				end
			end
		end
		if isInMarker and not HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = true
			TriggerEvent('esx_weaponshop:hasEnteredMarker', currentZone)
		end
		
		if not isInMarker and HasAlreadyEnteredMarker then
			HasAlreadyEnteredMarker = false
			TriggerEvent('esx_weaponshop:hasExitedMarker', LastZone)
		end
		Citizen.Wait(sleep)
	end
end)

-- Key Controls
Citizen.CreateThread(function()
	while true do
		local sleep = 1500

		if CurrentAction ~= nil then
			sleep = 0
			ESX.ShowHelpNotification(CurrentActionMsg)

			if IsControlJustReleased(0, 38) then

				if CurrentAction == 'shop_menu' then
					if Config_esx_weaponshop.LicenseEnable and Config_esx_weaponshop.Zones[CurrentActionData.zone].Legal then
						ESX.TriggerServerCallback('esx_license:checkLicense', function(hasWeaponLicense)
							if hasWeaponLicense then
								OpenShopMenu(CurrentActionData.zone)
							else
								-- OpenBuyLicenseMenu(CurrentActionData.zone)
								ESX.ShowNotification('ليس لديك رخصة <font color= red>سلاح</font>')
							end
						end, GetPlayerServerId(PlayerId()), 'weapon')
					else
						OpenShopMenu(CurrentActionData.zone)
					end
				end

				CurrentAction = nil
			end
		end
		Citizen.Wait(sleep)
	end
end)
