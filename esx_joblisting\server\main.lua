local availableJobs = {}

-- دالة للحصول على متطلبات الخبرة للوظيفة
function GetJobExperienceRequirement(jobName)
    if Config.ExperienceSystem.Enabled then
        -- نتحقق من الإعدادات في config.lua
        if Config.ExperienceSystem.JobRequirements[jobName] then
            return Config.ExperienceSystem.JobRequirements[jobName]
        end
    end
    return nil -- إذا لم تكن الوظيفة محددة، لا توجد متطلبات خبرة
end

-- دالة للتحقق من أهلية اللاعب للوظيفة
function CheckJobEligibility(playerId, jobName)
    if not Config.ExperienceSystem.Enabled then
        return true -- إذا كان النظام معطل، السماح للجميع
    end

    local requiredLevel = GetJobExperienceRequirement(jobName)
    if requiredLevel == nil then
        return true -- إذا لم تكن الوظيفة تحتاج خبرة، السماح للجميع
    end

    local xPlayer = ESX.GetPlayerFromId(playerId)
    if not xPlayer then
        return false
    end

    local playerLevel = xPlayer.get("rank") or 0

    return playerLevel >= requiredLevel
end

MySQL.ready(function()
    -- جلب الوظائف العادية من قاعدة البيانات
    MySQL.Async.fetchAll('SELECT name, label FROM jobs WHERE whitelisted = @whitelisted', {
        ['@whitelisted'] = false
    }, function(result)
        for i=1, #result, 1 do
            table.insert(availableJobs, {
                job = result[i].name,
                label = result[i].label,
                grade = result[i].grade
            })
        end
    end)
end)

local disroles = {


    ["1301356913111339119"] = {"police", "<span style='color:#34aeeb;'> الشرطة - جندي", 0},
    ["1301356911819493438"] = {"police", "<span style='color:#34aeeb;'> الشرطة - جندي أول", 1},
    ["1301356910968307763"] = {"police", "<span style='color:#34aeeb;'> الشرطة - عريف", 2},
    ["1301356910217531403"] = {"police", "<span style='color:#34aeeb;'> الشرطة - وكيل رقيب", 3},
    ["1301356908569165836"] = {"police", "<span style='color:#34aeeb;'> الشرطة - رقيب", 4},
    ["1301356907017015327"] = {"police", "<span style='color:#34aeeb;'> الشرطة - رقيب أول", 5},
    ["1301356906346053642"] = {"police", "<span style='color:#34aeeb;'> الشرطة - رئيس رقباء", 6},
    ["1301356905381232713"] = {"police", "<span style='color:#34aeeb;'> الشرطة - ملازم", 7},
    ["1301356904173535295"] = {"police", "<span style='color:#34aeeb;'> الشرطة - ملازم اول", 8},
    ["1301356903078694917"] = {"police", "<span style='color:#34aeeb;'> الشرطة - نقيب", 9},
    ["1301356902231441428"] = {"police", "<span style='color:#34aeeb;'> الشرطة - رائد", 10},
    ["1301356901220618258"] = {"police", "<span style='color:#34aeeb;'> الشرطة - مقدم", 11},
    ["1301356900306260008"] = {"police", "<span style='color:#34aeeb;'> الشرطة - عقيد", 12},
    ["1301356899249295451"] = {"police", "<span style='color:#34aeeb;'> الشرطة - عميد", 13},
    ["1301356898167165054"] = {"police", "<span style='color:#34aeeb;'> الشرطة - لواء", 14},
    ["1301356896107757579"] = {"police", "<span style='color:#34aeeb;'> الشرطة - مساعد نائب الشرطة", 15},
    ["1301356895130619965"] = {"police", "<span style='color:#34aeeb;'> الشرطة - مساعد قائد الشرطة", 16},
    ["1301356893926719561"] = {"police", "<span style='color:#34aeeb;'>  الشرطة - نائب قائد الشرطة ", 17},
    ["1301356893247377409"] = {"police", "<span style='color:#34aeeb;'>  الشرطة - قائد الشرطة", 18},

	------------------------------------------------------------------------------------------------------------------------------------

    ["1301356990697574450"] = {"agent","<span style='color:#76ff03;'> ‍♂️ جندي", 0},
    ["1301356988768452608"] = {"agent","<span style='color:#76ff03;'> ‍♂️ جندي اول", 1},
    ["1301356987405041694"] = {"agent","<span style='color:#76ff03;'> ‍♂️ عريف", 2},
    ["1301356986335760425"] = {"agent","<span style='color:#76ff03;'> ‍♂️ وكيل رقيب", 3},
    ["1301356984913629185"] = {"agent","<span style='color:#76ff03;'> ‍♂️ رقيب", 4},
    ["1301356983504601088"] = {"agent","<span style='color:#76ff03;'> ‍♂️ رقيب اول", 5},
    ["1301356981990195333"] = {"agent","<span style='color:#76ff03;'> ‍♂️ رئيس رقباء", 6},
    ["1301356981046607915"] = {"agent","<span style='color:#76ff03;'> ‍♂️ ملازم", 7},
    ["1301356980060815360"]= {"agent","<span style='color:#76ff03;'> ‍♂️ ملازم اول", 8},
    ["1301356978483892287"] = {"agent","<span style='color:#76ff03;'> ‍♂️ نقيب", 9},
    ["1301356977443700746"]= {"agent","<span style='color:#76ff03;'> ‍♂️ رائد", 10},
    ["1301356975866642464"] = {"agent","<span style='color:#76ff03;'> ‍♂️ مقدم", 11},
    ["1301356974683983955"] = {"agent","<span style='color:#76ff03;'> ‍♂️ عقيد", 12},
    ["1301356974147108945"] = {"agent","<span style='color:#76ff03;'> ‍♂️ عميد", 13},
    ["1301356972855005236"] = {"agent","<span style='color:#76ff03;'> ‍♂️ لواء", 14},
	["1301356969340174407"] = {"agent","<span style='color:#76ff03;'> ‍♂️ مساعد نائب حرس الحدود", 15},
	["1301356968308510814"] = {"agent","<span style='color:#76ff03;'> ‍♂️ مساعد قائد حرس الحدود ", 16},
    ["1301356967339757569"] = {"agent","<span style='color:#76ff03;'> ‍♂️ نائب قائد حرس الحدود ", 17},
    ["1301356966706282516"] = {"agent","<span style='color:#76ff03;'> ‍♂️  قائد حرس الحدود ", 18},

    ------------------------------------------------------------------------------------------------------------------------------------
    ["1304408274149314614"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - متدرب", 0},
    ["1301357051078774895"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 1", 1},
    ["1301357050269532211"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 2", 2},
    ["1301357049015308410"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 3", 3},
    ["1301357048058875976"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 4", 4},
    ["1301357047379656776"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 5", 5},
    ["1301357046557573151"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 6", 6},
    ["1301357045227982980"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 7", 7},
    ["1301357044569346110"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 8", 8},
    ["1301357043688542308"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 9", 9},
    ["1301357042740498442"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - مستوى 10", 10},
    ["1301357039963996184"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - نائب القائد", 11},
    ["1301357039276261406"] = {"ambulance", "<span style='color:red;'>الهلال الاحمر - القائد", 12},
    
    ------------------------------------------------------------------------------------------------------------------------------------

    ["1304409087358013480"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - فني متدرب", 0},
    ["1301357027984937055"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مـستـوى 1", 1},
    ["1301357026840150157"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مـستـوى 2", 2},
    ["1301357025434931281"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مـسـتـوى 3", 3},
    ["1301357024390676500"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مستوى4", 4},
    ["1301357023119675443"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مستوى5", 5},
    ["1301357021974757499"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مستوى6", 6},
    ["1301357021370781696"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مستوى7", 7},
    ["1301357020041183232"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مستوى8", 8},
    ["1301357018883424306"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مستوى9", 9},
    ["1304410720435310593"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مساعد نائب الكراج", 14},
    ["1301357016219910217"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - مساعد مدير الكراج", 14},
    ["1301357015158755359"] = {"mechanic", "<span style='color:gray;'> كراج الميكانيكي - نائب مدير الكراج", 10},
    ["1301357013938212988"] = {"mechanic", "<span style='color:#gray;'> كراج الميكانيكي - مدير الكراج", 11},

    ------------------------------------------------------------------------------------------------------------------------------------  

    ['834479382700752927'] = {"taxi","<span style='color:#ff9400;'> 🚗 سائق ", 0},
    ['834477395318407208'] = {"taxi","<span style='color:#ff9400;'> 🚗 سائق معتمد", 1},
    ['834477878304833578'] = {"taxi","<span style='color:#ff9400;'> 🚗 سائق محترف", 2},
    ['833367081462267914'] = {"taxi","<span style='color:#ff9400;'> 🚗 نائب مدير شركة التاكسي", 3},
    ['833366938398621726'] = {"taxi","<span style='color:#ff9400;'> 🚗 مدير شركة التاكسي", 4},

  
  
    ------------------------------------------------------------------------------------------------------------------------------------    
    ["1311027736135073915"] = {"admin", "<span style='color:gold;'> الرقابة و التفتيش - مشرف متدرب", 0},
    ["1301356834833174618"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - مشرف", 1},
    ["1301356833784594442"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش -  مشرف +", 2},
    ["1311027028908310578"] = {"admin", "<span style='color:gold;'> الرقابة و التفتيش - مشرف عام", 3},
    ["1301356831784042566"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - ادمن", 4},
    ["1301356830676488344"] = {"admin", "<span style='color:gold;'>الرقابة و التفتيش - ادمن +", 5},
}

ESX.RegisterServerCallback('esx_joblisting:getJobsList', function(source, cb)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local playerLevel = 0

    -- الحصول على مستوى خبرة اللاعب
    if xPlayer and Config.ExperienceSystem.Enabled then
        playerLevel = xPlayer.get("rank") or 0
    end

    local disjobs = {}
    for k, v in pairs(availableJobs) do
        local requiredLevel = GetJobExperienceRequirement(v.job)
        local jobData = {
            job = v.job,
            label = v.label,
            grade = v.grade,
            required_level = requiredLevel, -- nil للوظائف العادية، رقم للوظائف التي تحتاج خبرة
            player_level = playerLevel,
            has_experience_requirement = requiredLevel ~= nil
        }
        disjobs[k] = jobData
    end

	if exports['discord_perms'].GetRoles(src, src) ~= nil then
		for k, v in pairs(exports['discord_perms'].GetRoles(src, src)) do
        	for i, j in pairs(disroles) do
            	if i == v then
            		local requiredLevel = GetJobExperienceRequirement(j[1])
                	table.insert(disjobs, {
                    	job = j[1],
                    	label = j[2],
                    	grade = j[3],
                    	required_level = requiredLevel,
                    	player_level = playerLevel,
                    	has_experience_requirement = requiredLevel ~= nil
                	})
                	break
            	end
        	end
    	end
	end
    cb(disjobs)
end)


--[[
function MinaStatus (name, title, message, color)
	local DiscordWebHook = "1304118500713041940"
	
	local embeds = {
		{
			["title"]=title,
			["type"]="rich",
            ["description"] = message,
			["color"] =color,
			["footer"]=  { ["text"]= "حالة الميناء", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/829403287777837126/846541193026469888/FS.png"},
		}
	}
	
	if message == nil or message == '' then return FALSE end
	PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

ESX.PlayerData.job.name
"..GetPlayerName(source).."
"..GetPlayerIdentifiers(source)[1].."
--]]

local AdminWebHook = "https://discord.com/api/webhooks/1309982470523064350/J4mOkmQvup01VZ6L3lyWmoEKAF7MpgIgD8pBo54NyQoWq9XQHbTOW1bE8BYPZpyws5_X" -- سجل تغير وظيفة الرقابة
local PoliceWebHook = "https://discord.com/api/webhooks/1336870887634374747/45frRkyyFBAJs1vU4eJaMdMhZbCCPJStar2jGcWaNZ4u-segFLYoJHg0nPRRhF4YVqCo" -- سجل تغير وظيفة الشرطة
local AgentWebHook = "https://discord.com/api/webhooks/1328586947366617182/b1Ie5RLxcbKQbEqVCiroTgxm2s1xBWun5vOk6N56A77EHrYIAb2Eep3NU28hI-GXQpYo" -- سجل تغير وظيفة حرس الحدود
local AmbulanceWebHook = "https://discord.com/api/webhooks/1309982470523064350/J4mOkmQvup01VZ6L3lyWmoEKAF7MpgIgD8pBo54NyQoWq9XQHbTOW1bE8BYPZpyws5_X" -- سجل تغير وظيفة الهلال الاحمر
local MechanicWebHook = "https://discord.com/api/webhooks/1339457394090180662/f3_QIJ9srzTjh8mewbREK41MXgQ57qRsXPIlYW1e65BsNeV5ICzz8dq5cz_iwQWoTrAq" -- سجل تغير وظيفة الميكانيكي
RegisterServerEvent('esx_joblisting:setJob_kaugy36')
AddEventHandler('esx_joblisting:setJob_kaugy36', function(newjob, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ids = ExtractIdentifiers(source)
    local _discordID ="<@" ..ids.discord:gsub("discord:", "")..">"
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end

    -- التحقق من متطلبات الخبرة (فقط للوظائف التي تحتاج خبرة)
    local requiredLevel = GetJobExperienceRequirement(newjob)
    if requiredLevel ~= nil and not CheckJobEligibility(source, newjob) then
        local playerLevel = xPlayer.get("rank") or 0
        local message = string.format(Config.ExperienceSystem.Messages.NotEligible, requiredLevel, playerLevel)
        TriggerClientEvent('esx:showNotification', source, message)
        return
    end

    if xPlayer then
	if xPlayer.job.name == "admin" then
	xPlayer.setJob(newjob, grade)
	local embeds_admin = {
		{
			["title"]= "**خرج من الرقابة والتفتيش**",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n".."الرتبة "..xPlayer.job.grade_label.." الرتبة\n **بيانات الوظيفة الجديدة:**\n"..newjob.." ",
			["color"] = "10038562",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	PerformHttpRequest(AdminWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_admin}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "police" then
	xPlayer.setJob(newjob, grade)
	local embeds_police = {
		{
			["title"]= "**خرج من إدارة الشرطة**",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n".."الرتبة "..xPlayer.job.grade_label.." الرتبة\n **بيانات الوظيفة الجديدة:**\n"..newjob.." ",
			["color"] = "10038562",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	PerformHttpRequest(PoliceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_police}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "agent" then
	xPlayer.setJob(newjob, grade)
	local embeds_agent = {
		{
			["title"]= "خرج من إدارة حرس الحدود",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n".."الرتبة "..xPlayer.job.grade_label.." الرتبة\n **بيانات الوظيفة الجديدة:**\n"..newjob.." ",
			["color"] = "10038562",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	PerformHttpRequest(AgentWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_agent}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "ambulance" then
	xPlayer.setJob(newjob, grade)
	local embeds_ambulance = {
		{
			["title"]= "خرج من الهلال الاحمر",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n".."الرتبة "..xPlayer.job.grade_label.." الرتبة\n **بيانات الوظيفة الجديدة:**\n"..newjob.." ",
			["color"] = "10038562",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	PerformHttpRequest(AmbulanceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_ambulance}), { ['Content-Type'] = 'application/json' })
	elseif xPlayer.job.name == "mechanic" then
	xPlayer.setJob(newjob, grade)
	local embeds_mechanic = {
		{
			["title"]= "خرج من كراج الميكانيكي",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n".."الرتبة "..xPlayer.job.grade_label.."\n **بيانات الوظيفة الجديدة:**\n"..newjob.." ",
			["color"] = "10038562",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	PerformHttpRequest(MechanicWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds_mechanic}), { ['Content-Type'] = 'application/json' })
	else
	xPlayer.setJob(newjob, grade)
    end
    end
end)

RegisterServerEvent('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme')
AddEventHandler('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ids = ExtractIdentifiers(source)
    _discordID ="<@" ..ids.discord:gsub("discord:", "")..">"
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "رجع إلى الرقابة والتفتيش",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n"..xPlayer.job.label.."\n".."الرتبة "..xPlayer.job.grade_label.."\n **بيانات الوظيفة الجديدة:**\n".." الرقابة والتفتيش",
			["color"] = "3325987",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	        PerformHttpRequest(AdminWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_police_da3oid63')
AddEventHandler('esx_joblisting:setJob_police_da3oid63', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ids = ExtractIdentifiers(source)
    _discordID ="<@" ..ids.discord:gsub("discord:", "")..">"
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "رجع إلى إدارة الشرطة",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n"..xPlayer.job.label.."\n".."الرتبة "..xPlayer.job.grade_label.."\n **بيانات الوظيفة الجديدة:**\n".." إدارة الشرطة",
			["color"] = "3325987",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	        PerformHttpRequest(PoliceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_agent_kaug362')
AddEventHandler('esx_joblisting:setJob_agent_kaug362', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ids = ExtractIdentifiers(source)
    _discordID ="<@" ..ids.discord:gsub("discord:", "")..">"
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "رجع إلى إدارة حرس الحدود",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n"..xPlayer.job.label.."\n".."الرتبة "..xPlayer.job.grade_label.."\n **بيانات الوظيفة الجديدة:**\n".." إدارة حرس الحدود",
			["color"] = "3325987",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	        PerformHttpRequest(AgentWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_ambulance_d8labd3')
AddEventHandler('esx_joblisting:setJob_ambulance_d8labd3', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ids = ExtractIdentifiers(source)
    _discordID ="<@" ..ids.discord:gsub("discord:", "")..">"
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "رجع إلى الهلال الاحمر",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n"..xPlayer.job.label.."\n".."الرتبة "..xPlayer.job.grade_label.." الرتبة\n **بيانات الوظيفة الجديدة:**\n".." الهلال الاحمر",
			["color"] = "3325987",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	        PerformHttpRequest(AmbulanceWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

RegisterServerEvent('esx_joblisting:setJob_mechanic_a73kvgad3')
AddEventHandler('esx_joblisting:setJob_mechanic_a73kvgad3', function(job, grade)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ids = ExtractIdentifiers(source)
    _discordID ="<@" ..ids.discord:gsub("discord:", "")..">"
    if not grade then grade = 0 end
    if grade < 0 then grade = 0 end
    if xPlayer then
	        local embeds = {
		{
			["title"]= "رجع إلى كراج الميكانيكي",
			["type"]="rich",
            ["description"] = "\n **هوية الموظف:**\n"..xPlayer.getName().."\n".._discordID.."\n **بيانات الوظيفة القديمة:**\n"..xPlayer.job.label.."\n".."الرتبة "..xPlayer.job.grade_label.."\n **بيانات الوظيفة الجديدة:**\n".." كراج الميكانيكي",
			["color"] = "3325987",
			["footer"]=  { ["text"]= "مركز التوظيف", 
            ["icon_url"] = "https://cdn.discordapp.com/attachments/1012685157532188692/1019978560536068176/270c2823e1c5ca60.png"},
		}
	}
	        PerformHttpRequest(MechanicWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
			xPlayer.setJob(job, grade)
    end
end)

function ExtractIdentifiers(src)
    local identifiers = {
        discord = ""
    }

    for i = 0, GetNumPlayerIdentifiers(src) - 1 do
        local id = GetPlayerIdentifier(src, i)

        if string.find(id, "discord") then
            identifiers.discord = id
        end
    end

    return identifiers
end