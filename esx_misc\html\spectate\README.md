I'm no longer mantaining this.. you can keep your script updated here
https://github.com/benzon/esx_spectate-with_player_control
thx

# esx_spectate (with Player Control)

This is my modded version that let's you check data, inventory, money, weapons and licenses of the player you're spectating.
It's very useful to check if the player's using mods since my script let's you check if the player is using godmode and let's you check his health and armor (thx to EasyAdmin and Bluethefurry for that :P)
I've also added a scrollbar to the users windows since when the server gets full the window get fullscreen and it's impossible to stop specting players since the red X is unclickable..

# RENAME IT "esx_spectate" BEFORE PUTTING IT INTO THE RESOURCE FOLDER!!!

I've already translated it into English.. if you need to translate.. modify client.lua

here are some screenshots

![ScreenShot](https://i.imgur.com/FgaGQZb.jpg)
![ScreenShot](https://i.imgur.com/VusCct5.jpg)
![ScreenShot](https://i.imgur.com/bK02Yu3.jpg)
