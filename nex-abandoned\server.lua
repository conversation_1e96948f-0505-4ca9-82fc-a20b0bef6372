-- Get the ESX shared object for server-side use
ESX = exports['es_extended']:getSharedObject()

-- Wait for ESX object if it's not immediately available (good practice)
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(100)
    end
    if Config.Debug then print("[nex-abandoned] Server: ESX object ready.") end
end)


--[[
    Event Handler: Triggered when a client notifies the server that a player left a vehicle.
    @param plate The license plate of the vehicle left.
]]
RegisterNetEvent('nex-abandoned:playerLeftVehicle', function(plate)
    local _source = source -- Player who triggered the event

    if plate and type(plate) == "string" and plate ~= "" then
        if Config.Debug then print("[nex-abandoned] Server: Received playerLeftVehicle event from source " .. _source .. " for plate: " .. plate) end

        -- Notify ALL clients to update their LastDrive table for this plate.
        -- This ensures the IsVehicleAbandoned check is consistent for all players
        -- viewing the same vehicle.
        -- Optimization: Could potentially only trigger for clients in the area,
        -- but triggering for all (-1) is simpler and often sufficient.
        TriggerClientEvent('Nex:leftVeh', -1, plate) -- -1 sends to all clients

        -- Optional: Add server-side logging or tracking here if needed
        -- e.g., LogAbandonedVehicle(plate, GetGameTimer())

    else
        print("[nex-abandoned] Server: Received playerLeftVehicle event from source " .. _source .. " with invalid plate.")
    end
end)

-- Optional: Add any other server-side logic needed for your resource here.

print("----------------------------------------")
print("[nex-abandoned] Server Script Loaded")
print("----------------------------------------")

