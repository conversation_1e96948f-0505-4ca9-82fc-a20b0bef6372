<html>

	<head>
      <link rel="stylesheet" href="reset.css">
	  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
   </head>

	<body>

      <style>
         @keyframes fade {
            from {opacity: 0;}
               to {opacity: 1;}
         }

         html {
            display: flex;
            justify-content: center;
            align-items: center;
            align-content: center;
            overflow: hidden;
         }

         .Get-base {
            margin-top: 125%;
            background-color: rgba(255, 255, 255, 0.75);
            background-clip: padding-box;
            border-radius: 7px;
            width: 300px;
            height: auto;
            padding: 10px;
            display: flex;
            justify-content: center;
            flex-direction: column;
         }

         .Get-base a {
            display: flex;
            justify-content: center;
            font-family: Ta<PERSON>wal;
            letter-spacing: 1px;
            font-size: 25px;
            margin-bottom: 5px;
         }

         .Get-base p {
            display: flex;
            justify-content: center;
            border: none;
            border-radius: 7px;
            color: white;
            border: 1px solid rgba(255, 255, 255, .1);
            background-color: rgba(50, 50, 50);
            padding: 10px;
            margin-bottom: 10px;
            font-size: 12px;
            font-family: Tajawal;
         }

         .Get-base button {
            border: none;
            border-radius: 7px;
            border: 1px solid rgba(255, 255, 255, .1);
            background-color: rgba(255, 0, 0, .75);
            padding: 10px;
            color: white;
            font-size: 16px;
            font-family: Tajawal;
            letter-spacing: 1px;
            text-transform: uppercase;
         }

         .Get-base button:hover {
            background-color: rgba(255, 0, 0, .55);
         }

         .Get-base button:focus {
            outline: none;
         }

         .y {
            top: 43.65%;
            left: 56.3%;
            position: absolute;
            color: rgb(35, 35, 35);
            background-color: white;
            padding: 5px;
            border-radius: 5px;
            font-size: 14px;
         }

         .y:hover {
            color: rgb(155, 155, 155);
         }

         .y1 {
            top: 48.95%;
            left: 56.3%;
            position: absolute;
            color: rgb(35, 35, 35);
            background-color: white;
            padding: 5px;
            border-radius: 5px;
            font-size: 14px;
         }

         .y1:hover {
            color: rgb(155, 155, 155);
         }

         .y2 {
            top: 54.5%;
            left: 56.3%;
            position: absolute;
            color: rgb(35, 35, 35);
            background-color: white;
            padding: 5px;
            border-radius: 5px;
            font-size: 14px;
         }

         .y2:hover {
            color: rgb(155, 155, 155);
         }

         .y3 {
            top: 60%;
            left: 56.3%;
            position: absolute;
            color: rgb(35, 35, 35);
            background-color: white;
            padding: 5px;
            border-radius: 5px;
            font-size: 14px;
         }

         .y3:hover {
            color: rgb(155, 155, 155);
         }

         textarea {
            position: absolute;
            left: -100%;
         }
      </style>

      <div id="Get" class="Get-base">
         <a>Coords</a>
         <div>
            <p id="GetCoords">x = 330.25, y = 225.12, z = 15.3</p>
            <i class="y fas fa-copy" onclick="GetCopy()"></i>
         </div>
         <div>
            <p id="GetnormalCoords">330.25, 225.12, 15.3</p>
            <i class="y1 fas fa-copy" onclick="GetnormalCopy()"></i>
         </div>
         <div>
            <p id="Getvector3Coords">vector3(330.25, 225.12, 15.3)</p>
            <i class="y2 fas fa-copy" onclick="Getvector3Copy()"></i>
         </div>
         <div>
            <p id="Getvector4Coords">vector4(330.25, 225.12, 15.3, 45.2)</p>
            <i class="y3 fas fa-copy" onclick="Getvector4Copy()"></i>
         </div>
         <button onclick="GetClose()">Close</button>
      </div>

      <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0-2/js/all.min.js"></script>
      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
      <script src="index.js" type="text/javascript"></script>
   </body>
</html>