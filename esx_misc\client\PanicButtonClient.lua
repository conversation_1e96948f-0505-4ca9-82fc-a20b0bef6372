local alarm = nil
local notified = {}
local Dontnotified = {}
local NoCrimetime = nil
local alarm_leo_PlayWithinDistance = {}
local blipStatus  = {}
local blipDetail  = {}
local LeoDrawTextStatus = {}
local citizenDrawTextStatus = {}
local DrawTextY = {}
local godmodStarted = {}
local canUseGodmod = {}
local canUseTransition = {}
local transitionStarted = {}
local timer = 0
local helpmetime = 15 -- seconds disable F10 if pressed
local peacetimePeriod = 0

local godmodLocations = Config.panicButton.godmodLocations
local locationsData = Config.panicButton.locationsData
local noXPzones = Config.panicButton.noXPzones
local skyTransitionLocations = Config.panicButton.skyTransitionLocations


local NoCrimetimeGODMODE = false
local NoCrimetimeGODMODESandy = false
local NoCrimetimeGODMODEPoleto = false
local NoCrimetimeGODMODECayo = false
local NoCrimetimeGODMODELos = false

--check alarm status from server side
Citizen.CreateThread(function()
	while PlayerData == nil or PlayerData.job == nil do
		Citizen.Wait(500)
	end
	
	TriggerServerEvent('esx_misc:cheakStatus')
end)

RegisterNetEvent("esx:setJob")
AddEventHandler("esx:setJob", function(job)
	PlayerData.job = job
end)

local isfriday = false



--update alarm status from server side



RegisterNetEvent("esx_misc:updateStatus")
AddEventHandler("esx_misc:updateStatus", function(status, location, firstTime)
	alarm = status
    if firstTime then
       
		for i=1, #alarm, 1 do			
			if (alarm[i].location == "peace_time" or alarm[i].location == "restart_time") and alarm[i].siren then
			    checkSkyTransitionANDgodmod(i)	
				break
			end
		end

	end

	if notified == nil then
		intialNotified()
	elseif not firstTime then
		for i=1, #alarm, 1 do			
			if alarm[i].location == location then
			checkSkyTransitionANDgodmod(i)	
			Dontnotified[i] = false
				if not alarm[i].siren and notified[i] then
					notified[i] = false
					alarm_leo_PlayWithinDistance[i] = false
				end
			end
		end
	end	
end)

RegisterNetEvent("esx_misc:updateStatus_notifieNO")
AddEventHandler("esx_misc:updateStatus_notifieNO", function(status)
	alarm = status

		for i=1, #alarm, 1 do			
		--notified[i] = true
		Dontnotified[i] = true
		checkSkyTransitionANDgodmod(i)	
	end	
end)

RegisterNetEvent("esx_misc:updateNoCrimetime")
AddEventHandler("esx_misc:updateNoCrimetime", function(data)
	NoCrimetime = data

	dogodmode = false
	dogodmodeSandy = false
	dogodmodePoleto = false 
	dogodmodeCayo= false
	dogodmodeLos= false

	for i=1, #NoCrimetime, 1 do
		if NoCrimetime[i].name == "NoCrimetime" and NoCrimetime[i].active then
			dogodmode = true
		elseif NoCrimetime[i].name == "NoCrimetimeSandy" and NoCrimetime[i].active then
			dogodmodeSandy = true
		elseif NoCrimetime[i].name == "NoCrimetimePoleto" and NoCrimetime[i].active then
			dogodmodePoleto = true
		elseif NoCrimetime[i].name == "NoCrimetimeCayo" and NoCrimetime[i].active then
			dogodmodeCayo = true
		elseif NoCrimetime[i].name == "NoCrimetimeLos" and NoCrimetime[i].active then
			dogodmodeLos = true
		end

	end

	if dogodmode and not NoCrimetimeGODMODE then

		NoCrimetimeGODMODE = true

        startNoCrimetimeGodmod()

	elseif not dogodmode then

        NoCrimetimeGODMODE = false

	end
	
	if dogodmodeSandy and not NoCrimetimeGODMODESandy then

		NoCrimetimeGODMODESandy = true

        startNoCrimetimeGodmodSandy()

	elseif not dogodmodeSandy then

        NoCrimetimeGODMODESandy = false

	end
    
	if dogodmodePoleto and not NoCrimetimeGODMODEPoleto then

		NoCrimetimeGODMODEPoleto = true

        startNoCrimetimeGodmodPoleto()

	elseif not dogodmodePoleto then

        NoCrimetimeGODMODEPoleto = false

	end

	if dogodmodeCayo and not NoCrimetimeGODMODECayo then

		NoCrimetimeGODMODECayo = true

        startNoCrimetimeGodmodCayo()

	elseif not dogodmodeCayo then

        NoCrimetimeGODMODECayo = false

	end
	if dogodmodeLos and not NoCrimetimeGODMODELos then

		NoCrimetimeGODMODELos = true

        startNoCrimetimeGodmodLos()

	elseif not dogodmodeLos then

        NoCrimetimeGODMODELos = false

	end
end)

function startNoCrimetimeGodmod()
	local isAntiExplotionEnabled, previousVehicleModel, previousVehicleHandle
	print('startNoCrimetimeGodmod')
	
	local player = PlayerPedId()
	local ped = PlayerId()
	
	--safezone
	ClearPlayerWantedLevel(PlayerId())
	print('SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true)')
	SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true)
	--godmode
	SetPedCanRagdoll(player, false)
	
	while NoCrimetimeGODMODE do
		Citizen.Wait(500)
		--safezone
		NetworkSetFriendlyFireOption(false)
		
		ClearPedBloodDamage(player)
		ResetPedVisibleDamage(player)
		ClearPedLastWeaponDamage(player)
		SetEntityProofs(player, true, true, true, true, true, true, true, true)
		
		NetworkRequestControlOfEntity(GetVehiclePedIsIn(-1))
		SetVehicleEngineHealth(GetVehiclePedIsIn(player, false), 1000.0)
		
		if PlayerData.job.name ~= 'admin' then
			DisableControlAction(2, 37, true) -- disable weapon wheel (Tab)
			DisablePlayerFiring(player,true)
			DisableControlAction(0, 106, true)
			DisableControlAction(0, 140, true)
			DisableControlAction(0, 141, true)
		end
		
		local playerPed = PlayerPedId()
		local playerCoords = GetEntityCoords(playerPed)
		local isInVehicle, vehiclePedIsIn = IsPedInAnyVehicle(playerPed, false)

		if isInVehicle then vehiclePedIsIn = GetVehiclePedIsIn(playerPed, false) end

		if IsExplosionInSphere(8, playerCoords.x, playerCoords.y, playerCoords.z, 50.0) or IsExplosionInSphere(31, playerCoords.x, playerCoords.y, playerCoords.z, 50.0) then
			print('Anti-explosions: explosion detected!')
			isAntiExplotionEnabled = true
			SetEntityProofs(playerPed, false, true, true, false, false, false, false, false)

			if isInVehicle then
				SetVehicleExplodesOnHighExplosionDamage(vehiclePedIsIn, true)
			end
		elseif isAntiExplotionEnabled then
			print('Anti-explosions: explosion ended!')
			SetEntityProofs(playerPed, false, false, false, false, false, false, false, false)

			if isInVehicle then
				SetVehicleExplodesOnHighExplosionDamage(vehiclePedIsIn, false)
			end
		end
	end
	
	--when godmod OFF	
	NetworkSetFriendlyFireOption(true)
	SetPedCanRagdoll(player, true)
	ClearPedLastWeaponDamage(player)
	SetEntityProofs(player, false, false, false, false, false, false, false, false)
end

local pinkcage = PolyZone:Create(Config.NoCrimePolyZone['Sandy'], {
	name="pink_cage",
	minZ=-126.4521,
	maxZ=2566.663,
	debugGrid=false,
	gridDivisions=25
})

function startNoCrimetimeGodmodSandy()
	while NoCrimetimeGODMODESandy do
		Citizen.Wait(500)
		local plyPed = PlayerPedId()
		if PlayerData.job.name == 'police' or PlayerData.job.name == 'ambulance' or PlayerData.job.name == 'admin' or PlayerData.job.name == 'agent' or PlayerData.job.name == 'mechanic' then
			-- Allowed
		else
			local coord = GetEntityCoords(plyPed)
			local inPoly = pinkcage:isPointInside(coord)
			if inPoly then
				if GetSelectedPedWeapon(plyPed) ~= GetHashKey("WEAPON_UNARMED") then
					SetCurrentPedWeapon(plyPed, GetHashKey("WEAPON_UNARMED"), true)
				end
				DisableControlAction(0, 37, true)
			end
		end
	end
end

local pinkcage4 = PolyZone:Create(Config.NoCrimePolyZone['Poleto'], {
	name="pink_cage_poleto",
	minZ=-126.4521,
	maxZ=2566.663,
	debugGrid=false,
	gridDivisions=25
})

function startNoCrimetimeGodmodPoleto()
	while NoCrimetimeGODMODEPoleto do
		Citizen.Wait(500)
		local plyPed = PlayerPedId()
		if PlayerData.job.name == 'police' or PlayerData.job.name == 'ambulance' or PlayerData.job.name == 'admin' or PlayerData.job.name == 'agent' or PlayerData.job.name == 'mechanic' then
			-- Allowed
		else
			local coord = GetEntityCoords(plyPed)
			local inPoly = pinkcage4:isPointInside(coord)
			if inPoly then
				if GetSelectedPedWeapon(plyPed) ~= GetHashKey("WEAPON_UNARMED") then
					SetCurrentPedWeapon(plyPed, GetHashKey("WEAPON_UNARMED"), true)
				end
				DisableControlAction(0, 37, true)
			end
		end
	end
end

local pinkcage2 = PolyZone:Create(Config.NoCrimePolyZone['Cayo'], {
	name="pink_cage_cayo",
	minZ=-126.4521,
	maxZ=2566.663,
	debugGrid=false,
	gridDivisions=25
})

function startNoCrimetimeGodmodCayo()
	while NoCrimetimeGODMODECayo do
		Citizen.Wait(500)
		local plyPed = PlayerPedId()
		if PlayerData.job.name == 'police' or PlayerData.job.name == 'ambulance' or PlayerData.job.name == 'admin' or PlayerData.job.name == 'agent' or PlayerData.job.name == 'mechanic' then
			-- Allowed
		else
			local coord = GetEntityCoords(plyPed)
			local inPoly = pinkcage2:isPointInside(coord)
			if inPoly then
				if GetSelectedPedWeapon(plyPed) ~= GetHashKey("WEAPON_UNARMED") then
					SetCurrentPedWeapon(plyPed, GetHashKey("WEAPON_UNARMED"), true)
				end
				DisableControlAction(0, 37, true)
			end
		end
	end
end

local pinkcage3 = PolyZone:Create(Config.NoCrimePolyZone['Los'], {
	name="pink_cage_los",
	minZ=-126.4521,
	maxZ=2566.663,
	debugGrid=false,
	gridDivisions=25
})

function startNoCrimetimeGodmodLos()
	while NoCrimetimeGODMODELos do
		Citizen.Wait(500)
		local plyPed = PlayerPedId()
		if PlayerData.job.name == 'police' or PlayerData.job.name == 'ambulance' or PlayerData.job.name == 'admin' or PlayerData.job.name == 'agent' or PlayerData.job.name == 'mechanic' then
			-- Allowed
		else
			local coord = GetEntityCoords(plyPed)
			local inPoly = pinkcage3:isPointInside(coord)
			if inPoly then
				if GetSelectedPedWeapon(plyPed) ~= GetHashKey("WEAPON_UNARMED") then
					SetCurrentPedWeapon(plyPed, GetHashKey("WEAPON_UNARMED"), true)
				end
				DisableControlAction(0, 37, true)
			end
		end
	end
end

local thread = false
RegisterNetEvent("esx_misc:updatePeacetimePeriod")
AddEventHandler("esx_misc:updatePeacetimePeriod", function(period)
    peacetimePeriod = period * 60

    if not thread then
        thread = true
        CreateThread(function()
            while peacetimePeriod > 0 do
                Wait(1000)
                if peacetimePeriod > 0 then
                    peacetimePeriod = peacetimePeriod - 1
                end
            end
            thread = false
        end)
    end
end)



function isNoCrimetime()
	local noCrimeZone = Config.panicButton.noCrimeZone
	local data = {}
	if alarm then
		for k,v in pairs (alarm) do
			if v.siren then
				for i=1,#noCrimeZone,1 do
					if v.location == noCrimeZone[i] then
						data.active = true
						data.location = v.location
						data.label = v.label
						return data
					end	
				end
			end
		end
	else
	--print('# no crime zone found')
		data.active = false
		return data
	end
end

function isNoCrimetime2()
	local noCrimeZone2 = Config.panicButton.noCrimeTime2
	local data2 = {}
	for k,v in pairs (NoCrimetime) do
		if v.active then
			for i=1,#noCrimeZone2,1 do
				if v.name == noCrimeZone2[i] then
					data2.active = true
					data2.name = v.name
					data2.label = v.label
					return data2
				end	
			end
		end
	end
	--print('# no crime zone found')
	data2.active = false
	return data2
end

function intialNotified()
	for i=1, #alarm, 1 do
		notified[i] = false
		alarm_leo_PlayWithinDistance[i] = false
	end
end

function showNotification(i)


	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end
	
	notified[i] = true
	local location, label = alarm[i].location, alarm[i].label
	local msg = nil
	local XPmsg = nil
	local start = true
	local data = nil
	
	if locationsData[location] ~= nil then
		data = locationsData[location]
	end
	
	if (MycurrentJobLeo or PlayerData.job == 'ambulance') then
		if data ~= nil and data.leo.notification ~= nil then
			msg = data.leo.notification
		end
	else
		if data ~= nil and data.citizen.notification ~= nil then
			msg = data.citizen.notification
		end
	end
	
	if msg == nil then
		if (MycurrentJobLeo or PlayerData.job == 'ambulance') then
		-- 	-- msg = locationsData['other'].leo.notification
		else
			if isInsideArea(i) then
				msg = locationsData['other'].citizen.notification
			end
		end	
	end
	
	local function startSendNotification()
		Citizen.CreateThread(function()
			if msg then
				for j=1, #msg, 1 do
					if location == 'peace_time' or 'restart_time' and alarm[i].siren then
						if start then
							start = false
							Citizen.Wait(3000)
							ESX.ShowNotification(msg[j]..label)
							PlaySoundFrontend(-1, "GOLF_NEW_RECORD", "HUD_AWARDS", true)
							
							-- while GetPlayerSwitchState() ~= 12 do
							-- 	Citizen.Wait(1)
							-- end
						else
							ESX.ShowNotification(msg[j])
							PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)
							Citizen.Wait(7000)
						end
					elseif alarm[i].siren then
						if start then
							start = false
							if not Dontnotified[i] then
							Citizen.Wait(2000)
							ESX.ShowNotification(msg[j]..label)
							PlaySoundFrontend(-1, "GOLF_NEW_RECORD", "HUD_AWARDS", true)
							Citizen.Wait(7000)
							else
							Dontnotified[i] = false
						end
						else
						if not Dontnotified[i] then
							ESX.ShowNotification(msg[j])
							PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)
							Citizen.Wait(7000)
							else
							Dontnotified[i] = false
						end
						end
					else
						if not Dontnotified[i] then
						ESX.ShowNotification('<font color=green>تم إلغاء الحالة في<font color=orange> '..label)
						PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)
						else
						Dontnotified[i] = false
						end
						break
					end
				end
			end
			if alarm[i].siren and XPmsg ~= nil then
				ESX.ShowNotification(XPmsg)
				PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)
			end
		end)
	end
	
	local function isNoXPzoneNotification(i)
		local found = false
		for j=1,#noXPzones,1 do
			if noXPzones[j] == location then
				found = true
				break
			end
		end
		if not found then
			XPmsg = 'منطقة استنفار<font color=orange> '..label..' </br><font color=#0094FF>تمنحك خبرة إضافية'
		end
	end
	
	if not ( MycurrentJobLeo or PlayerData.job == 'ambulance' )  then
		startSendNotification()
	elseif ( MycurrentJobLeo or PlayerData.job == 'ambulance' )  then
		isNoXPzoneNotification(i)
		startSendNotification()
	end

end

--trigger alarm
RegisterNetEvent("esx_misc:TriggerPanicButton")
AddEventHandler("esx_misc:TriggerPanicButton", function(location, siren, label)	
	if location ~= 'peace_time' and  location ~= 'sea_port_friday' and  location ~= 'blaine_meeting' then
		sendPhoneAlert(location, siren, label)
	end	
end)

-- Optimized main thread to control all zones and godmode
Citizen.CreateThread(function()
	Citizen.Wait(4000)
	while alarm == nil or PlayerData == nil or NoCrimetime == nil do
		Citizen.Wait(500)
	end

	local lastAlarmCheck = 0
	local lastNoCrimeCheck = 0
	local currentTime = 0

	while true do
		Citizen.Wait(100) -- Reduced from 1ms to 100ms for better performance
		currentTime = GetGameTimer()

		-- Check alarms every 250ms instead of every frame
		if currentTime - lastAlarmCheck > 250 then
			lastAlarmCheck = currentTime

			for i=1, #alarm, 1 do
				if isInsideArea(i) and alarm[i].siren then
					if not (MycurrentJobLeo or PlayerData.job == 'ambulance') and not notified[i] then
						showNotification(i)
						if isSirenZone(i) then
							TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 700, "risk2", 0.1)
						end
					elseif not alarm_leo_PlayWithinDistance[i] and isSirenZone(i) then
						TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 700, "risk2", 0.1)
						alarm_leo_PlayWithinDistance[i] = true
					end
				end
			end
		end

		-- Check NoCrimetime status every 500ms instead of every frame
		if currentTime - lastNoCrimeCheck > 500 then
			lastNoCrimeCheck = currentTime

			for i=1,#NoCrimetime,1 do
				if NoCrimetime[i].name == "NoCrimetime" and NoCrimetime[i].active and not NoCrimetimeGODMODE then
					NoCrimetimeGODMODE = true
					Citizen.CreateThread(function()
						startNoCrimetimeGodmod()
					end)

				elseif NoCrimetime[i].name == "NoCrimetimeSandy" and NoCrimetime[i].active and not NoCrimetimeGODMODESandy then
					NoCrimetimeGODMODESandy = true
					Wait(10)
					Citizen.CreateThread(function()
						startNoCrimetimeGodmodSandy()
					end)

				elseif NoCrimetime[i].name == "NoCrimetimePoleto" and NoCrimetime[i].active and not NoCrimetimeGODMODEPoleto then
					NoCrimetimeGODMODEPoleto = true
					Wait(10)
					Citizen.CreateThread(function()
						startNoCrimetimeGodmodPoleto()
					end)

				elseif NoCrimetime[i].name == "NoCrimetimeCayo" and  NoCrimetime[i].active and not NoCrimetimeGODMODECayo then
					NoCrimetimeGODMODECayo = true
					Wait(10)
					Citizen.CreateThread(function()
						startNoCrimetimeGodmodCayo()
					end)

				elseif NoCrimetime[i].name == "NoCrimetimeLos" and  NoCrimetime[i].active and not NoCrimetimeGODMODELos then
					NoCrimetimeGODMODELos = true
					Wait(10)
					Citizen.CreateThread(function()
						startNoCrimetimeGodmodLos()
					end)
				end
			end
		end
	end
end)

function isSirenZone(i)
	local location = alarm[i].location
	local noSirenZone = Config.panicButton.noSirenZone
	
	for i=1,#noSirenZone,1 do
		if noSirenZone[i] == location then
			return false
		end
	end
	
	return true
end

--optimized xp level checking
local giveXP = {}
local takeXP = {}
Citizen.CreateThread(function()
	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end

	local takeXPzones = Config.panicButton.takeXPzones

	local function isNoXPzones(i)
		for j=1,#noXPzones,1 do
			if alarm[i].location == noXPzones[j] then
				return false
			end
		end
		return true
	end

	local function isTakeXPzones(i)
		for j=1,#takeXPzones,1 do
			if alarm[i].location == takeXPzones[j] then
				return true
			end
		end
		return false
	end

	for i=1, #alarm, 1 do
		giveXP[i] = false
		takeXP[i] = false
	end

	local lastXPCheck = 0
	local currentTime = 0

	while true do
		Citizen.Wait(500) -- Reduced frequency from 1ms to 500ms
		currentTime = GetGameTimer()

		-- Check XP zones every 1 second instead of every frame
		if currentTime - lastXPCheck > 1000 then
			lastXPCheck = currentTime

			if ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) then
				for i=1, #alarm, 1 do
					if alarm[i] and alarm[i].siren and isInsideArea(i) and not giveXP[i] and isNoXPzones(i) then
						giveXP[i] = true
						startGiveXP(i)
					end
				end
			end

			if not ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) then
				for i=1, #alarm, 1 do
					if alarm[i] and alarm[i].siren and isInsideArea(i) and not takeXP[i] and isTakeXPzones(i) then
						takeXP[i] = true
						startTakeXP(i)
					end
				end
			end
		end
	end
end)

--give XP to leo jobs inside blip
function startGiveXP(i)
	Citizen.CreateThread(function()
		local waitime = Config.panicButton.giveXPtime
		local PlayerOnlineTime = -1
		local myxp = Config.panicButton.giveXPvalue
		
		while alarm[i].siren and isInsideArea(i) and ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) do
			PlayerOnlineTime = PlayerOnlineTime + 1
			if Config.dev_mod then print('# startGiveXP PlayerOnlineTime='..PlayerOnlineTime..' waitime='..waitime) end
			if PlayerOnlineTime == waitime then
				if Config.dev_mod then print('# zahya_xplevel:AddPlayerXP'..myxp) end
				TriggerServerEvent('zahya_xplevel:updateCurrentPlayerXP_clientSide', 'add', myxp, 'الأستنفارات: التواجد في منطقة تمنح خبرة إضافية')
				PlayerOnlineTime = 0
			end	
			
			Citizen.Wait(60000)
		end
		if Config.dev_mod then print('# STOP startGiveXP') end
		giveXP[i] = false
	end)
end

function startTakeXP(i)
	if exports.esx_jail.isPlayerJailed() then
		return
	end
	
	Citizen.CreateThread(function()
		local waitime = Config.panicButton.takeXPtime
		local PlayerOnlineTime = -1
		local myxp = Config.panicButton.takeXPvalue
		
		while alarm[i].siren and isInsideArea(i) and not ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) do
			PlayerOnlineTime = PlayerOnlineTime + 1
			if Config.dev_mod then print('# startTakeXP PlayerOnlineTime='..PlayerOnlineTime..' waitime='..waitime) end
			if PlayerOnlineTime == waitime then
				if Config.dev_mod then print('# zahya_xplevel:RemovePlayerXP = '..myxp) end
				TriggerServerEvent('zahya_xplevel:updateCurrentPlayerXP_clientSide', 'remove', myxp, 'الأستنفارات: التواجد في منطقة تخصم خبرة')
				ESX.ShowNotification('<font color=red>تم خصم <font color=orange> '..myxp..' <font color=red> من خبرتك</br>لتواجدك داخل منطقة محظورة')
				PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", true)
				PlayerOnlineTime = 0
				if myxp ~= 100 then
					myxp = myxp + 10
				end	
			end	
			
			Citizen.Wait(60000)
		end
		if Config.dev_mod then print('# STOP startTakeXP') end
		takeXP[i] = false
	end)
end

--citizen draw text values
local msg1 = {}
local color1 = {}
--leo draw text values
local msg2 = {}
local color2 = {}
--general draw text values
local font = 1 	-- 0 or 1
local scale = 0.4
local DrawTextYbase = 0.860

--optimized draw text citizen only
Citizen.CreateThread(function()
	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end

	for i=1, #alarm, 1 do
		citizenDrawTextStatus[i] = false
		msg1[i] = nil
		color1[i] = {}
	end

	local lastDrawCheck = 0
	local currentTime = 0

	while true do
		Citizen.Wait(250) -- Reduced frequency from 1ms to 250ms
		currentTime = GetGameTimer()

		-- Check draw text every 500ms instead of every frame
		if currentTime - lastDrawCheck > 500 then
			lastDrawCheck = currentTime

			if not ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) then
				for i=1, #alarm, 1 do
					if alarm[i] and alarm[i].siren and isInsideArea(i) and not citizenDrawTextStatus[i] then
						if alarm[i].location == 'sea_port_close' then
							if exports.esx_jail.isPlayerJailed() then
								return
							end
						end
						citizenDrawTextStatus[i] = true
						citizenDrawText(i)
						resetDrawTextY() --reset draw text Y axisend
					end
				end
			end
		end
	end
end)

function citizenDrawText(i)
	local check = false
	local data = nil
	
	if locationsData[alarm[i].location] ~= nil then
		data = locationsData[alarm[i].location]
		if data ~= nil and data.citizen.draw ~= nil and data.citizen.color ~= nil then	
			msg1[i] = '[ '..alarm[i].name..' ] '.. data.citizen.draw 
			color1[i] = data.citizen.color
			check = true
		end
	end
	
	if not check then
		msg1[i] = '[ '..alarm[i].name..' ] '..locationsData['other'].citizen.draw
		color1[i] = locationsData['other'].citizen.color
	end
	
	--start draw
	Citizen.CreateThread(function()
		Citizen.Wait(100)
		
		while alarm[i].siren and isInsideArea(i) and not ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) do
			Citizen.Wait(0)
			
			
			if not hidehud then
				SetTextColour(color1[i].r,color1[i].g,color1[i].b,color1[i].a)
				SetTextFont(fontId)
				SetTextScale(scale, scale)
				SetTextWrap(0.0, 1.0)
				SetTextCentre(true)
				SetTextDropshadow(2, 2, 0, 0, 0)
				SetTextEdge(1, 0, 0, 0, 205)
				SetTextEntry("STRING")
				if isAlarmTimerZone(i) then	
					AddTextComponentString('['..formatTime(peacetimePeriod)..']  '..msg1[i])
				else	
					AddTextComponentString(msg1[i])
				end
				DrawText(0.500, DrawTextY[i])
			end
		end
		
		citizenDrawTextStatus[i] = false
		resetDrawTextY()
	end)
end

-- Citizen.CreateThread(function()
-- 	while alarm == nil or PlayerData == nil do
-- 		Citizen.Wait(500)
-- 	end
	
	
	
	
-- 	for i=1, #alarm, 1 do
-- 		if isInsideArea(i) and alarm[i].siren then 
-- 	Citizen.Wait(0)

		
-- 	end


-- end



--optimized blip and leo draw text for police
Citizen.CreateThread(function()
	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end

	for i=1, #alarm, 1 do
		blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
		SetBlipAlpha(blipDetail[i], 0)
		LeoDrawTextStatus[i] = false
		DrawTextY[i] = 0.0
		godmodStarted[i] = false
		color2[i] = {}
		msg2[i] = nil
	end

	local lastBlipCheck = 0
	local currentTime = 0

	while true do
		Citizen.Wait(150) -- Reduced frequency from 1ms to 150ms
		currentTime = GetGameTimer()

		-- Check blips and leo text every 300ms instead of every frame
		if currentTime - lastBlipCheck > 300 then
			lastBlipCheck = currentTime

			for i=1, #alarm, 1 do
				if alarm[i] and alarm[i].siren then
					if not blipStatus[i] then
						blipStatus[i] = true
						startBlip(i)
					end

					if ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) then
						if not LeoDrawTextStatus[i] then
							LeoDrawTextStatus[i] = true
							LeoDrawText(i) --call draw text for police function
							resetDrawTextY() --reset draw text Y axis
						end

						if not notified[i] then
							showNotification(i)
						end
					end
				elseif alarm[i] and not alarm[i].siren and blipStatus[i] then
					blipStatus[i] = false
					SetBlipAlpha(blipDetail[i], 0)
				end
			end
		end
	end
end)

--start blip
function startBlip(i)
	while alarm[i].coords == nil do
		Citizen.Wait(0)
	end	
	
	--[[add this for alarm use current player location
	if alarm[i].location == 'my_location' then
		blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'my_location_safezone' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'helpme' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'event_start' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'event_location' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'event_registration' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'event_end' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	elseif alarm[i].location == 'restricted_area' then
	    blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	end]]
	
	blipDetail[i] = AddBlipForRadius(alarm[i].coords, alarm[i].dist)
	
	SetBlipHighDetail(blipDetail[i], true)
	SetBlipAlpha(blipDetail[i], 100)
	SetBlipAsShortRange(blipDetail[i], true)
	
	falshBlipcolor(i) --call blip color function
	
end

--blip function blip color
function falshBlipcolor(i)
	Citizen.CreateThread(function()
		local red = blipColor.red
		local blue = blipColor.blue
		local blipColor = blue
		local check = false
		local data = nil
		
		if locationsData[alarm[i].location] ~= nil then
			data = locationsData[alarm[i].location]
			if data.blipColor ~= nil and data.location == alarm[i].location then
				blipcolor = data.blipColor
				check = true
			end
		end	
		
		if check then
			local alpha = 100
			SetBlipColour(blipDetail[i], blipcolor) --set blip color
			
			while (alarm[i] and alarm[i].siren) do --make blip alpha glow
				while alpha <= 140 do
					alpha = alpha + 1
					SetBlipAlpha(blipDetail[i], alpha)
					
					Citizen.Wait(1)
				end
				
				while alpha >= 60 do
					alpha = alpha - 1
					SetBlipAlpha(blipDetail[i], alpha)
					
					Citizen.Wait(1)
				end
				
				Citizen.Wait(500)
			end	
			
			SetBlipAlpha(blipDetail[i], 0)	
		else
			while (alarm[i] and alarm[i].siren) do
				if blipColor == blue then
					blipColor = red
					SetBlipColour(blipDetail[i], blipColor)
				else
					blipColor = blue
					SetBlipColour(blipDetail[i], blipColor)
				end	
				Citizen.Wait(500)
			end	
			
			SetBlipAlpha(blipDetail[i], 0)
		end
	end)
end

--draw text for police function
function LeoDrawText(i)
	local check = false
	local data = nil
		
	if locationsData[alarm[i].location] ~= nil then
		data = locationsData[alarm[i].location]
		if data.leo.draw ~= nil and data.leo.color ~= nil and data.location == alarm[i].location then
			msg2[i] = '[ '..alarm[i].name..' ] '.. data.leo.draw
			color2[i] = data.leo.color
			check = true
		end
	end
	
	if not check then
		msg2[i] = '[ '..alarm[i].name..' ] '..locationsData.other.leo.draw
		color2[i] = locationsData.other.leo.color
	end
	
	Citizen.CreateThread(function()
		Citizen.Wait(100)
		
		while (alarm[i] and alarm[i].siren) and ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) do
		--while alarm[i].siren and ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) or isInsideArea(i) do
			Citizen.Wait(0)
			if not hidehud and (alarm[i] and alarm[i].siren) then
				SetTextColour(color2[i].r,color2[i].g,color2[i].b,color2[i].a)
				SetTextFont(fontId)
				SetTextScale(scale, scale)
				SetTextWrap(0.0, 1.0)
				SetTextCentre(true)
				SetTextDropshadow(2, 2, 0, 0, 0)
				SetTextEdge(1, 0, 0, 0, 205)
				SetTextEntry("STRING")
				if isAlarmTimerZone(i) then
					AddTextComponentString('['..formatTime(peacetimePeriod)..']  '..msg2[i])
				else
					AddTextComponentString(msg2[i])
				end	
				DrawText(0.500, DrawTextY[i])
			end
		end	
		
		LeoDrawTextStatus[i] = false
		resetDrawTextY()
	end)
end

function formatTime(seconds)
	local hours = math.floor(seconds / 3600)
	local minutes = math.floor((seconds % 3600) / 60)
	local remainingSeconds = seconds % 60
  
	local formattedTime = string.format("%02d:%02d:%02d", hours, minutes, remainingSeconds)
	return formattedTime
  end

function resetDrawTextY()
	local count = 1
	local k = 0
	local y
	for i=1, #alarm, 1 do
		if alarm[i].siren then
			if count == 1 then
				DrawTextY[i] = DrawTextYbase
				y = DrawTextYbase
			else
				y = y - 0.025
				DrawTextY[i] = y
			end
			count = count + 1
		end
	end	
end

--check if inside alert radiuas dist
function isInsideArea(i)
	local ped = PlayerPedId()
	local pedCoords = GetEntityCoords(ped)
	local check = 0
	
	check = GetDistanceBetweenCoords(pedCoords, alarm[i].coords, true)
	
	if check <= alarm[i].dist then
		return true
	else
		return false
	end		
end	

--send alert phone msg to police --DISABLE NOW FOR GCC PHONE
function sendPhoneAlert(location, siren, label)
	local x,y,z = table.unpack(GetEntityCoords(PlayerPedId(), false))
	
	local msg = nil
	local msg2 = nil
	
	if location == 'hacker' then 
		msg = 'هذا بلاغ تلقائي.. تم اطلاق صافرة الانذار وحالة الاستنفار الأمني لمكافحة'..label..' من قبل: '
		msg2 = 'هذا بلاغ تلقائي.. تم إلغاء حالة الاستنفار الأمني لمكافحة '..label..' من قبل: '
	elseif location == 'my_location_safezone' then 
		msg = 'هذا بلاغ تلقائي.. تم اعلان حالة '..label..' من قبل: '
		msg2 = 'هذا بلاغ تلقائي.. تم إلغاء حالة '..label..' من قبل: '
	elseif location == 'peace_time' then 
		msg = 'هذا بلاغ تلقائي.. تم اعلان '..label..' من قبل: '
		msg2 = 'هذا بلاغ تلقائي.. تم انتهاء '..label..' من قبل: '
	elseif location == 'helpme' then 
		msg = 'هذا بلاغ تلقائي.. '..label..' من قبل: '
		msg2 = 'هذا بلاغ تلقائي.. تم انهاء '..label..' من قبل: '
	else
		msg = 'هذا بلاغ تلقائي.. تم اطلاق صافرة الانذار وحالة الاستنفار الأمني في '..label..' من قبل: '
		msg2 = 'هذا بلاغ تلقائي.. تم إلغاء حالة الاستنفار الأمني في '..label..' من قبل: '
	end
	
	
	for i=1, #LeoJobs, 1 do
		if siren then
			TriggerServerEvent('esx_phone:send', LeoJobs[i], msg..PlayerData.job.label, true, {x =x, y =y, z =z})
		else
			TriggerServerEvent('esx_phone:send', LeoJobs[i], msg2..PlayerData.job.label, true, {x =x, y =y, z =z})
		end
	end	
end

--check if player job is leo
--function ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) this var defined in Config and used in "main_esx.lua"

function checkSkyTransitionANDgodmod(i)
--[[
	if isSkyTransitionZone(i) then
			canUseTransition[i] = true
		if Config.dev_mod then print('# canUseTransition is true for: '..alarm[i].location) end
		else
		if Config.dev_mod then print('# canUseTransition is false for: '..alarm[i].location) end
			canUseTransition[i] = false
		end]]
		
		--godmodStarted[i] = false
		--transitionStarted[i] = false
		
			if alarm[i].location == 'restart_time' or alarm[i].location == 'hacker' or alarm[i].location == 'my_location_safezone' or alarm[i].location == 'peace_time' then
				canUseGodmod[i] = true
			else
				canUseGodmod[i] = false
			end
		
		
		if alarm[i].location ~= 'helpme' or alarm[i].location ~= 'other' then
            if isSkyTransitionZone(i) and not canUseTransition[i] then
                canUseTransition[i] = true
                --print('canUseTransition is true for: '..alarm[i].location)
            elseif canUseTransition[i] then
            --print('canUseTransition is true for: '..alarm[i].location)
            canUseTransition[i] = true
            else
            --print('canUseTransition is false for: '..alarm[i].location)
                canUseTransition[i] = false
            end
        else
            canUseTransition[i] = false
        end
end

--godmod & sky transition
--optimized godmod & sky transition
Citizen.CreateThread(function()
	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end

	local lastGodmodCheck = 0
	local currentTime = 0

	while true do
		Citizen.Wait(200) -- Reduced frequency from 1ms to 200ms
		currentTime = GetGameTimer()

		-- Check godmod and transitions every 500ms instead of every frame
		if currentTime - lastGodmodCheck > 500 then
			lastGodmodCheck = currentTime

			for i=1, #alarm, 1 do
				if alarm[i] and alarm[i].siren then -- Add nil check
					--god mod
					if canUseGodmod[i] and not godmodStarted[i] and isInsideArea(i) then
						startGodmod(i)
					end

					--sky transition
					if not transitionStarted[i] and canUseTransition[i] then
						transitionStarted[i] = true
						if not exports.esx_visa:IsPlayerVisaTesting() then
							TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 1, "peace_time", 0.8)
							doSkyTransition()
						end
					end
				elseif alarm[i] and not alarm[i].siren then -- Add nil check
					if transitionStarted[i] and canUseTransition[i] then
						transitionStarted[i] = false
						if not exports.esx_visa:IsPlayerVisaTesting() then
							TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 1, "peace_time", 0.8)
							doSkyTransition()
						end
					end
				end
			end
		end
	end
end)



--[[Citizen.CreateThread(function()
	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end
	
	--[[
	for i=1, #alarm, 1 do
		godmodStarted[i] = false
		transitionStarted[i] = false
		
		for k=1, #godmodLocations, 1  do
			if godmodLocations[k] == alarm[i].location then
				canUseGodmod[i] = true
			else
				canUseGodmod[i] = false
			end
		end
		
		
		if isSkyTransitionZone(i) then
			canUseTransition[i] = true
			print('canUseTransition is true for: '..alarm[i].location)
		else
		print('canUseTransition is false for: '..alarm[i].location)
			canUseTransition[i] = false
		end
		
	
	
	while true do
		Citizen.Wait(1)
		for i=1, #alarm, 1 do
			--god mod
			if canUseGodmod[i] and alarm[i].siren then
				if not godmodStarted[i] and isInsideArea(i) then
					startGodmod(i)
				end
			end
		
			--sky transition
			if not transitionStarted[i] and alarm[i].siren and canUseTransition[i] then
				transitionStarted[i] = true
				if not exports.esx_visa:IsPlayerVisaTesting() then
					TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 1, "peace_time", 0.8)
					doSkyTransition()
				end	
			elseif transitionStarted[i] and not alarm[i].siren and  canUseTransition[i] then
				transitionStarted[i] = false
				if not exports.esx_visa:IsPlayerVisaTesting() then
					TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 1, "peace_time", 0.8)
					doSkyTransition()
				end	
			end
		end
	end	
end)]]


function isSkyTransitionZone(i)
	for k,v in pairs(Config.panicButton.skyTransitionLocations) do
		if alarm[i].location == v then
			return true
		end	
	end
end

function isAlarmTimerZone(i)
	for k,v in pairs(Config.panicButton.alarmTIMER) do
		if alarm[i].location == v then
			return true
		end	
	end
end

function startGodmod(i)
	godmodStarted[i] = true
	local isAntiExplotionEnabled, previousVehicleModel, previousVehicleHandle
	
	Citizen.CreateThread(function()
	--when godmod ON		
		local player = PlayerPedId()
		local ped = PlayerId()
		
		--safezone
		ClearPlayerWantedLevel(PlayerId())
		SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true)
		--godmode
		SetPedCanRagdoll(PlayerPedId(), false)
		
		while isInsideArea(i) and alarm[i].siren do
			
			Citizen.Wait(0)

			--safezone
			NetworkSetFriendlyFireOption(false)
				
			--godmode
			-- print('true god mode 2')
			SetPlayerInvincible(ped, true)
			-- SetEntityInvincible(player, true)
			-- ClearPedBloodDamage(player)
			-- ResetPedVisibleDamage(player)
			-- ClearPedLastWeaponDamage(player)
			-- SetEntityProofs(player, true, true, true, true, true, true, true, true)
			-- SetEntityOnlyDamagedByPlayer(player, false)
			-- SetEntityCanBeDamaged(player, false)
			-- SetEntityHealth(PlayerPedId(-1), 200)
			
			-- NetworkRequestControlOfEntity(GetVehiclePedIsIn(-1))
			-- SetVehicleEngineHealth(GetVehiclePedIsIn(player, false), 1000.0)
			--SetVehicleMaxSpeed(GetVehiclePedIsIn(player, false), 50)
			--SetVehicleFixed(GetVehiclePedIsIn(player, false))
			--SetVehicleDirtLevel(GetVehiclePedIsIn(player, false), 0.0)
			--SetVehicleLights(GetVehiclePedIsIn(player, false), 0)
			--SetVehicleBurnout(GetVehiclePedIsIn(player, false), false)
			--Citizen.InvokeNative(0x1FD09E7390A74D54, GetVehiclePedIsIn(player, false), 0)
			
			--safezone
			if PlayerData.job.name ~= 'admin' then
				DisableControlAction(2, 37, true) -- disable weapon wheel (Tab)
				DisablePlayerFiring(player,true) -- Disables firing all together if they somehow bypass inzone Mouse Disable
				DisableControlAction(0, 106, true) -- Disable in-game mouse controls
				DisableControlAction(0, 140, true) -- Disable Reload and melle
				DisableControlAction(0, 141, true) -- Disable melle
			end
			
			--[[ -- تحت العمل من عبدالرحمن
			if GetSelectedPedWeapon(ped) == 883325847 or GetSelectedPedWeapon(ped) == GetHashKey("WEAPON_UNARMED") or GetSelectedPedWeapon(ped) == GetHashKey("weapon_fireextinguisher") then
			SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true)
			end]]
			
			--anti explosion
			local playerPed = PlayerPedId()
			local playerCoords = GetEntityCoords(playerPed)
			local isInVehicle, vehiclePedIsIn = IsPedInAnyVehicle(playerPed, false)
	
			if isInVehicle then vehiclePedIsIn = GetVehiclePedIsIn(playerPed, false) end
	
			-- explosion anti cheat
			if IsExplosionInSphere(8, playerCoords.x, playerCoords.y, playerCoords.z, 50.0) or IsExplosionInSphere(31, playerCoords.x, playerCoords.y, playerCoords.z, 50.0) then
				print('Anti-explosions: explosion detected!')
				isAntiExplotionEnabled = true
				SetEntityProofs(playerPed, false, true, true, false, false, false, false, false)
	
				if isInVehicle then
					SetVehicleExplodesOnHighExplosionDamage(vehiclePedIsIn, true)
				end
			elseif isAntiExplotionEnabled then
				print('Anti-explosions: explosion ended!')
				SetEntityProofs(playerPed, false, false, false, false, false, false, false, false)
	
				if isInVehicle then
					SetVehicleExplodesOnHighExplosionDamage(vehiclePedIsIn, false)
				end
			end
	
			--[[model change anti cheat
			if isInVehicle then
				local vehicleModel = GetEntityModel(vehicle)
	
				if vehicle == previousVehicleHandle and vehicleModel ~= previousVehicleModel and previousVehicleModel then
					--print('Detected vehicle model swap!')
					TriggerServerEvent('blaine_blabla:foundhacker', 'vehicle_swap', vehicleModel, previousVehicleModel)
					DeleteEntity(vehicle)
				end
			end]]
		end
	
	--when godmod OFF	
		--safezone
		-- Wait(3000)
		-- while GetPlayerSwitchState() ~= 12 do
		-- 	Wait(1000)
		-- 	print('waiting')
		-- end
		NetworkSetFriendlyFireOption(true)
		--godmode
		local player = PlayerPedId()
		local ped = PlayerId()
		SetPlayerInvincible(PlayerId(), false)
		SetEntityInvincible(PlayerPedId(), false)
		SetPedCanRagdoll(PlayerPedId(), true)
		ClearPedLastWeaponDamage(player)
		SetEntityProofs(player, false, false, false, false, false, false, false, false)
		-- SetEntityOnlyDamagedByPlayer(player, true)
		SetEntityCanBeDamaged(player, true)
		
		godmodStarted[i] = false
		
	end)	
end

--optimized help me F10 system
Citizen.CreateThread(function()
	while alarm == nil or PlayerData == nil do
		Citizen.Wait(500)
	end

	local counter = 4
	local isDrawingCounter = false

	local function DrawCounter()
		if isDrawingCounter then return end
		isDrawingCounter = true

		Citizen.CreateThread(function()
			while IsControlPressed(0, 57) and counter ~= 0 do
				Citizen.Wait(50) -- Reduced frequency from 2ms to 50ms

				SetTextColour(255,0,0,150)
				SetTextFont(fontId)
				SetTextScale(1.0, 1.0)
				SetTextWrap(0.0, 1.0)
				SetTextCentre(true)
				SetTextDropshadow(2, 2, 0, 0, 0)
				SetTextEdge(1, 0, 0, 0, 205)
				SetTextEntry("STRING")
				AddTextComponentString(counter..' ﺔﺛﺎﻐﺘﺳﺍ ﺀﺍﺪﻧ')
				DrawText(0.500, 0.500)
				if counter == 0 then
					break
				end
			end
			isDrawingCounter = false
		end)
	end

	while true do
		Citizen.Wait(50) -- Reduced from 0ms to 50ms for better performance

		if IsControlPressed(0, 57) and GetLastInputMethod(0) and ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) then
			counter = 4
			DrawCounter()

			while IsControlPressed(0, 57) and counter ~= 0 do
				counter = counter - 1
				Citizen.Wait(1000)
			end

			if counter ~= 0 then
				ESX.ShowNotification('<font color=orange>نداء الإستغاثة</font></br>لمدة 3 ثواني '..'F10'..' استمر بضغط')
			end
		end

		if counter == 0 then
			local ped = PlayerPedId()
			local pedCoords = GetEntityCoords(ped)
			print('send help me')
			TriggerEvent("zahya_police_safe_zone:TogglePanicButton", pedCoords, 'helpme', 100.0)

			Citizen.CreateThread(function()
				local reset = helpmetime

				while helpmetime ~= 0 do
					helpmetime = helpmetime -1
					Citizen.Wait(1000)
				end

				Citizen.Wait(100)
				helpmetime = reset
			end)

			while IsControlPressed(0, 57) do
				Citizen.Wait(100)
			end

			Citizen.Wait(1000)

			while helpmetime ~= 0 do
				Citizen.Wait(100) -- Reduced from 0ms to 100ms

				if IsControlJustReleased(0, 57) and GetLastInputMethod( 0 ) and ( MycurrentJobLeo or PlayerData.job == 'ambulance' ) then
					ESX.ShowNotification('<font color=red>نداء الاستغاثة</font> عليك الانتظار: <font color=orange>'..helpmetime..'</font> ثانية')
				end
			end
			counter = 4
		end
	end
end)

--- transition code ---
------- Configurable options  -------

-- set the opacity of the clouds
local cloudOpacity = 0.01 -- (default: 0.01)

-- setting this to false will NOT mute the sound as soon as the game loads 
-- (you will hear background noises while on the loading screen, so not recommended)
local muteSound = true -- (default: true)


------- Code -------
-- Mutes or un-mutes the game's sound using a short fade in/out transition.
function ToggleSound(state)
    if state then
        StartAudioScene("MP_LEADERBOARD_SCENE");
    else
        StopAudioScene("MP_LEADERBOARD_SCENE");
    end
end

-- Runs the initial setup whenever the script is loaded.
function InitialSetup()
    -- Disable sound (if configured)
    ToggleSound(muteSound)
    -- Switch out the player if it isn't already in a switch state.
    if not IsPlayerSwitchInProgress() then
        SwitchOutPlayer(PlayerPedId(), 0, 1)
    end
end


-- Hide radar & HUD, set cloud opacity, and use a hacky way of removing third party resource HUD elements.
function ClearScreen()
    SetCloudHatOpacity(cloudOpacity)
    HideHudAndRadarThisFrame()
    
    -- nice hack to 'hide' HUD elements from other resources/scripts. kinda buggy though.
    SetDrawOrigin(0.0, 0.0, 0.0, 0)
end

-- Sometimes this gets called too early, but sometimes it's perfectly timed,
-- we need this to be as early as possible, without it being TOO early, it's a gamble!

function doSkyTransition()
	ESX.UI.Menu.CloseAll()
	TriggerEvent("esx_misc:hidehud", true) -- hide hud in switch state
	
	Citizen.CreateThread(function()	
		while GetPlayerSwitchState() ~= 12 do
			Citizen.Wait(0)

			if PlayerData.job.name ~= 'admin' then
			DisableAllControlActions(0)
		end
		end
	end)
	
	local playerPed = PlayerPedId()
	local veh =
	
	FreezeEntityPosition(playerPed, true)
	if IsInVehicle() then
		veh = GetVehiclePedIsIn(playerPed)
		FreezeEntityPosition(veh, true)
	end	
	-- In case it was called too early before, call it again just in case.
	InitialSetup()
		
	-- Wait for the switch cam to be in the sky in the 'waiting' state (5).
	while GetPlayerSwitchState() ~= 5 do
		Citizen.Wait(0)
		ClearScreen()
	end
	
	ClearScreen()
	Citizen.Wait(0)
	DoScreenFadeOut(0)
	
	ClearScreen()
	Citizen.Wait(0)
	ClearScreen()
	DoScreenFadeIn(500)
	while not IsScreenFadedIn() do
		Citizen.Wait(0)
		ClearScreen()
	end
	
	local timer = GetGameTimer()
	
	--FreezeEntityPosition(PlayerPedId(), true)
	
	local delaytime = 15 * 1000
	
	-- Re-enable the sound in case it was muted.
	ToggleSound(false)
	ShowLoadingPromt('Loading', delaytime, 3) -- loading icon
	while true do
		Citizen.Wait(0)
		ClearScreen()
		
		-- wait 20 seconds before starting the switch to the player
		if GetGameTimer() - timer > delaytime then
			
			-- Switch to the player.
			SwitchInPlayer(PlayerPedId())
			
			ClearScreen()
			
			-- Wait for the player switch to be completed (state 12).
			while GetPlayerSwitchState() ~= 12 do
				Citizen.Wait(0)
				ClearScreen()
			end
			-- Stop the infinite loop.
			break
		end
	end
	
	-- Reset the draw origin, just in case (allowing HUD elements to re-appear correctly)
	TriggerEvent("esx_misc:hidehud", false) -- show hud after switch state
	
	ClearDrawOrigin()
	Citizen.Wait(500)
	SetEntityVisible(PlayerPedId(), true)
	
	FreezeEntityPosition(playerPed, false)
	if IsInVehicle() then
		veh = GetVehiclePedIsIn(playerPed)
		FreezeEntityPosition(veh, false)
	end
end

function ShowLoadingPromt(msg, time, type)
  Citizen.CreateThread(function()
    Citizen.Wait(0)
    BeginTextCommandBusyString("STRING")
    AddTextComponentString(msg)
    EndTextCommandBusyString(type)
    Citizen.Wait(time)
    RemoveLoadingPrompt()
  end)
end

function IsInVehicle ()
  return GetPedInVehicleSeat(GetVehicle(), -1)
end

function GetVehicle ()
  return GetVehiclePedIsIn(PlayerPedId(), false)
end

--[[RegisterNetEvent('esx_misc:startTimer')
AddEventHandler('esx_misc:startTimer', function(i, peacetime)
	timer = peacetime
	
	Citizen.CreateThread(function()
		while timer > 0 and alarm[i].siren do
			Citizen.Wait(0)
			Citizen.Wait(1000)
			if(timer > 0)then
				timer = timer - 1
			end
		end
	end)
	
	Citizen.CreateThread(function()
		while alarm[i].siren do
			Citizen.Wait(0)
			--drawTxt(0.675, 1.22, 1.0,1.0,0.36, msg..timer, 255, 255, 255, 255)
			if GetPlayerSwitchState() == 12 then
				--label
				SetTextColour(138,0,198,255)
				SetTextFont(font)
				SetTextScale(0.4, 0.4)
				SetTextWrap(0.0, 1.0)
				SetTextCentre(false)
				SetTextDropshadow(2, 2, 0, 0, 0)
				SetTextEdge(1, 0, 0, 0, 205)
				SetTextEntry("STRING")
				AddTextComponentString('Remaining time ['..timer..']')
				DrawText(0.415, 0.010)
			end
		end
	end)
end)]]
