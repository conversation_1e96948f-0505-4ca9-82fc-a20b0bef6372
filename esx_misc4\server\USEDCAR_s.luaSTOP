ESX.RegisterServerCallback('esx_misc:Get_vehicle_info', function(source, cb, model)
    MySQL.Async.fetchAll("SELECT * FROM vs_cars WHERE model = @model", {['@model'] = model}, function(result)
        for _,v in pairs(result) do
            cb(v)
        end
    end)
end)

--get vehicle props for spawn cars
ESX.RegisterServerCallback('esx_misc:getVehiclePropps', function(source, cb)
    MySQL.Async.fetchAll("SELECT * FROM vehicles_for_sale", {
    }, function(result)
        local vehicles = {}
        for i = 1, #result, 1 do
            if result[i] ~= nil then
                table.insert(vehicles, {props = json.decode(result[i].props), price = result[i].price, message = result[i].message})
            end
        end
        cb(vehicles)
    end)
end)

--sell vehicle
RegisterNetEvent('esx_misc:SetInfoAndPrice')
AddEventHandler('esx_misc:SetInfoAndPrice', function(textA, PriceA, plate, props)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.execute('INSERT INTO vehicles_for_sale (message, price, plate, owner, Time, props) VALUES (@message, @price, @plate, @owner, @Time, @props)',
    {
        ['@message']    = textA,
        ['@price']      = PriceA,
        ['@owner']      = xPlayer.identifier,
        ['@Time']       = 1440,
        ['@props']      = json.encode(props),
        ['@plate']      = plate,
    })
end)

--for ADS price
RegisterNetEvent('esx_misc:RemoveADSMoney')
AddEventHandler('esx_misc:RemoveADSMoney', function(price)
    local xPlayer = ESX.GetPlayerFromId(source)

   xPlayer.removeMoney(price)
end)

--Buy Car
 RegisterNetEvent('esx_misc:USEDCARS:BuyCar')
 AddEventHandler('esx_misc:USEDCARS:BuyCar', function(plate, pricee, props)
     local xPlayer = ESX.GetPlayerFromId(source)

     MySQL.Async.fetchAll("SELECT * FROM vehicles_for_sale WHERE plate=@plate", {
         ['@plate'] = plate
     }, function(result)
         if result[1] ~= nil then
             if xPlayer.getMoney() >= pricee then

                MySQL.Async.execute('DELETE FROM vehicles_for_sale WHERE plate = @plate', {
                     ['@plate'] = plate
                 })
        
                 MySQL.Async.execute('UPDATE owned_vehicles SET owner=@owner WHERE plate = @plate', {
                     ['@owner'] = xPlayer.identifier,
                     ['@plate'] = plate,
                 }, function(rowsChanged)
                     xPlayer.showNotification('تم نقل ملكية المركبة بأسمك <font color=yellow>'.. plate .. '</font>')
                 end)

                 --Spawn Vehicle
                 TriggerClientEvent('esx_misc:UsedCar:SpwanCarOutSideWhenBuy', source, props)
             else
                 xPlayer.showNotification('<font color=red>لا تملك النقود الكافية لشراء المركبة</font>')
            end
         else
             xPlayer.showNotification('<font color=red>معرض المستعمل </font></br>هذه المركبة تمت إزالتها أو تم شراؤها')
         end
    end)

 end)


--Optimized Vehicle Deletion - Reduced frequency and batch operations
Citizen.CreateThread( function()
    while true do
        Citizen.Wait(5*60*1000) -- Changed from every minute to every 5 minutes

        -- Use a more efficient query to only get vehicles that need processing
        MySQL.Async.fetchAll("SELECT plate, owner, Time FROM vehicles_for_sale WHERE Time > 0 AND Time <= 1440", {}, function(result)
            if result and #result > 0 then
                local updateQueries = {}
                local deleteQueries = {}
                local notifications = {}

                for _, v in pairs(result) do
                    if v.Time > 1 then
                        -- Batch update queries
                        table.insert(updateQueries, {
                            query = "UPDATE vehicles_for_sale SET Time = @Time WHERE plate = @plate",
                            parameters = {
                                ['@Time'] = v.Time - 5, -- Subtract 5 minutes since we check every 5 minutes
                                ['@plate'] = v.plate,
                            }
                        })
                    else
                        -- Batch delete queries
                        table.insert(deleteQueries, {
                            query = "DELETE FROM vehicles_for_sale WHERE plate = @plate",
                            parameters = {
                                ['@plate'] = v.plate
                            }
                        })

                        -- Store notification data
                        table.insert(notifications, {
                            owner = v.owner,
                            plate = v.plate
                        })
                    end
                end

                -- Execute batch updates
                if #updateQueries > 0 then
                    for _, queryData in pairs(updateQueries) do
                        MySQL.Async.execute(queryData.query, queryData.parameters)
                    end
                end

                -- Execute batch deletes
                if #deleteQueries > 0 then
                    for _, queryData in pairs(deleteQueries) do
                        MySQL.Async.execute(queryData.query, queryData.parameters)
                    end
                end

                -- Send notifications after database operations
                if #notifications > 0 then
                    Citizen.CreateThread(function()
                        for _, notif in pairs(notifications) do
                            local xTarger = ESX.GetPlayerFromIdentifier(notif.owner)
                            if xTarger ~= nil then
                                xTarger.showNotification('<font color=#FA3737>معرض المستعمل : </font>تم نقل مركبتك '..tostring(notif.plate)..' إلى الكراج العمومي')
                            end
                        end
                    end)
                end
            end
        end)
    end
end)

