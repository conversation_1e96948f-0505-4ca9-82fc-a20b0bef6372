ESX.RegisterServerCallback('esx_misc:Get_vehicle_info', function(source, cb, model)
    MySQL.Async.fetchAll("SELECT * FROM vs_cars WHERE model = @model", {['@model'] = model}, function(result)
        for _,v in pairs(result) do
            cb(v)
        end
    end)
end)

--get vehicle props for spawn cars
ESX.RegisterServerCallback('esx_misc:getVehiclePropps', function(source, cb)
    MySQL.Async.fetchAll("SELECT * FROM vehicles_for_sale", {
    }, function(result)
        local vehicles = {}
        for i = 1, #result, 1 do
            if result[i] ~= nil then
                table.insert(vehicles, {props = json.decode(result[i].props), price = result[i].price, message = result[i].message})
            end
        end
        cb(vehicles)
    end)
end)

--sell vehicle
RegisterNetEvent('esx_misc:SetInfoAndPrice')
AddEventHandler('esx_misc:SetInfoAndPrice', function(textA, PriceA, plate, props)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.execute('INSERT INTO vehicles_for_sale (message, price, plate, owner, Time, props) VALUES (@message, @price, @plate, @owner, @Time, @props)',
    {
        ['@message']    = textA,
        ['@price']      = PriceA,
        ['@owner']      = xPlayer.identifier,
        ['@Time']       = 1440,
        ['@props']      = json.encode(props),
        ['@plate']      = plate,
    })
end)

--for ADS price
RegisterNetEvent('esx_misc:RemoveADSMoney')
AddEventHandler('esx_misc:RemoveADSMoney', function(price)
    local xPlayer = ESX.GetPlayerFromId(source)

   xPlayer.removeMoney(price)
end)

--Buy Car
 RegisterNetEvent('esx_misc:USEDCARS:BuyCar')
 AddEventHandler('esx_misc:USEDCARS:BuyCar', function(plate, pricee, props)
     local xPlayer = ESX.GetPlayerFromId(source)

     MySQL.Async.fetchAll("SELECT * FROM vehicles_for_sale WHERE plate=@plate", {
         ['@plate'] = plate
     }, function(result)
         if result[1] ~= nil then
             if xPlayer.getMoney() >= pricee then

                MySQL.Async.execute('DELETE FROM vehicles_for_sale WHERE plate = @plate', {
                     ['@plate'] = plate
                 })
        
                 MySQL.Async.execute('UPDATE owned_vehicles SET owner=@owner WHERE plate = @plate', {
                     ['@owner'] = xPlayer.identifier,
                     ['@plate'] = plate,
                 }, function(rowsChanged)
                     xPlayer.showNotification('تم نقل ملكية المركبة بأسمك <font color=yellow>'.. plate .. '</font>')
                 end)

                 --Spawn Vehicle
                 TriggerClientEvent('esx_misc:UsedCar:SpwanCarOutSideWhenBuy', source, props)
             else
                 xPlayer.showNotification('<font color=red>لا تملك النقود الكافية لشراء المركبة</font>')
            end
         else
             xPlayer.showNotification('<font color=red>معرض المستعمل </font></br>هذه المركبة تمت إزالتها أو تم شراؤها')
         end
    end)

 end)


--Delete Vehicle After 24 hours
Citizen.CreateThread( function()
    while true do
        Citizen.Wait(60*1000) -- every min
        MySQL.Async.fetchAll("SELECT * FROM vehicles_for_sale", {}, function(result)
            for _,v in pairs(result) do
                if v.Time > 0 and v.Time <= 1440 then
                    MySQL.Async.execute("UPDATE vehicles_for_sale SET Time = @Time WHERE plate=@plate",
                    {
                        ['@Time']      = v.Time - 1,
                        ['@plate']      = v.plate,
                    })
                else
                    MySQL.Async.execute("DELETE FROM vehicles_for_sale WHERE plate = @plate",
                        {
                            ['@plate'] = v.plate
                    })
                    
                    local xTarger = ESX.GetPlayerFromIdentifier(v.owner)
                        if xTarger ~= nil then
                            xTarger.showNotification('<font color=#FA3737>معرض المستعمل : </font>تم نقل مركبتك '..tostring(v.plate)..' إلى الكراج العمومي')
                        end
                end
            end
        end)
    end
end)

