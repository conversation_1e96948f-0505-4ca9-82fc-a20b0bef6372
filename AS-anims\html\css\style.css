@font-face {
    font-family: "Rubik Medium";
    src: url('../fonts/Rubik-Medium.ttf');
    font-display: swap;
}

@font-face {
    font-family: "Rubik Light";
    src: url('../fonts/Rubik-Light.ttf');
    font-display: swap;
}

.material-icons.md-18 { font-size: 24px; }

body {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0;
    /*AS: #c6c6c6;*/
}

.menu-container {
    position: relative;
    display: none;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
    width: 5em;
    height: 40em;
    margin-right: 1%;
    border-radius: 0.5vh;

    background: rgba(0, 0, 0, 0.80);
}

.sidebar:hover {
    transition: 0.2s;
    transform: scale(1.15);
    cursor: pointer;
}

.anims-container {
    position: relative;
    display: none;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    width: 70em;
    height: 40em;
    border-radius: 0.6vh;
}

.settings-container {
    position: relative;
    display: none;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
    width: 40em;
    height: 40em;
    border-radius: 0.6vh;
    background: rgba(0, 0, 0, 0.80);

    font-family: "Rubik Medium";
    color: #FFFFFF;
}
/*Arab Support: #c6c6c6;*/
.settings-title {
    font-size: 2.8em;
    margin-top: 2%;
    text-decoration: underline;
}

.settings-container div {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    width: 80%;
    margin: 0.10%;
}

.settings-container div span {
    font-size: 1.1em;
}

.settings-container div input {
    width: 30%;
    height: 100%;
    background: none;
    border: none;
    border-bottom: 0.1vh solid white;

    font-family: "Rubik Light";
    text-align: center;
    font-weight: 600;
    font-size: 1.1em;
    -webkit-appearance: none;
    outline: none;
    color: #FFFFFF;
}

.settings-container div input::-webkit-outer-spin-button,
.settings-container div input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.settings-container button {
    background: #272a2d;
    font-size: 1.05em;
    font-family: "Rubik Medium";
    width: 80%;
    height: 6.5%;
    border-radius: 30.4vh;
    border: none;
    color: white;
    box-shadow: 0.1vh 0.1vh 0.3vh 0.1vh #14171f;
    margin-top: 1.8%;
    margin-bottom: 1.5%;
    transition: 0.2s;
    cursor: pointer;
}

.settings-container button:hover {
    background-color: #990D35;
    box-shadow: 0.1vh 0.1vh 0.2vh 0.1vh #550d21;
}

.navbar {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-flow: row nowrap;
    width: 100%;
}

.info-container {
    position: absolute;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    align-self: flex-end;
    flex-direction: column;
    width: 18.2em;
    height: 4em;

    margin-right: 5%;
    top: -4.5em;

    background: #000000cc;
    border-radius: 0.6vh;
    text-align: center;
}

.info-container span {
    font-size: 2.2vh;
    color: #FFFFFF;
    font-family: "Rubik Medium";
    margin-left: 5%;
    margin-right: 5%;
    text-transform: capitalize;
}

.info-container span:last-of-type {
    font-size: 1.8vh;
    color: #C1C1C1;
    font-family: "Rubik Light";
    text-transform: none;
}

.navbar div {
    position: relative;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 70.5%;
    height: 3.8em;
    background: rgba(0, 0, 0, 0.80);
    border-radius: 0.6vh;
}
/*ALI: #c6c6c6;*/
.controls {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 1.5%;
    width: 2.5em;
    height: 2.5em;
    background-color: #000000cc;
    border-radius: 0.6vh;
    transition: 0.2s;
    cursor: pointer;
}

.controls:hover {
    transform: scale(1.05);
    background-color: #990D35;
}

.search-bar {
    background: transparent;
    border: transparent;
    font-size: 0.90em;
    font-family: "Rubik Light";
    font-weight: bold;
    color: #dedede;
    border-bottom: 0.15vh solid white;
    outline: none;
    width: 90%;
}

.anims-block {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-flow: row wrap;
    overflow-y: auto;
    margin-top: 1.5%;
    width: 100%;
    border-radius: 0.8vh;
}

.anims-block::-webkit-scrollbar {
    width: 0.6vh;
}

.anims-block::-webkit-scrollbar-track {
    background-color: #555;
    border-radius: 1.6vh;
}

.anims-block::-webkit-scrollbar-thumb {
    background: rgb(141, 141, 141);
    border-radius: 1.6vh;
}

.anims-block::-webkit-scrollbar-thumb:hover {
    background: #a7a7a7;
}

.anim {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 24%;
    height: 5.5em;
    margin: 0.35%;
    border-radius: 0.4vh;

    background: rgba(0, 0, 0, 0.70);
    cursor: pointer;
    transition: 0.2s;
    overflow: hidden;
}

.anim:hover {
    transform: scale(1.025);
    background-color: rgba(216, 17, 90, 0.80);
}

.anim div {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: 50%;
    margin-left: 7.5%;
    font-family: "Rubik Medium";
}

.anim div span:first-of-type {
    color: #FFFFFF;
    font-size: 1.4em;
    word-break:break-all;
}

.anim div span:last-of-type {
    color: #C1C1C1;
    font-size: 0.855em;
    word-break:break-all;
}

.star {
    color: #999999;
    font-size: 1.8em;
    margin-right: 10%;
    transition: 0.3s;
    cursor: pointer;
    z-index: 1;
}

.star:hover {
    transform: scale(1.05);
}

.favorite {order: 1 !important;}
.dances {order: 2;}
.scenarios {order: 3;}
.walks {order: 4;}
.expressions {order: 5;}
.shared {order: 6;}

.pop {
    animation: pop 0.3s ease-in-out;
}

.fadeIn {
    animation: fadeIn 0.4s;
}

.fadeOut {
    animation: fadeOut 0.4s;
}

@keyframes pop {
    0% {
        transform: scale(1.0);
    }
    100% {
        transform: scale(1.05);
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@media screen and (min-width: 45.375em) {
    .info-container {
        max-width: 11.50em;
        max-height: 2.725em;
        top: -3em;
        margin-right: 0;
    }
    .info-container span {
        font-size: 0.7em;
        margin-right: 2.5%;
        margin-left: 2.5%;
        width: 90%;
    }
    .info-container span:last-of-type {
        font-size: 0.55em;
    }
    .menu-container {
        max-width: 3em;
        max-height: 20em;
    }
    .anims-container {
        max-width: 30em;
        max-height: 20em;
    }
    .settings-container {
        max-width: 14em;
        max-height: 20em;
    }
    .settings-container div span {
        font-size: 0.65em;
    }
    .settings-container button {
        font-size: 0.60em;
    }
    .settings-title {
        font-size: 100.8em;
    }
    .settings-container div input {
        font-size: 0.7em;
    }
    .anim {
        max-width: 24%;
        max-height: 2.9em;
    }
    .anim div span:first-of-type {
        font-size: 0.8em;
    }

    .anim div span:last-of-type {
        font-size: 0.525em;
    }
    .navbar div {
        max-width: 60%;
        max-height: 2.5em;
    }
    .star {
        font-size: 1.2em
    }
    .search-bar {
        font-size: 0.8em;
        max-width: 80%;
    }
    .controls {
        max-width: 1.7em;
        max-height: 1.7em;
    }
}
/*AS: #c6c6c6;*/
@media screen and (min-width: 80em) {
    .info-container {
        max-width: 15em;
        max-height: 3.5em;
        top: -4em;
        margin-right: 0.4em;
    }
    .info-container span {
        font-size: 0.9em;
        margin-right: 0.2em;
        margin-left: 0.2em;
    }
    .info-container span:last-of-type {
        font-size: 0.65em;
    }
    .menu-container {
        max-width: 4em;
        max-height: 25em;
    }
    .anims-container {
        max-width: 40em;
        max-height: 25em;
    }
    .settings-container {
        max-width: 18em;
        max-height: 25em;
    }
    .settings-container div span {
        font-size: 0.85em;
    }

    .settings-container button {
        font-size: 0.80em;
    }
    .settings-title {
        font-size: 1.0em;
    }
    .settings-container div input {
        font-size: 0.85em;
    }
    .anim {
        max-width: 24%;
        max-height: 4em;
    }
    .anim div span:first-of-type {
        font-size: 1.0em;
    }

    .anim div span:last-of-type {
        font-size: 0.655em;
    }
    .navbar div {
        max-width: 60%;
        max-height: 3.2em;
    }
    .star {
        font-size: 1.4em
    }
    .search-bar {
        font-size: 0.9em;
        max-width: 80%;
    }
    .controls {
        max-width: 2.2em;
        max-height: 2.2em;
    }
}

@media screen and (min-width: 100em) {
    .info-container {
        max-width: 100em;
        max-height: 4em;
        top: -4.5em;
        margin-right: 1.4em;
    }
    .info-container span {
        font-size: 1.1em;
        margin-right: 0.5em;
        margin-left: 0.5em;
    }
    .info-container span:last-of-type {
        font-size: 0.75em;
    }
    .menu-container {
        max-width: 100%;
        max-height: 100%;
    }
    .anims-container {
        max-width: 100%;
        max-height: 100%;
    }
    .settings-container {
        max-width: 110%;
        max-height: 100%;
    }
    .settings-container div span {
        font-size: 1.2em;
    }

    .settings-container button {
        font-size: 1.05em;
    }
    .settings-title {
        font-size: 1.8em;
    }
    .settings-container div input {
        font-size: 1.1em;
    }
    .anim {
        min-width: 24%;
        min-height: 5.8em;
    }
    .anim div span:first-of-type {
        font-size: 1.3em;
    }
    .anim div span:last-of-type {
        font-size: 1.055em;
    }
    .star {
        font-size: 2.2em
    }
    .navbar div {
        min-width: 70.5%;
        min-height: 3.8em;
    }
    .controls {
        min-width: 2.5em;
        min-height: 2.5em;
    }
    .search-bar {
        font-size: 1.2em;
        max-width: 100%;
    }
}
/*Arab Support: #c6c6c6;*/
@media screen and (min-width: 160em) {
    .info-container {
        min-width: 28.5em;
        min-height: 6em;
        top: -7em;
        margin-right: 1.4em;
    }
    .info-container span {
        font-size: 1.7em;
        margin-right: 0.5em;
        margin-left: 0.5em;
    }
    .info-container span:last-of-type {
        font-size: 1.2em;
    }
    .menu-container {
        min-width: 6.5em;
        min-height: 55em;
    }
    .anims-container {
        min-width: 100em;
        min-height: 55em;
    }
    .settings-container {
        min-width: 35em;
        min-height: 55em;
    }
    .settings-container div span {
        font-size: 1.6em;
    }

    .settings-container button {
        font-size: 1.65em;
    }
    .settings-title {
        font-size: 8.1em;
    }
    .settings-container div input {
        font-size: 1.6em;
    }
    .anim {
        min-width: 24em;
        min-height: 7.8em;
    }
    .anim div span:first-of-type {
        font-size: 2.0em;
    }
    .anim div span:last-of-type {
        font-size: 1.555em;
    }
    .star {
        font-size: 3.2em
    }
    .navbar div {
        min-width: 68.5%;
        min-height: 5.8em;
    }
    .controls {
        min-width: 4.0em;
        min-height: 4.0em;
    }
    .search-bar {
        font-size: 1.7em;
        max-width: 95%;
    }
}
