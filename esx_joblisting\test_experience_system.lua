-- ملف اختبار نظام الخبرة للوظائف
-- يمكن استخدام هذا الملف لاختبار النظام

-- أوامر للاختبار (يجب تشغيلها من console أو من خلال admin menu)

--[[
-- لإعطاء خبرة للاعب
TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'add', 1000, 'اختبار نظام الوظائف')

-- لإزالة خبرة من اللاعب  
TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', source, 'remove', 500, 'اختبار نظام الوظائف')

-- للتحقق من مستوى اللاعب الحالي
local playerLevel = exports.zahya_xplevel.ESXP_GetRank()
print('مستوى اللاعب الحالي: ' .. playerLevel)

-- للتحقق من خبرة اللاعب الحالية
local playerXP = exports.zahya_xplevel.ESXP_GetXP()
print('خبرة اللاعب الحالية: ' .. playerXP)
--]]

-- دالة لاختبار النظام
function TestExperienceSystem()
    print("=== اختبار نظام متطلبات الخبرة للوظائف ===")
    
    -- طباعة إعدادات النظام
    print("حالة النظام: " .. (Config.ExperienceSystem.Enabled and "مفعل" or "معطل"))
    
    print("متطلبات الخبرة للوظائف:")
    for job, level in pairs(Config.ExperienceSystem.JobRequirements) do
        print("  " .. job .. " -> مستوى " .. level)
    end
    
    print("الألوان المستخدمة:")
    print("  مؤهل: " .. Config.ExperienceSystem.Colors.Eligible)
    print("  غير مؤهل: " .. Config.ExperienceSystem.Colors.NotEligible)
    
    print("=== انتهاء الاختبار ===")
end

-- تشغيل الاختبار عند تحميل المورد
if IsDuplicityVersion() then -- server side
    AddEventHandler('onResourceStart', function(resourceName)
        if GetCurrentResourceName() == resourceName then
            Wait(1000) -- انتظار قليل للتأكد من تحميل كل شيء
            TestExperienceSystem()
        end
    end)
end

-- أوامر مفيدة للإدارة
RegisterCommand('setplayerxp', function(source, args)
    if source == 0 then -- console only
        local playerId = tonumber(args[1])
        local xpAmount = tonumber(args[2])
        
        if playerId and xpAmount then
            TriggerEvent('zahya_xplevel:updateCurrentPlayerXP', playerId, 'add', xpAmount, 'تعديل من الكونسول')
            print('تم إعطاء ' .. xpAmount .. ' خبرة للاعب ' .. playerId)
        else
            print('الاستخدام: setplayerxp [player_id] [xp_amount]')
        end
    end
end)

RegisterCommand('checkplayerxp', function(source, args)
    if source == 0 then -- console only
        local playerId = tonumber(args[1])
        
        if playerId then
            local xPlayer = ESX.GetPlayerFromId(playerId)
            if xPlayer then
                local playerXP = xPlayer.get("xp") or 0
                local playerRank = xPlayer.get("rank") or 0
                print('اللاعب ' .. playerId .. ' - الخبرة: ' .. playerXP .. ' - المستوى: ' .. playerRank)
            else
                print('اللاعب غير موجود')
            end
        else
            print('الاستخدام: checkplayerxp [player_id]')
        end
    end
end)
