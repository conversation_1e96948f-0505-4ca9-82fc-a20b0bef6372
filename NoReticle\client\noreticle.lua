Citizen.CreateThread(function()
	TriggerServerEvent('testnoreticle', GetPlayerServerId(PlayerId()))
end)

RegisterNetEvent('nrcallback')
AddEventHandler('nrcallback', function(isStaff)
	if(not isStaff) then

		local isSniper = false
		while true do
			Citizen.Wait(0)

			local ped = GetPlayerPed(-1)
			local currentWeaponHash = GetSelectedPedWeapon(ped)

			if currentWeaponHash == 100416529 then
				isSniper = true
			elseif currentWeaponHash == 205991906 then
				isSniper = true
			elseif currentWeaponHash == -952879014 then
				isSniper = true
			elseif currentWeaponHash == GetHashKey('WEAPON_HEAVYSNIPER_MK2') then
				isSniper = true
			else
				isSniper = false
			end

			if not isSniper then
				HideHudComponentThisFrame(14)
			end
		end
	end
end)
