function AddTextEntry(key, value)
    Citizen.InvokeNative(GetHash<PERSON>ey("ADD_TEXT_ENTRY"), key, value)
end

function kunesc(txt)
    return "<FONT FACE = 'A9eelsh'>" .. txt .. '</font>'
end

Citizen.CreateThread(function()
    AddTextEntry('FE_THDR_GTAO', kunesc("~g~najm ~w~country | ~g~ﻢﺠﻧ ~w~ﺔﻌﻃﺎﻘﻣ"))
    AddTextEntry('PM_PANE_LEAVE', kunesc("~r~ﺮﻓﺮﻴﺴﻟﺍ ﻦﻣ ﺝﻭﺮﺨﻟﺍ"))
    AddTextEntry('PM_PANE_QUIT', kunesc("~r~ﺔﺒﻌﻠﻟﺍ ﻦﻣ ﺝﻭﺮﺨﻟﺍ"))
    AddTextEntry('PM_SCR_MAP', kunesc("ﻢﺠﻧ  ﺔﻄﻳﺮﺧ"))
    AddTextEntry('PM_SCR_GAM', kunesc("ﺔﺒﻌﻠﻟﺍ"))
    AddTextEntry('PM_SCR_INF', kunesc("ﺕﺎﻣﻮﻠﻌﻣ"))
    AddTextEntry('PM_SCR_SET', kunesc("ﺕﺍﺩﺍﺪﻋﻹﺍ"))
    AddTextEntry('PM_SCR_STA', kunesc("ﺔﻟﺎﺤﻟﺍ"))
    AddTextEntry('PM_SCR_GAL', kunesc("ﺭﻮﺼﻟﺍ"))
    AddTextEntry('PM_SCR_RPL', kunesc("ﺭﺎﺘﺳ ﻙﻭﺭ ﺝﺎﺘﻧﻮﻣ 🎥"))
end)