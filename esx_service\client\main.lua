
RegisterNetEvent('esx_service:notifyAllInService')
AddEventHandler('esx_service:notifyAllInService', function(notification, target)
	target = GetPlayerFromServerId(target)
	if target == PlayerId() then return end

	local targetPed = GetPlayerPed(target)
	local mugshot, mugshotStr = ESX.Game.GetPedMugshot(targetPed)

	--ESX.ShowAdvancedNotification(notification.title, notification.subject, notification.msg, mugshotStr, notification.iconType)
	ESX.ShowNotification("<p align=center><font color=yellow>اعلان خدمة موظف</font></br><font color=gray>"..notification.subject.."<b></br><font color=#00d874> "..notification.title.." <font color=white>"..notification.msg.."<b>")
	UnregisterPedheadshot(mugshot)
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	TriggerServerEvent('esx_service:notifyAllInServiceLeave')
end)