Config = {}

Config.DrawDistance = 100
Config.MarkerSize = {x = 1.5, y = 1.5, z = 1.0}
Config.MarkerColor = {r = 102, g = 102, b = 204}
Config.RoomMenuMarkerColor = {r = 102, g = 204, b = 102}
Config.MarkerType = 1
Config.MarkerText = false

Config.RentModifier = 200 -- rent price: <property price> / <rent modifier> (rounded)
Config.SellModifier = 2   -- sell price: <property price> / <sell modifier> (rounded)

Config.Properties = {}

Config.EnablePlayerManagement = false -- If set to true you use esx_realestateagentjob
Config.Locale = 'en'

Config.itemsThatAreAllowedToPutInProperty = {
    -- الاكل والشرب
	'water',
	'bread',
	'chocolate',
	'cupcake',
	'bergrkb',
	'batato',
	'grape',
	'cheps',
	'coshe',
	'bergrul',
	'cocacola',
	'grape_juice',
	'pepsi',

	-- الممنوعات
	'beer',
	'meth',
	'weed',
	'opium',
	'coke',
	'coke_pooch',
	'meth_pooch',
	'opium_pooch',
	'weed_pooch',
	'id_card_f',
	-- اخرى
	'bulletpolice',
	'bulletproof',
	'bulletproofbox',

	'radio',
	'bulletproof',
	'thermal_charge',
	'parkingcard',
	'laptop_h',
	'drill',
	'grand_cru',
	'laser_drill',

}