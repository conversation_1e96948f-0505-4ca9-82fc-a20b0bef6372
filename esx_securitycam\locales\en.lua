Locales['en'] = {
  -- <PERSON><PERSON> Hint
  ['marker_hint'] = 'Press ~INPUT_CONTEXT~ to connect to the ~g~cameras~s~',

  -- Menu
  ['securitycams_menu'] = 'Security Cameras',
  ['bank_menu_selection'] = 'Pacific Standard Bank',
  ['police_menu_selection'] = 'Police Station',

  -- In Cameras
  ['pacific_standard_bank'] = 'Pacific Standard Bank',
  ['police_station'] = 'Police Station',

  -- Pacific Standard Bank Cameras
  ['bcam'] = 'Main Entrance',
  ['bcam2'] = 'Lobby',
  ['bcam3'] = 'Second Entrance',
  ['bcam4'] = 'Staircase To Second Floor',
  ['bcam5'] = 'Staircase Above Bankvault',
  ['bcam6'] = 'Offices Corridor #1',
  ['bcam7'] = 'Offices Corridor #2',
  ['bcam8'] = 'Second Floor #1',
  ['bcam9'] = 'Second Floor #2',
  ['bcam10'] = 'Staircase To Bankvault',
  ['bcam11'] = 'Outside The Bankvault',

  -- Police Station Cameras
  ['pcam'] = 'Parking Lot',
  ['pcam2'] = 'Cell #1',
  ['pcam3'] = 'Cell #2',
  ['pcam4'] = 'Cell #3',
  ['pcam5'] = 'Polices Parking Lot & Garage',
  ['pcam6'] = 'Outside Entrance',
  ['pcam7'] = 'Lobby',

  -- Settings
  ['next'] = 'NEXT CAMERA',
  ['previous'] = 'PREVIOUS CAMERA',
  ['close'] = 'CLOSE CAMERA',

  -- Hack
  ['marker_hint_hacking_policestation'] = 'Press ~INPUT_CONTEXT~ to hack the police stations ~g~cameras~s~',
  ['marker_hint_hacking_bank'] = 'Press ~INPUT_CONTEXT~ to hack the banks ~g~cameras~s~',
  ['broken_cameras'] = 'Cameras are down! <br />Go to the cameras main plant to see if anything is wrong.',
  ['hacking_succeed'] = 'Hacking complete! <br />The police cant connect to the cameras anymore.',
  ['hacking_failed'] = 'Hacking incomplete! <br />The police are on their way.',
  ['infected_cameras'] = 'Cameras have been infected! <br />The police cant connect to the cameras.',
  ['nothing_wrong'] = 'There is nothing wrong?',
  ['removing_viruses'] = 'Virus found! Starting to clean up the virus...',
  ['unhack_policestation'] = 'Press ~INPUT_CONTEXT~ to get rid of the ~g~virus~s~',
  ['unhack_bank'] = 'Press ~INPUT_CONTEXT~ to get rid of the ~g~virus~s~',
}
