Items                  = {}
local DataStoresIndex  = {}
local DataStores       = {}
local SharedDataStores = {}

local listPlate = Config.VehiclePlate



AddEventHandler('onMySQLReady', function()
  local result = MySQL.Sync.fetchAll('SELECT * FROM trunk_inventory')
  local data = nil
  if #result ~= 0 then
    for i=1,#result,1 do
      local plate = result[i].plate
      local owner = result[i].owner
      local data = (result[i].data == nil and {} or json.decode(result[i].data))
      local dataStore   = CreateDataStore(plate, owner, data)
      SharedDataStores[plate] = dataStore
    end
  end
end)

function loadInvent(plate)
  local result = MySQL.Sync.fetchAll('SELECT * FROM trunk_inventory WHERE plate = @plate',
  {
    ['@plate'] = plate,
  })
  local data = nil
  if #result ~= 0 then
    for i=1,#result,1 do
      local plate = result[i].plate
      local owner = result[i].owner
      local data = (result[i].data == nil and {} or json.decode(result[i].data))
      local dataStore   = CreateDataStore(plate, owner, data)
      SharedDataStores[plate] = dataStore
    end
  end
end

function getOwnedVehicule(plate)
  local found = false
  -- if listPlate then
    -- for k,v in pairs(listPlate) do
      -- if string.find(plate,v) ~= nil then
        -- found = true
        -- break
      -- end
    -- end
  -- end
  if not found then
    local result = MySQL.Sync.fetchAll('SELECT * FROM owned_vehicles')
    while result == nil do
      Wait(5)
    end
    if result ~= nil and #result > 0 then
      for _,v in pairs(result) do
        local vehicle = json.decode(v.vehicle)
        if vehicle.plate == plate then
          found = true
          break
        end
      end
    end
  end
  return found
end




function MakeDataStore(plate)
  local data = {}
  local owner = getOwnedVehicule(plate)
  local dataStore   = CreateDataStore(plate, owner, data)
  if plate then 
  SharedDataStores[plate] = dataStore
  MySQL.Async.execute('INSERT IGNORE INTO trunk_inventory(plate,data,owner) VALUES (@plate,\'{}\',@owner)',
  {
    ['@plate'] = plate,
    ['@owner'] = owner,
  }
  )
  loadInvent(plate)
end
end


function GetSharedDataStore(plate)
  if SharedDataStores[plate] == nil then
    MakeDataStore(plate)
  end
  return SharedDataStores[plate]
end

AddEventHandler('esx_trunk:getSharedDataStore', function(plate,cb)
  cb(GetSharedDataStore(plate))
end)