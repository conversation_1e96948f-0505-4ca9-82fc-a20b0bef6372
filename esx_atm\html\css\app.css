@font-face {
	font-family: roboto;
	src: url('../roboto.ttf');
}

body {
	width: 100%;
	height: 100%;
	overflow: hidden;
	font-family: roboto;
}

#cursor {
	position: absolute;
	z-index: 999999;
	display: none;
}

#container {
	position: absolute;
	background: linear-gradient(#0c3b97, #3458ae);
	height: 500px;
	width: 600px;
	top: 50%;
	left: 50%;
	margin: -250px 0 0 -300px;
	cursor: default;
}

#header {
	position: absolute;
	display: block;
	width: 100%;
	height: 75px;
	background: linear-gradient(#f5f5f5, #adadad);
}

#logo {
	height: 55px;
	margin: 10px;
}

#welcome-text {
	font-size: 25px;
	position: absolute;
	top: 100px;
	left: 50%;
	width: 450px;
	margin: 0 0 0 -225px;
	color: white;
}

#menu {
	position: relative;
	top: 90%;
}

#menu {
	display: flex;
	flex-direction: column;
	height: 150px;
	margin: -150px auto 0;
	width: 500px;
}

#menu div {
	display: flex;
	flex-direction: row;
}

input {
	margin: 0 20px 0 0;
	background: linear-gradient(#00408e, #02306b);
	border-radius: 5px;
	border: 1px solid #4f89d4;
	color: white;
}

input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
	/* display: none; <- Crashes Chrome on hover */
	-webkit-appearance: none;
	margin: 0;
	/* <-- Apparently some margin are still there even though it's hidden */
}

button {
	background: #ffffff;
	border-radius: 5px;
}

button:hover {
	cursor: pointer;
}

#deposit_btn {
	width: 250px;
	height: 50px;
	font-size: 2em;
}

#deposit_amount {
	width: 250px;
	height: 50px;
	font-size: 2em;
}

#withdraw_btn {
	width: 250px;
	height: 50px;
	font-size: 2em;
}

#withdraw_amount {
	width: 250px;
	height: 50px;
	font-size: 2em;
}