-- Event --

-- Mina
RegisterNetEvent('Mody:Log:MinaOFF')
AddEventHandler('Mody:Log:MinaOFF', function()
	MinaOFF()
end)

RegisterNetEvent('Mody:Log:MinaON')
AddEventHandler('Mody:Log:MinaON', function()
	MinaON()
end)

-- Panic
RegisterNetEvent('Mody:Log:PanicStart')
AddEventHandler('Mody:Log:PanicStart', function()
	PanicStartLog()
end)

RegisterNetEvent('Mody:Log:PanicStopLog')
AddEventHandler('Mody:Log:PanicStopLog', function()
	PanicStopLog()
end)

-- PeaceTime
RegisterNetEvent('Mody:Log:PeaceTimeStartLog')
AddEventHandler('Mody:Log:PeaceTimeStartLog', function(time)
	local time = time
	PeaceTimeStartLog(time)
end)

RegisterNetEvent('Mody:Log:PeaceTimeStopLog')
AddEventHandler('Mody:Log:PeaceTimeStopLog', function()
	PeaceTimeStopLog()
end)

-- Store Log
RegisterNetEvent('Mody:Log:StoreLog')
AddEventHandler('Mody:Log:StoreLog', function(StoreName, OwnerName)
	StoreLog(StoreName, OwnerName)
end)

-- Function --

-- Mina

function MinaOFF()
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
		if xPlayer.getJob().name == 'police' then
			local DISCORD_IMAGE = ''
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
		 	 	{
					  ["color"] = 0,
			 		 ["title"] = "اغلاق الميناء البحري الرئيسي\nتنويه",
			 		 ["description"] = "يمنع الدخول حتى يتم اعلان الافتتاح\nتواجد المواطنين داخل الميناء اثناء اعلان اغلاق الرسمي يخصم من الخبرة ومخالف للنظام العام",
			 		 ["footer"] = {
				 	 	["text"] = ""..xPlayer.getJob().label.."",
				  		["icon_url"] = "https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj"
			 		 },
			 		 ["fields"] = {{
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
					}}
		 		 }
	  		}
			PerformHttpRequest('https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })

		else
			local DISCORD_IMAGE = 'غيرهاs/1273835024323711048/HTeBY49zKdGw-qb2-jfys5_S8kR_k1HP07Y23BNPmyf6vLDR31x1exZD6OOVWdoMYLlj'
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
				  {
					  ["color"] = 0,
					  ["title"] = "اغلاق الميناء البحري الرئيسي\nتنويه",
					  ["description"] = "يمنع الدخول حتى يتم اعلان الافتتاح\nتواجد المواطنين داخل الميناء اثناء اعلان اغلاق الرسمي يخصم من الخبرة ومخالف للنظام العام",
					  ["footer"] = {
					 	 ["text"] = ""..xPlayer.getJob().label.."",
					 	 ["icon_url"] = "غيرهاs/1273835024323711048/HTeBY49zKdGw-qb2-jfys5_S8kR_k1HP07Y23BNPmyf6vLDR31x1exZD6OOVWdoMYLlj"
					  },
				 	 ["fields"] = {{
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
					}}
			 	 }
		 	 }
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
		end
	end
end

function MinaON()
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
		if xPlayer.getJob().name == 'police' then
			local DISCORD_IMAGE = 'غيرهاs/1273835024323711048/HTeBY49zKdGw-qb2-jfys5_S8kR_k1HP07Y23BNPmyf6vLDR31x1exZD6OOVWdoMYLlj'
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
		 	 	{
					  ["color"] = 5360128,
			 		 ["title"] = "افتتاح الميناء البحري الرئيسي\nتنويه",
			 		 ["description"] = "يمكنك الآن بيع واستلام البضائع من الموقع",
			 		 ["footer"] = {
				 	 	["text"] = ""..xPlayer.getJob().label.."",
				  		["icon_url"] = "غيرهاs/1273835024323711048/HTeBY49zKdGw-qb2-jfys5_S8kR_k1HP07Y23BNPmyf6vLDR31x1exZD6OOVWdoMYLlj"
			 		 },
			 		 ["fields"] = {{
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
					}}
		 		 }
	  		}
			PerformHttpRequest('https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })

		else
			local DISCORD_IMAGE = ''
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
				  {
					  ["color"] = 5360128,
					  ["title"] = "افتتاح الميناء البحري الرئيسي\nتنويه",
					  ["description"] = "يمكنك الآن بيع واستلام البضائع من الموقع",
					  ["footer"] = {
					 	 ["text"] = ""..xPlayer.getJob().label.."",
					 	 ["icon_url"] = "غيرهاs/1273835024323711048/HTeBY49zKdGw-qb2-jfys5_S8kR_k1HP07Y23BNPmyf6vLDR31x1exZD6OOVWdoMYLlj"
					  },
				 	 ["fields"] = {{
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
					}}
			 	 }
		 	 }
			PerformHttpRequest('https://discord.com/api/webhooks/1324936382535438498/jto8L5tj3vYLp8WdSmvFEUP1373ok1pmN6U8uLA12IOZJL5u0vzBaL8nwysAf-AIvDzj', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
		end
	end
end

-- Panic
function PanicStartLog()
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
		if xPlayer.getJob().name == 'police' then
			local DISCORD_IMAGE = 'غيرهاs/1273834608932163635/XuIDwOZ6CdpmtmFureylwL_G6YFBy6d0BJEMyC-wrV8cFDRkam1W4R749RWcUMGqxrf4'
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
		 	 	{
					  ["color"] = 11468800,
			 		 ["title"] = "تم ارسال نداء استغاثة\nرسالة",
			 		 ["description"] = "احتاج مساعدة أو النجدة في موقعي",
			 		 ["footer"] = {
				 	 	["text"] = ""..xPlayer.getJob().label.."",
				  		["icon_url"] = "غيرهاs/1273834608932163635/XuIDwOZ6CdpmtmFureylwL_G6YFBy6d0BJEMyC-wrV8cFDRkam1W4R749RWcUMGqxrf4"
			 		 },
			 		 ["fields"] = {{
				 		 ["name"] = "تعليمات للعام",
				 		 ["value"] = "حاول المساعدة ولا تعرض حياتك للخطر إذا كان يوجد تبادل اطلاق نار راقب من بعيد وحاول اخذ مواصفات المشتبه بهم وابلاغ الجهات الأمنية عن طريق الجوال أو في الموقع"
			 		 },
					  {
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
					  }
					}
		 		 }
	  		}
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })

		else
			local DISCORD_IMAGE = 'غيرهاs/1020372712830537728/-lb1NqWH09pwKqFm5S5jza3BlOV7uNERt9bcBtbX9fLpvtef1aEJbhuTQhq71on7BIFY'
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
				  {
					  ["color"] = 11468800,
					  ["title"] = "تم ارسال نداء استغاثة\nرسالة",
					  ["description"] = "احتاج مساعدة أو النجدة في موقعي",
					  ["footer"] = {
					 	 ["text"] = ""..xPlayer.getJob().label.."",
					 	 ["icon_url"] = "غيرهاs/1020372712830537728/-lb1NqWH09pwKqFm5S5jza3BlOV7uNERt9bcBtbX9fLpvtef1aEJbhuTQhq71on7BIFY"
					  },
				 	 ["fields"] = {{
						  ["name"] = "تعليمات للعام",
					  	["value"] = "حاول المساعدة ولا تعرض حياتك للخطر إذا كان يوجد تبادل اطلاق نار راقب من بعيد وحاول اخذ مواصفات المشتبه بهم وابلاغ الجهات الأمنية عن طريق الجوال أو في الموقع"
				  	},
				 	 {
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
				 	 }
					}
			 	 }
		 	 }
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
		end
	end
end

function PanicStopLog()
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
		if xPlayer.getJob().name == 'police' then
			local DISCORD_IMAGE = 'غيرهاs/1020372712830537728/-lb1NqWH09pwKqFm5S5jza3BlOV7uNERt9bcBtbX9fLpvtef1aEJbhuTQhq71on7BIFY'
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
		 	 	{
					  ["color"] = 5360128,
			 		 ["title"] = "انتهاء نداء استغاثة\nرسالة",
			 		 ["description"] = "انتهت حالة الاستغاثة في الموقع",
			 		 ["footer"] = {
				 	 	["text"] = ""..xPlayer.getJob().label.."",
				  		["icon_url"] = "غيرهاs/1020372712830537728/-lb1NqWH09pwKqFm5S5jza3BlOV7uNERt9bcBtbX9fLpvtef1aEJbhuTQhq71on7BIFY"
			 		 },
			 		 ["fields"] = {--[[{
				 		 ["name"] = "تعليمات للعام",
				 		 ["value"] = "حاول المساعدة ولا تعرض حياتك للخطر إذا كان يوجد تبادل اطلاق نار راقب من بعيد وحاول اخذ مواصفات المشتبه بهم وابلاغ الجهات الأمنية عن طريق الجوال أو في الموقع"
			 		 },]]
					  {
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
					  }
					}
		 		 }
	  		}
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })

		else
			local DISCORD_IMAGE = 'غيرهاs/1020372712830537728/-lb1NqWH09pwKqFm5S5jza3BlOV7uNERt9bcBtbX9fLpvtef1aEJbhuTQhq71on7BIFY'
			local DISCORD_NAME = ''..xPlayer.getJob().label..''
			local connect = {
				  {
					  ["color"] = 5360128,
					  ["title"] = "انتهاء نداء استغاثة\nرسالة",
					  ["description"] = "انتهت حالة الاستغاثة في الموقع",
					  ["footer"] = {
					 	 ["text"] = ""..xPlayer.getJob().label.."",
					 	 ["icon_url"] = "غيرهاs/1020372712830537728/-lb1NqWH09pwKqFm5S5jza3BlOV7uNERt9bcBtbX9fLpvtef1aEJbhuTQhq71on7BIFY"
					  },
				 	 ["fields"] = {--[[{
						  ["name"] = "تعليمات للعام",
					  	["value"] = "حاول المساعدة ولا تعرض حياتك للخطر إذا كان يوجد تبادل اطلاق نار راقب من بعيد وحاول اخذ مواصفات المشتبه بهم وابلاغ الجهات الأمنية عن طريق الجوال أو في الموقع"
				  	},]]
				 	 {
						["name"] = ""..xPlayer.getJob().grade_label.."",
						["value"] = ""..xPlayer.getName()..""
				 	 }
					}
			 	 }
		 	 }
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
		end
	end
end

-- PeaceTime
function PeaceTimeStartLog(time)
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
			local DISCORD_IMAGE = 'غيرهاs/1273834558323949691/UCTsu5AHzH2_ON_VjZ-1qYCRX0xAKnC5YGj233FnaeTv2I0rmhoeMq8JcSSzuCEzmEJ2'
			local DISCORD_NAME = 'إدارة الرقابة والتفتيش'
			local time = time
			local connect = {
		 	 	{
					  ["color"] = 5360128,
			 		 ["title"] = "اعلان وقت راحة | المدة "..time.." دقيقة\nتنويه",
			 		 ["description"] = "يمنع العمل الاجرامي وتعطل جميع الدوائر الحكومية والخاصة حتى انتهاء الوقت التخريب خلال وقت الراحة يعرضك لعقوبات مشددة",
			 		 ["footer"] = {
				 	 	["text"] = "إدارة الرقابة والتفتيش",
				  		["icon_url"] = "غيرهاs/1273834558323949691/UCTsu5AHzH2_ON_VjZ-1qYCRX0xAKnC5YGj233FnaeTv2I0rmhoeMq8JcSSzuCEzmEJ2"
			 		 },
			 		 ["fields"] = {--[[{
				 		 ["name"] = "تعليمات للعام",
				 		 ["value"] = "حاول المساعدة ولا تعرض حياتك للخطر إذا كان يوجد تبادل اطلاق نار راقب من بعيد وحاول اخذ مواصفات المشتبه بهم وابلاغ الجهات الأمنية عن طريق الجوال أو في الموقع"
			 		 },]]
					  {
						["name"] = "الوظائف المعتمدة",
						["value"] = "يجب موافقة الرتبة الأعلى لتغير الوظيفة يمنع تغيير الوظيفة إذا كان وقت الراحة قصير"
					  }
					}
		 		 }
	  		}
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
	end
end

function PeaceTimeStopLog()
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
			local DISCORD_IMAGE = 'غيرهاs/1020373026052788326/Ir91Sr_LL8g-MSWt1cnCls9N-HNoaqJ-lxYO1zZpn1QQuR2FDz7MfTBwjG0S1QXi9SDm'
			local DISCORD_NAME = 'إدارة الرقابة والتحقيق'
			local time = 10
			local connect = {
		 	 	{
					  ["color"] = 5360128,
			 		 ["title"] = "انتهاء وقت راحة \nرسالة",
			 		 ["description"] = "عودة الحياة طبيعية في المنطقة",
			 		 ["footer"] = {
				 	 	["text"] = "إدارة الرقابة والتحقيق",
				  		["icon_url"] = "غيرهاs/1273834558323949691/UCTsu5AHzH2_ON_VjZ-1qYCRX0xAKnC5YGj233FnaeTv2I0rmhoeMq8JcSSzuCEzmEJ2"
			 		 },
			 		 --[["fields"] = {
				 		 ["name"] = "تعليمات للعام",
				 		 ["value"] = "حاول المساعدة ولا تعرض حياتك للخطر إذا كان يوجد تبادل اطلاق نار راقب من بعيد وحاول اخذ مواصفات المشتبه بهم وابلاغ الجهات الأمنية عن طريق الجوال أو في الموقع"
			 		 },
					  {
						["name"] = "الوظائف المعتمدة",
						["value"] = "يجب موافقة الرتبة الأعلى لتغير الوظيفة يمنع تغيير الوظيفة إذا كان وقت الراحة قصير"
					  }
					}]]
		 		 }
	  		}
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
	end
end

-- Store Log

function StoreLog(StoreName, OwnerName)
	local src = source
	local xPlayer = ESX.GetPlayerFromId(src)
	if xPlayer ~= nil then
			local DISCORD_IMAGE = 'غيرهاs/1020243795092180993/Jmj20Hzj-PXC1-7a3eTJFEHITa8KEiswmS_v_ukio_0MDJqYP67B6iqkpOjCgmucfz_W'
			local DISCORD_NAME = 'متجر'
			local StoreName = StoreName
			local OwnerName = OwnerName
			local connect = {
		 	 	{
					  ["color"] = 13174278,
			 		 ["title"] = "تم رصد عملية سرقة متجر",
			 		 --["description"] = ""..StoreName.."",
			 		 ["footer"] = {
				 	 	["text"] = "إدارة المتاجر",
				  		["icon_url"] = "غيرهاs/1273837830090788915/hsldG2_bcnTI87nmPQhs1Rm0W04iTNLO754wet4n4qxsZNIVc93M71Qw7cqC1pgiBvq-"
			 		 },
			 		 ["fields"] = {{
						["name"] = "اسم المتجر :",
						["value"] = ""..StoreName..""
			 		 },
					  {
						["name"] = "المالك :",
						["value"] = ""..OwnerName..""
					  }
					}
				}
		 	 }
	  	
			--PerformHttpRequest('غيرهاs/816264991400263700/uZXLulWj7u1lARij47neXpLzDGjm3haeqUw3k9oqUS-7Zadzu6dSw45GyKr2acUue8pb', function(err, text, headers) end, 'POST', json.encode({username = DISCORD_NAME, embeds = connect, avatar_url = DISCORD_IMAGE}), { ['Content-Type'] = 'application/json' })
	end
end