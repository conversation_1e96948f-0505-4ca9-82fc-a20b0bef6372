
--[[

local passengerDriveBy = false

Citizen.CreateThread(function()
	while true do

		playerPed = PlayerPedId()
		car = GetVehiclePedIsIn(playerPed, false)
		if car then
			if GetPedInVehicleSeat(car, -1) == playerPed then
				SetPlayerCanDoDriveBy(PlayerId(), false)
			elseif passengerDriveBy then
				SetPlayerCanDoDriveBy(PlayerId(), true)
			else
				SetPlayerCanDoDriveBy(PlayerId(), false)
			end
		else
			Wait(3000)
		end

		Wait(1)
	end
end)--]]