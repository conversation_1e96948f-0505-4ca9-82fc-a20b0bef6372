Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}


--shared
LeoJobs = {'police','agent','admin','army'}

--Other
Config        = {}
Config.Locale = "en"

Config.colaprice = 15 -- سعر الكوكاكولا ب المكينة

Config.EnableConnectOptions = false -- تفعيل/إلغاء خيارات دخول ديسكورد او متجر او دخول السيرفر بعد الضغط على دخول السيرفر

Config.EnablePlayersBlips = true

Config.jobsBlip = {
	['admin']={color=1},
	['police']={color=38},
	['agent']={color=2},
	['army']={color=31},
	['mechanic']={color=55},
	['ambulance']={color=47},
	['taxi']={color=46}
}

Config.dev_mod = false

--support 128+ players
Config.OnesyncInfinty = true

blipColor = {
	red = 1,
	blue = 63,
	green = 25,
	purple = 58,
	black = 72,
	gold = 46,
}

Config_car_job = {}

Config_car_job = {
    carsblacklisted = { -- put here the vehicles that you want only emergency services to be able to get in
        "polkuw30",
        "polkuw31",
        "polkuw32",
        "flatbed3",
        "towtruck14",
        "polkuw06",
        "unmarked_02",
        "unmarked_03",
        "unmarked_04",
        "polchall70",
        "unmarked_05",
        "unmarked_01",
        "21tahoek9rb",
        "M5RB_VV",
        "riot",
        "sspres",
        "riot4",
        "polkuw34",
        "7rs02",
        "7rs03",
        "7rs04",
        "7rs05",
        "7rs01",
        "riot4",
        "20ramambo",
        "polkuw99",
        "d1",
        "polkuw01",
        "polkuw05",
        "polmav2",
        "polmav",
        "ambulance5",
        "expedfire",
        "emspd21tahoe",
        "20ramambo",
        "ohio",
        "supervolito",
		

    },
    pNotify = true, -- if you have pnotify you can turn it on for a different message
    EsxNotify = false, -- Turn this on for the default ESX notification
    CheckTime = 1000, -- Check time in car, please don't go onder 1000ms if you want a good and stable server
    NotifyMessage = 'لايمكنك ركوب المركبة هذه',
}

--panic button
Config.panicButton = {}
Config.panicButton.giveXPvalue = 100 --how many xp to give for leo inside xp zones
Config.panicButton.giveXPtime = 3 --how many to sec to wait befor give xp to leo inside xp zones
Config.panicButton.noXPzones = { --zones if leo go inside will not take xp
	'sea_port_close',
	'internationa_close',
	'seaport_west_close',
	'sea_port_friday',
	'hacker',
	'my_location_safezone',
	'peace_time',
	'blaine_meeting',
	'event_start',
	'event_location',
	'event_registration',
	'event_end',
	'restart_time',
	'war_time_paleto',
	'war_time_sandy',
	'war_time_ls',
}


Config.panicButton.takeXPvalue = 20 --how many xp to give for leo inside xp zones
Config.panicButton.takeXPtime = 2 --how many to sec to wait befor give xp to leo inside xp zones
Config.panicButton.takeXPzones = { --zones if leo go inside will not take xp
	'sea_port_close',
	'restricted_area',
	'internationa_close',
	'seaport_west_close',
}

Config.panicButton.godmodLocations = {
	'hacker',
	'my_location_safezone',
	'peace_time',
	'restart_time',
}

Config.panicButton.noSirenZone = {
	'my_location_safezone',
	'peace_time',
	'helpme',
	'sea_port_close',
	'sea_port_friday',
	'blaine_meeting',
	'event_start',
	'event_location',
	'event_registration',
	'event_end',
	'restricted_area',
	'restart_time',
	'internationa_close',
	'seaport_west_close',
}

Config.panicButton.noCrimeZone = {
	'peace_time',
	'event_location',
	'sea_port_close',
	'restart_time',
	'internationa_close',
	'seaport_west_close',
}

Config.panicButton.noCrimeTime2 = {
	'NoCrimetime',
	'NewScenario',
	'MainBank',
	'SmallBanks',
	'Stores',
	'SellDrugs',
}

Config.panicButton.skyTransitionLocations = {
	'peace_time',
	'restart_time',
	'war_time_paleto',
	'war_time_sandy',
	'war_time_ls',
}

Config.panicButton.deleteAllAlarms = {
	'peace_time',
	'restart_time',
	'war_time_paleto',
	'war_time_sandy',
	'war_time_ls',
}

Config.panicButton.alarmTIMER = {
	'peace_time',
	'restart_time',
	'war_time_paleto',
	'war_time_sandy',
	'war_time_ls',
}

Config.panicButton.locationsData ={
	['hacker'] = {
		location = 'hacker',
		blipColor = nil, --nil = red and blue
		citizen = {
			--draw = 'ﺪﺟﻮﻳ ..ﻪﻳﻮﻨﺗ',--تنويه.. يوجد
			draw = '',--تنويه.. يوجد
			color = {r=200,g=0,b=0,a=255},
			notification = {
				'<font color=red>تم اطلاق صافرة الانذار لمكافحة ',
				'الخكر هو مخلوق نشأ على التخريب والخداع',
				'لاداعي للخروج من السيرفر انت محمي من الرقابة',
				'يمنع النشاط الاجرامي اثناء مكافحة الخكر',
				'لاتستعمل الأسلحة والمتفجرات حتى لاتتعرض للبان',
				'تعاونك مع الرقابة وقت الازمات يبعد عنك الشبهات',
			}
		},
		leo = {
			--draw = 'ﺶﻴﺘﻔﺘﻟﺍﻭ ﺔﺑﺎﻗﺮﻟﺍ', --تنويه من الرقابة والتفتيش
			draw = '', --تنويه من الرقابة والتفتيش
			color = {r=200,g=0,b=0,a=255},
			notification = {
				'<font color=red>تم اطلاق صافرة الانذار ل',
				'الخكر هو مخلوق نشأ على التخريب والخداع',
				'لاداعي للخروج من السيرفر انت محمي من الرقابة',
				'يمنع النشاط الاجرامي اثناء مكافحة الخكر',
				'يمكنك استيقاف اي شخص مشتبه به والتحقيق معه بشرط توافر حالة اشتباه هكر',
				'تعاونك مع الرقابة وقت الازمات يبعد عنك الشبهات',
			}
		}
	},
	['my_location_safezone'] = {
		location = 'my_location_safezone',
		blipColor = blipColor.green,
		citizen = {
			--draw = '<FONT FACE="A9eelsh">ﺶﻴﺘﻔﺘﻟﺍﻭ ﺔﺑﺎﻗﺮﻟﺍ', --الرقابة والتفتيش
			draw = '', --الرقابة والتفتيش
			color = {r=0,g=255,b=0,a=255},
			notification = {
				'<font color=00E121>تم اعلان حالة ',
				'يمنع القتل او استعمال السلاح هذه المنطقة',
				'اتبع ارشادات المراقب ولا تحاول التخريب',
				'عدم الانصياع للأوامر يعرضك للاعتقال فورا او عقوبات مشددة',
			}
		},
		leo = {
			--draw = 'ﺶﻴﺘﻔﺘﻟﺍﻭ ﺔﺑﺎﻗﺮﻟﺍ', --تنويه من الرقابة والتفتيش
			draw = '', --تنويه من الرقابة والتفتيش
			color = {r=0,g=255,b=0,a=255},
			notification = {
				'<font color=00E121>تم اعلان حالة ',
				'يمنع القتل او استعمال السلاح هذه المنطقة',
				'اتبع ارشادات المراقب ولا تحاول التخريب',
				'عدم الانصياع للأوامر يعرضك للاعتقال فورا او عقوبات مشددة',
			}
		}
	},
	['peace_time'] = {
		location = 'peace_time',
		blipColor = blipColor.purple,
		DrawText = '<FONT FACE="A9eelsh">ﺔﺣﺍﺭ ﺖﻗﻭ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '', --'ﺶﻴﺘﻔﺘﻟﺍﻭ ﺔﺑﺎﻗﺮﻟﺍ', --الرقابة والتفتيش
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'<font color=B748E2>تم اعلان ',
				'يمنع النشاط الاجرامي منعا باتا اثناء <font color=B748E2>وقت الراحة',
				'تعطل جميع الوظائف المعتمدة والعامة',
				'اتبع ارشادات المراقبين والتزم بالقوانين',
				'التخريب في وقت الراحة يعرضك لعقوبات مشددة',
			}
		},
		leo = {
			draw = '', --'ﺶﻴﺘﻔﺘﻟﺍﻭ ﺔﺑﺎﻗﺮﻟﺍ', --تنويه من الرقابة والتفتيش
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'<font color=B748E2>تم اعلان ',
				'يمنع النشاط الاجرامي منعا باتا اثناء <font color=B748E2>وقت الراحة',
				'تعطل جميع الوظائف المعتمدة والعامة',
				'يمكن تغيير وظيفتك اذا كان وقت الراحة كافي للعمل',
				'يجب اخذ موافقة تغيير الوظيفة من الاعلى رتبة في الخدمة',
				'اتبع ارشادات المراقبين والتزم بالقوانين',
				'التخريب في وقت الراحة يعرضك لعقوبات مشددة',
			}
		},
		chat = {
			[0] = 'تم إعلان '..'^6',
			[1] = '^7'..' لمدة '..'^6%s^7'..' دقيقة.'..'  يبدأ بعد '..'^3%s^7'..' دقيقة',
			[2] = '^7'..' لمدة '..'^6%s^7'..' دقيقة '..'^7'..' يمنع العمل الاجرامي وتعطل جميع الدوائر الحكومية والخاصة حتى انقضاء المدة',
			[3] = 'تم إعلان انتهاء '..'^6',
		}
	},
	['helpme'] = {
		location = 'helpme',
		blipColor = nil, --nil = red and blue
		citizen = {
			--draw = 'ﺐﻠﻃ ﻦﻣﺍ ﻞﺟﺭ ﻪﺒﺘﻧﺍ', --انتبه رجل امن طلب 
			draw = '', --انتبه رجل امن طلب 
			color = {r=226,g=139,b=0,a=255},
			notification = {
			'<font color=red>انتبه تم إرسال ',
			'يوجد رجل امن بحاجة إلى مساعدة في منطقتك',
			'حاول الابتعاد عن الخطر ان وجد في مكان الاستغاثه',
			'حاول متابعة رجل الامن الذي طلب المساعدة من بعيد',
			'يتم الآن توجيه قوة امنية لمكان الاستغاثة',
			}
		},
		leo = {
			--draw = '..ﻪﻳﻮﻨﺗ', --تنويه
			draw = '', --تنويه
			color = {r=226,g=139,b=0,a=255},
			notification = {
			'<font color=red>انتبه تم إرسال ',
			'يوجد رجل امن بحاجة إلى مساعدة',
			}
		}
	},
	['sea_port_close'] = {
		location = 'sea_port_close',
		blipColor = blipColor.black,
		DrawText = '<FONT FACE="A9eelsh">ﻖﻠﻐﻣ ﻲﺴﻴﺋﺮﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'يمنع الدخول منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن اعتقالك فورا او قتلك في حال ضبط داخل الموقع',
				'يتم الاعلان لاحقا عند الافتتاح',
				'تواجدك داخل الميناء الآن يخصم من خبرتك',
			}
		},
		leo = {
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'يمنع دخول الموطنين منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن احالة المخالفين للسجن مدة 60 شهر كحد اقصى</br><font color=red>عن أمر الرقابة والتفتيش',
				'يتم افتتاح الميناء وفقا للتعاميم والقوانين',
				'يسمح لرجال الامن دخول الميناء للضرورة فقط في حالة اشتباه تسلل مواطن',
				'اغلاق الميناء لا يعني وقت راحة وهروبك من العمل يعرضك لعقوبات مشددة',
			}
		}
	},
	['internationa_close'] = {
		location = 'internationa_close',
		blipColor = blipColor.black,
		DrawText = '<FONT FACE="A9eelsh">ﻖﻠﻐﻣ ﻲﻟﻭﺪﻟﺍ ﺭﺎﻄﻤﻟﺍ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'يمنع الدخول منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن اعتقالك فورا او قتلك في حال ضبط داخل الموقع',
				'يتم الاعلان لاحقا عند الافتتاح',
				'تواجدك داخل الميناء الآن يخصم من خبرتك',
			}
		},
		leo = {
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'يمنع دخول الموطنين منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن احالة المخالفين للسجن مدة 60 شهر كحد اقصى</br><font color=red>عن أمر الرقابة والتفتيش',
				'يتم افتتاح الميناء وفقا للتعاميم والقوانين',
				'يسمح لرجال الامن دخول الميناء للضرورة فقط في حالة اشتباه تسلل مواطن',
				'اغلاق الميناء لا يعني وقت راحة وهروبك من العمل يعرضك لعقوبات مشددة',
			}
		}
	},
	['seaport_west_close'] = {
		location = 'seaport_west_close',
		blipColor = blipColor.black,
		DrawText = '<FONT FACE="A9eelsh">ﻖﻠﻐﻣ ﻲﺑﺮﻐﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'يمنع الدخول منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن اعتقالك فورا او قتلك في حال ضبط داخل الموقع',
				'يتم الاعلان لاحقا عند الافتتاح',
				'تواجدك داخل الميناء الآن يخصم من خبرتك',
			}
		},
		leo = {
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'يمنع دخول الموطنين منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن احالة المخالفين للسجن مدة 60 شهر كحد اقصى</br><font color=red>عن أمر الرقابة والتفتيش',
				'يتم افتتاح الميناء وفقا للتعاميم والقوانين',
				'يسمح لرجال الامن دخول الميناء للضرورة فقط في حالة اشتباه تسلل مواطن',
				'اغلاق الميناء لا يعني وقت راحة وهروبك من العمل يعرضك لعقوبات مشددة',
			}
		}
	},
	['sea_port_friday'] = {
		location = 'sea_port_friday',
		blipColor = blipColor.black,
		DrawText = '<FONT FACE="A9eelsh">ﻖﻠﻐﻣ ﻲﺴﻴﺋﺮﻟﺍ ﻱﺮﺤﺒﻟﺍ ﺀﺎﻨﻴﻤﻟﺍ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=orange>تم اغلاق ',
				'السبب. يوم الجمعة إجازة للعاملين في الميناء البحري',
				'يمنع الدخول منعا باتا ويعتبر مخالف للنظام العام',
				'يحق لرجال الامن اعتقالك فورا او قتلك في حال ضبط داخل الموقع',
				'يتم الاعلان لاحقا عند الافتتاح',
			}
		},
		leo = {
			draw = '', --مغلق
			color = {r=70,g=70,b=70,a=255}
		}
	},
	['blaine_meeting'] = {
		location = 'blaine_meeting',
		blipColor = blipColor.gold,
		citizen = {	
			draw = '',
			color = {r=255,g=216,b=0,a=255},
			notification = {
				'تم افتتاح<font color=orange> ',
				'<font color=red>يمنع التخريب اوالقتل داخل الديوان',
				'<font color=red>يمنع الحديث خارج التمثيل',
				'<font color=red>يمنع الحديث عن الشكاوى او الامور البرمجية',
				'حياكم في ديوان بلاين الدعوة عامة',
			}
		},
		leo = {
			draw = '',
			color = {r=255,g=216,b=0,a=255},
			notification = {
				'تم افتتاح<font color=orange> ',
				'<font color=red>يمنع التخريب اوالقتل داخل الديوان',
				'<font color=red>يمنع الحديث خارج التمثيل',
				'<font color=red>يمنع الحديث عن الشكاوى او الامور البرمجية',
				'حياكم في ديوان بلاين الدعوة عامة',
				'يجب اخذ الاذن من العمليات لزيارة الديوان خلال وقت العمل',
			}
		}
	},
	['event_start'] = {
		location = 'event_start',
		blipColor = blipColor.purple,
		DrawText = '<FONT FACE="A9eelsh">ﺔﻴﻟﺎﻌﻔﻟﺍ ﺔﻳﺍﺪﺑ ﺔﻄﻘﻧ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'نقطة بداية الفعالية',
			}
		},
		leo = {
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'نقطة بداية الفعالية',
			}
		}
	},
	['event_location'] = {
		location = 'event_location',
		blipColor = blipColor.purple,
		citizen = {	
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'موقع الفعالية',
			}
		},
		leo = {
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'موقع الفعالية',
			}
		}
	},
	['event_registration'] = {
		location = 'event_registration',
		blipColor = blipColor.purple,
		DrawText = '<FONT FACE="A9eelsh">ﺔﻴﻟﺎﻌﻔﻟﺍ ﻞﻴﺠﺴﺗ ﻊﻗﻮﻣ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'موقع تسجيل الفعالية',
			}
		},
		leo = {
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'موقع تسجيل الفعالية',
			}
		}
	},
	['event_end'] = {
		location = 'event_end',
		blipColor = blipColor.purple,
		DrawText = '<FONT FACE="A9eelsh">ﺔﻴﻟﺎﻌﻔﻟﺍ ﺔﻳﺎﻬﻧ ﺔﻄﻘﻧ', --الكلام الي داخل الأقواس بوسط الشاشة (اذا عربي مقلوب و التعريب)
		citizen = {	
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'نقطة نهاية الفعالية',
			}
		},
		leo = {
			draw = '',
			color = {r=138,g=0,b=198,a=255},
			notification = {
				'نقطة نهاية الفعالية',
			}
		}
	},
	['restricted_area'] = {
		location = 'restricted_area',
		blipColor = blipColor.black,
		citizen = {	
			draw = '',
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=red>انتبه انت في</br><font color=gray>',
				'<font color=red>عليك مغادرة المنطقة المحظورة فورا',
				'<font color=red>تواجدك في المنطقة المحظورة يخصم من خبرتك',
			}
		},
		leo = {
			draw = '',
			color = {r=70,g=70,b=70,a=255},
			notification = {
				'<font color=red>تم اعلان حالة</br><font color=gray>',
				'<font color=red>يمنع تواجد المواطنين في المنطقة المحظورة',
				'<font color=red>المنطقة المحظورة تخصم من خبرة المواطن',
				'<font color=red>يجب توجيه المواطنين خارج المنطقة المحظورة',
				'<font color=red>يمنع التحقيق مع المتهمين داخل هذه المنطقة ويجب اقتيادهم إلى مكان آخر',
			}
		}
	},
	['restart_time'] = {
		location = 'restart_time',
		blipColor = blipColor.red,
		citizen = {	
			draw = '',
			color = {r=255,g=0,b=0,a=255},
			notification = {
				'<font color=red>تم اعلان حالة</br>',
				'يتم إطفاء السيرفر بعد انقضاء الوقت المحدد',
				'من الافضل لك ان تفصل من السيرفر قبل انتهاء الوقت',
				'يستمر التمثيل مالم يتم اعلان غير ذلك من الرقابة',
			}
		},
		leo = {
			draw = '',
			color = {r=255,g=0,b=0,a=255},
			notification = {
				'<font color=red>تم اعلان حالة</br>',
				'يتم إطفاء السيرفر بعد انقضاء الوقت المحدد',
				'من الافضل لك ان تفصل من السيرفر قبل انتهاء الوقت',
				'يستمر التمثيل مالم يتم اعلان غير ذلك من الرقابة',
			}
		},
		chat = {
			[0] = 'تم إعلان '..'^1',
			[1] = '^7'..' بعد '..'^1%s^7'..' دقيقة.'..'  يبدأ بعد '..'^3%s^7'..' دقيقة',
			[2] = '^7'..' بعد '..'^1%s^7'..' دقيقة '..'^7'..'يستمر التمثيل مالم يتم اعلان غير ذلك. افصل قبل انقضاء الوقت المحدد افضل لك',
			[3] = 'تم إعلان انتهاء '..'^1',
		}
	},	
	['other'] = { --other = استنفار
		location = 'other',
		blipColor = nil, --nil = red and blue
		citizen = {	
			draw = 'ﻲﻨﻣﺍ ﺭﺎﻔﻨﺘﺳﺍ', --استنفار امني في
			color = {r=200,g=0,b=0,a=255},
			notification = {
				'<font color=red>تم اطلاق صافرة الانذار في</br><font color=orange> ',
				'عليك المغادرة فورا وعدم محاولة الدخول حتى زوال الخطر',
				'اتبع ارشادات رجال الأمن ولا تحاول المقاومة',
				'عدم الانصياع للأوامر يعرضك للاعتقال فورا او تعريض حياتك للخطر',
				'رجال الأمن لهم حق</br><font color=red>اطلاق النار</br><font color=white>دون تنبيه في حالة <font color=orange>الاستنفار الأمني',
			}
		},
		leo = {
			draw = 'ﻲﻨﻣﺍ ﺭﺎﻔﻨﺘﺳﺍ', --استنفار امني في
			color = {r=200,g=0,b=0,a=255},
			notification = {
				'<font color=red>تم اطلاق صافرة الانذار في</br><font color=orange> ',
				'عليك الاستعانة بغرفة العمليات لأخذ الاوامر والتوجيه',
				'حافظ على حياتك وحياة المواطنين العزل في منطقة الاستنفار',
				'رجال الأمن لهم حق</br><font color=red>استعمال القوة الجبرية واطلاق النار عند الضرورة</br><font color=white>في منطقة <font color=orange>الاستنفار الأمني',
			}

		
		}
	}
}

--handcuff
Config.ArrestDistance = 3.0
Config.EnableHandcuffTimer = false
Config.HandcuffTimer = 10 * 60000 -- 10 mins

-- _holsterweapon
Config.UseESX 		  	= true
Config.cooldownPolice 	= 500 -- Will work with ESX only
Config.cooldownCitizen 	= 2000
Config.cooldownCurrent 	= 0

-- _holsterweapon
-- Add/remove weapon hashes here to be added for holster checks.
Config.Weapons = {
	"WEAPON_PISTOL",
	"WEAPON_COMBATPISTOL",
	"WEAPON_APPISTOL",
	"WEAPON_PISTOL50",
	"WEAPON_SNSPISTOL",
	"WEAPON_HEAVYPISTOL",
	"WEAPON_VINTAGEPISTOL",
	--"WEAPON_MARKSMANPISTOL",
	"WEAPON_MACHINEPISTOL",
	"WEAPON_VINTAGEPISTOL",
	"WEAPON_PISTOL_MK2",
	"WEAPON_SNSPISTOL_MK2",
	"WEAPON_FLAREGUN",
	"WEAPON_STUNGUN",
	"WEAPON_REVOLVER",
	"WEAPON_PUMPSHOTGUN",
	"WEAPON_KNIFE",
	"WEAPON_MICROSMG",
}

--ktackle Shift + G 
Config.TackleDistance	= 3.0

-----------------------------------------------------------------
-----------------------------------------------------------------
---------------------------Street Label--------------------------
-----------------------------------------------------------------
-----------------------------------------------------------------

-- Use the following variable(s) to adjust the position.
-- adjust the x-axis (left/right)
x = 1.000
-- adjust the y-axis (top/bottom)
y = 0.965
-- If you do not see the HUD after restarting script you adjusted the x/y axis too far.
	
-- Use the following variable(s) to adjust the color(s) of each element.
-- Use the following variables to adjust the color of the border around direction.
border_r = 255
border_g = 255
border_b = 255
border_a = 100

-- Use the following variables to adjust the color of the direction user is facing.
dir_r = 50
dir_g = 100
dir_b = 255
dir_a = 255

-- Use the following variables to adjust the color of the street user is currently on.
curr_street_r = 240
curr_street_g = 200
curr_street_b = 80
curr_street_a = 255

-- Use the following variables to adjust the color of the street around the player. (this will also change the town the user is in)
str_around_r = 255
str_around_g = 255 
str_around_b = 255
str_around_a = 255

-- Use the following variables to adjust the color of the city the player is in (without there being a street around them)
town_r = 255
town_g = 255
town_b = 255
town_a = 255

-----------------------------------------------------------------
-----------------------------------------------------------------
---------------------------tebext store--------------------------
-----------------------------------------------------------------
-----------------------------------------------------------------
Config.Main_esx_advancedvehicleshop = {
	DrawDistance = 10,
	-- looks like this: 'LLL NNN' The maximum plate length is 8 chars (including spaces & symbols), don't go past it!
	PlateLetters = 3,
	PlateNumbers = 3,
	PlateUseSpace = true,
	LegacyFuel = true
}
--cars.type= truck - car - boat - aircraft  h3
Config.product = {
	['market'] = {label='بقالـة', registerInRecord=true, rewardMoney=2000000, rewardXp=0, doubleXP_Store=12, rewardBlackMoney=0, cars={{label='مرسيدس يومنج',model='Unimog',type='truck',priceold=0,category='truck250',levelold=50,trunkkg=250,higherprice=4500000,lowerprice=3800000}}, weapons={}, chat='', msg1='ﺮﺟﺎﺘﻤﻟﺍ ﺔﻗﺎﺑ', msg2='ﺓﺄﻓﺎﻜﻣ ﻎﻠﺒﻣﻭ ﻮﻠﻴﻛ 052 ﺔﻨﺣﺎﺷﻭ ﺔﻟﺎﻘﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﺔﻟﺎﻘﺒﻟﺍ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'},

	['sponserBronze'] = {label='راعي برونزي', endTime = 15, registerInRecord=true, rewardMoney=650000, rewardXp=2500, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻱﺰﻧﻭﺮﺑ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻱﺰﻧﻭﺮﺑ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻱﺰﻧﻭﺮﺑ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'},
	['sponserSilver'] = {label='راعي فضي', endTime = 20, registerInRecord=true, rewardMoney=1000000, rewardXp=3500, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﻀﻓ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻲﻀﻓ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﻀﻓ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'},
	['sponserGold'] = {label='راعي ذهبي', endTime = 20, registerInRecord=true, rewardMoney= 1500000, rewardXp=4500, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﺒﻫﺫ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻲﺒﻫﺫ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﺒﻫﺫ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'},
	['sponserPlatinum'] = {label='راعي بلاتيني', endTime = 25, registerInRecord=true, doubleXP_Store=12, rewardMoney=2500000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﻨﻴﺗﻼﺑ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻲﻨﻴﺗﻼﺑ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﻨﻴﺗﻼﺑ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'}, -- ضعف خبرة 12 ساعة
	['sponserDiamond'] = {label='راعي الماسي', endTime = 30, registerInRecord=true, doubleXP_Store=12, rewardMoney=3000000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﺳﺎﻤﻟﺍ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻲﺳﺎﻤﻟﺍ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﺳﺎﻤﻟﺍ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'}, -- ضعف خبرة 12 ساعة
	['sponserOfficial'] = {label='راعي رسمي', endTime = 30, registerInRecord=true, doubleXP_Store=24, rewardMoney=4500000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﻤﺳﺭ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻲﻤﺳﺭ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﻤﺳﺭ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'}, -- ضعف خبرة 24 ساعة
	['sponserstrategic'] = {label='راعي استراتيجي', endTime = 30, registerInRecord=true, doubleXP_Store=24, rewardMoney=5500000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﺠﻴﺗﺍﺮﺘﺳ ﻲﻋﺍﺭ', msg2='ﺔﻴﻟﺎﻣ ﺓﺄﻓﺎﻜﻣﻭ ﻲﺠﻴﺗﺍﺮﺘﺳﺍ ﻲﻋﺍﺭ ﺔﻗﺎﻄﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﺠﻴﺗﺍﺮﺘﺳﺍ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'}, -- ضعف خبرة 24 ساعة

	['doubleXP6'] = {label='ضعف خبرة 6 ساعات', registerInRecord=true, doubleXP_Store=6}, -- ضعف خبرة 6 ساعات
	['doubleXP12'] = {label='ضعف خبرة 12 ساعة', registerInRecord=true, doubleXP_Store=12}, -- ضعف خبرة 12 ساعة
	['doubleXP24'] = {label='ضعف خبرة 24 ساعة', registerInRecord=true, doubleXP_Store=24}, -- ضعف خبرة 24 ساعة
	['doubleXP0'] = {label='ضعف خبرة 24 ساعة', registerInRecord=true, doubleXP_Store=0.25},
	['ksaday1'] = {label='اليوم الوطني', registerInRecord=true, doubleXP_Store=24, rewardMoney=3000000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﻨﻃﻮﻟﺍ ﻡﻮﻴﻟﺍ', msg2='ﻲﻨﻃﻮﻟﺍ ﻡﻮﻴﻟﺍ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﺒﻫﺫ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'},

	['ksaday2'] = {label='اليوم الوطني بريميوم', registerInRecord=true, doubleXP_Store=24, rewardMoney=3000000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻲﻨﻃﻮﻟﺍ ﻡﻮﻴﻟﺍ', msg2='ﻲﻨﻃﻮﻟﺍ ﻡﻮﻴﻟﺍ', msg3='ﻚﻤﻋﺩ ﻰﻠﻋ ﺍﺮﻜﺷ ﻲﺒﻫﺫ ﻲﻋﺍﺭ ﺔﻗﺎﺑ ﻲﻓ ﻙﺍﺮﺘﺷﻻﺍ ﻰﻬﺘﻧﺍ', color1='~w~', color2='~g~', color3='~r~'},
	
	['eid2020'] = {label='باقة عيد الفطر', rewardMoney=3000000, rewardXp=0, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﺮﻄﻔﻟﺍ ﺪﻴﻋ ﺔﻗﺎﺑ', msg2='ﺪﻴﻌﻟﺍ ﺓﺄﻓﺎﻜﻣ ﺖﻤﻠﺘﺳﺍ', msg3='', color1='~w~', color2='~g~', color3='~r~'}, -- ضعف خبرة 24 ساعة
	
	['freeStart'] = {label='البداية المجانية 500 ألف 10 لفلات',  registerInRecord=true, rewardMoney=500000, rewardXp=28500, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﺔﻴﻧﺎﺠﻤﻟﺍ ﺔﻳﺍﺪﺒﻟﺍ ﺔﻗﺎﺑ', msg2='ﺔﻳﺍﺪﺒﻟﺍ ﺔﻗﺎﺑ ﺖﻤﻠﺘﺳﺍ', msg3='', color1='~w~', color2='~g~', color3='~r~'},
	
	['urusmarkt'] = {label='لامبورجيني اوروس', registerInRecord=true, rewardMoney=500000, rewardXp=0, doubleXP_Store=0, rewardBlackMoney=0, cars={{label='لامبورجيني اوروس',model='urusb',type='car',priceold=0,category='mazad',levelold=50,trunkkg=250,higherprice=4500000,lowerprice=3800000}}, weapons={}, chat='', msg1='ﺕﺎﺒﻛﺮﻤﻟﺍ ﺔﻗﺎﺑ', msg2='ﺓﺄﻓﺎﻜﻣ ﻎﻠﺒﻣﻭ ﺔﺒﻛﺮﻣ ﻚﺤﻨﻣ ﻢﺗ', msg3='1', color1='~w~', color2='~g~', color3='~r~'},

	['bestActor'] = {label='مكافأة أفضل ممثل', registerInRecord=true, rewardMoney=500000, rewardXp=2000, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻞﺜﻤﻣ ﻞﻀﻓﺃ ﺓﺄﻓﺎﻜﻣ', msg2='ﻎﻠﺒﻣﻭ ﺔﻴﻓﺎﺿﺇ ﺓﺮﺒﺧ ﻰﻠﻋ ﺖﻠﺼﺣ', msg3='', color1='~w~', color2='~g~', color3='~r~'},
	['bestEmloyee'] = {label='مكافأة أفضل موظف', registerInRecord=true, rewardMoney=500000, rewardXp=2000, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﻒﻇﻮﻣ ﻞﻀﻓﺃ ﺓﺄﻓﺎﻜﻣ', msg2='ﻎﻠﺒﻣﻭ ﺔﻴﻓﺎﺿﺇ ﺓﺮﺒﺧ ﻰﻠﻋ ﺖﻠﺼﺣ', msg3='', color1='~w~', color2='~g~', color3='~r~'},
	['bestGiver'] = {label='مكافأة عطاء متميز', registerInRecord=true, rewardMoney=500000, rewardXp=2000, rewardBlackMoney=0, cars={}, weapons={}, chat='', msg1='ﺰﻴﻤﺘﻣ ﺀﺎﻄﻋ ﺓﺄﻓﺎﻜﻣ', msg2='ﻎﻠﺒﻣﻭ ﺔﻴﻓﺎﺿﺇ ﺓﺮﺒﺧ ﻰﻠﻋ ﺖﻠﺼﺣ', msg3='', color1='~w~', color2='~g~', color3='~r~'},
	['eid2023'] = {label='باقة عيد الفطر', registerInRecord=true, rewardMoney=0, rewardXp=0, doubleXP_Store=24, rewardBlackMoney=0, cars={{label='تويوتا هايلكس 2012',model='jrl10',type='car',priceold=99500,category='n40kg',levelold=10,trunkkg=40,higherprice=165000,lowerprice=75000}}, weapons={}, chat='', msg1='ﺮﻄﻔﻟﺍ ﺪﻴﻋ ﺔﻗﺎﺑ',  msg2=' 40 ﺔﻟﻮﻤﺣ - ﻲﻀﻓ ﻲﻋﺍﺭ - ﺔﻋﺎﺳ 42 ﺓﺮﺒﺧ ﻒﻌﺿ ﻚﺤﻨﻣ ﻢﺗ', msg3='1', color1='~w~', color2='~g~', color3='~r~'}, -- ضعف خبرة 24 ساعة

	-- ['eid2023'] = {label='باقة عيد الفطر', registerInRecord=true, rewardMoney=0, rewardXp=0, doubleXP_Store=24, rewardBlackMoney=0, cars={{label='تويوتا هايلكس 2012',model='jrl10',type='car',priceold=99500,category='n40kg',levelold=10,trunkkg=40,higherprice=165000,lowerprice=75000}}, weapons={}, chat='', msg1='ﺮﻄﻔﻟﺍ ﺪﻴﻋ ﺔﻗﺎﺑ', msg2=' 40 ﺔﻟﻮﻤﺣ - ﻲﻀﻓ ﻲﻋﺍﺭ - ﺓﺮﺒﺧ ﻒﻌﺿ ﺮﻄﻔﻟﺍ ﺪﻴﻋ ﺔﻗﺎﺑ ﻚﺤﻨﻣ ﻢﺗ', msg3='1', color1='~w~', color2='~g~', color3='~r~'},
}

Config.scalformtime = 800


-----------------------------------------------------------------
-----------------------------------------------------------------
--------------------------control system-------------------------
-----------------------------------------------------------------
-----------------------------------------------------------------

Config.controlSystem = {
	['visa'] = {label='سحب تأشيرة', msg1='', msg2='', msg3='', color1='~w~', color2='~g~', color3='~r~'},
	['warn'] = {label='انذار', msg1='', msg2='', msg3='', color1='~w~', color2='~g~', color3='~r~'},
}


Config.controlSystem.scalformtime = 800


---no crime time

Config.NoCrimePolyZone = {   
	['Sandy'] = {
		vector2(5366.62, 887.6909),
		vector2(-3711.224, 892.96),
		vector2(-3018.54, 5289.072),
		vector2(4390.253, 5147.509)
	},

	['Poleto'] = {
		vector2(4390.253, 5147.509),
		vector2(-3018.54, 5289.072),
		vector2(-3242.051, 8333.243),
		vector2(5478.356, 8008.492)
	},


	['Cayo'] = {
		vector2(2900.3398, -4106.9873),
		vector2(3646.1626, -6156.999),
		vector2(6583.7627, -5482.4258),
		vector2(5105.5293, -3125.8301)
	},
	['Los'] = {
		vector2(5366.62, 887.6909),
		vector2(-3711.224, 892.96),
		vector2(-3744.5549, -3909.158),
		vector2(4021.0032, -3934.0510)
	},
}