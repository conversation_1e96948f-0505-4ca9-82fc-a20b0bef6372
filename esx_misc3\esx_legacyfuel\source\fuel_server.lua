
if Config_esx_legacyfuel.UseESX then

	RegisterServerEvent('givefuel:weapon')
	AddEventHandler('givefuel:weapon', function()
		local xPlayer = ESX.GetPlayerFromId(source)
		xPlayer.addWeapon('WEAPON_PETROLCAN', 4500)
	end)
	RegisterServerEvent('fuel:pay')
	AddEventHandler('fuel:pay', function(price)
		local xPlayer = ESX.GetPlayerFromId(source)
		local amount = ESX.Math.Round(price)

		if price > 0 then
			xPlayer.removeMoney(amount)
		end
	end)
end
