local alertId = 5

RegisterServerEvent('tgiann-policeAlert:server:normal')
AddEventHandler('tgiann-policeAlert:server:normal', function(alertName, streed, sex, x, y)
    alertId = alertId + 1
    TriggerClientEvent("tgiann-policeAlert:client:Normal", -1, alertId, alertName, streed, sex, x, y, os.date("%H:%M"))
end)

RegisterServerEvent('tgiann-policeAlert:server:full')
AddEventHandler('tgiann-policeAlert:server:full', function(alertName, streed, car, plate, color, sex, x, y)
    alertId = alertId + 1
    TriggerClientEvent("tgiann-policeAlert:client:full", -1, alertId, alertName, streed, car, plate, color, sex, x, y, os.date("%H:%M"))
end)

RegisterServerEvent('tgiann-policeAlert:server:codeAlert')
AddEventHandler('tgiann-policeAlert:server:codeAlert', function(code)
    TriggerClientEvent("tgiann-policeAlert:client:codeAlert", -1, code)
end)

RegisterServerEvent('tgiann-policeAlert:server:yonel')
AddEventHandler('tgiann-policeAlert:server:yonel', function(alertId, gpsName)
    TriggerClientEvent("tgiann-policeAlert:client:yonel", -1, alertId, gpsName)
end)

--qb police alert
RegisterServerEvent('police:server:policeAlert')
AddEventHandler('police:server:policeAlert', function(text)
    TriggerClientEvent("tgiann-policeAlert:alert", source, text)
end)