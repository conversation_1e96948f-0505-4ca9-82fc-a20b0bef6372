local Keys = {
  ["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
  ["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
  ["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
  ["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
  ["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
  ["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
  ["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
  ["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
  ["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}

local Player          = nil
local CruisedSpeed    = 0
local CruisedSpeedKm  = 0
local VehicleVectorY  = 0

Citizen.CreateThread(function ()
  while true do
    Wait(0)
    if IsControlJustPressed(1, Keys['Y']) and IsDriver() then
      Player = PlayerPedId()
      TriggerCruiseControl()
    end
  end
end)

function TriggerCruiseControl ()
  if CruisedSpeed == 0 and IsDriving() then
    if GetVehiculeSpeed() > 0 and GetVehicleCurrentGear(GetVehicle()) > 0  then
		local playerPed = PlayerPedId()
		local veh = GetVehiclePedIsIn(playerPed)
		local class = GetVehicleClass(veh)
		
		--print ('class= ', class)
		
		if ( class ~= 14) and ( class ~= 13) and ( class ~= 16) then
	
			CruisedSpeed = GetVehiculeSpeed()
			CruisedSpeedKm = TransformToKm(CruisedSpeed)
		
			--ESX.ShowNotification(_U('activated') .. ': ~b~ ' .. CruisedSpeedKm .. ' km/h')
			
			exports.pNotify:SendNotification({
				text = "<font size=5><p align=center><b>مثبت سرعه: <font color=orange>" .. CruisedSpeedKm .. "<font color=gray> كم/ساعه",	   
					type = "alert",
					timeout = 7000,
					layout = "centerleft",
					queue = "left",
					killer = true
			})
		
			Citizen.CreateThread(function ()
				while CruisedSpeed > 0 and IsInVehicle() == Player do
					Wait(0)
			
					if not IsTurningOrHandBraking() and GetVehiculeSpeed() < (CruisedSpeed - 1.5) then
						CruisedSpeed = 0
						--ESX.ShowNotification(_U('deactivated'))
						exports.pNotify:SendNotification({
							text = "<font color=red size=5><p align=center><b>إلغاء مثبت السرعه",	   
								type = "alert",
								timeout = 5000,
								layout = "centerleft",
								queue = "left",
								killer = true
						})
						Wait(2000)
						break
					end
			
					if not IsTurningOrHandBraking() and IsVehicleOnAllWheels(GetVehicle()) and GetVehiculeSpeed() < CruisedSpeed then
						SetVehicleForwardSpeed(GetVehicle(), CruisedSpeed)
					end
			
					if IsControlJustPressed(1, Keys['Y']) then
						CruisedSpeed = GetVehiculeSpeed()
						CruisedSpeedKm = TransformToKm(CruisedSpeed)
					end
			
					if IsControlJustPressed(2, 72) then
						CruisedSpeed = 0
						--ESX.ShowNotification('إلغاء مثبت السرعه')
						exports.pNotify:SendNotification({
							text = "<font color=red size=5><p align=center><b>إلغاء مثبت السرعه",	   
								type = "alert",
								timeout = 5000,
								layout = "centerleft",
								queue = "left",
								killer = true
						})
						Wait(2000)
						break
					end
				end
			end)
		elseif class == 14 then --boat
			exports.pNotify:SendNotification({
				text = "<font color=red size=5><p align=center><b>لايمكن استعمال مثبت السرعه بالقارب</font>",	   
					type = "error",
					timeout = 5000,
					layout = "centerleft",
					queue = "left",
					killer = true
			})
		elseif class == 13 then --cycle
			exports.pNotify:SendNotification({
				text = "<font color=red size=5><p align=center><b>لايمكن استعمال مثبت السرعه بالدراجة الهوائية</font>",	   
					type = "error",
					timeout = 5000,
					layout = "centerleft",
					queue = "left",
					killer = true
			})
		elseif class == 16 then --plane
			exports.pNotify:SendNotification({
				text = "<font color=red size=5><p align=center><b>لايمكن استعمال مثبت السرعه بالطائرات</font>",	   
					type = "error",
					timeout = 5000,
					layout = "centerleft",
					queue = "left",
					killer = true
			})			
		end	
    end
  end
end

function IsTurningOrHandBraking ()
  return IsControlPressed(2, 76) or IsControlPressed(2, 63) or IsControlPressed(2, 64)
end

function IsDriving ()
  return IsPedInAnyVehicle(Player, false)
end

function GetVehicle ()
  return GetVehiclePedIsIn(Player, false)
end

function IsInVehicle ()
  return GetPedInVehicleSeat(GetVehicle(), -1)
end

function IsDriver ()
  return GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) == PlayerPedId()
end

function GetVehiculeSpeed ()
  return GetEntitySpeed(GetVehicle())
end

function TransformToKm (speed)
  return math.floor(speed * 3.6 + 0.5)
end
