<html>
    <head>
        <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.1.1/howler.min.js" type="text/javascript"></script>
        <script>
            var audioPlayer = null;
            var pressurePlayer = null;
            window.addEventListener('message', function(event) {
                if(event.data.command == "play"){
                  audioPlayer = new Howl({src: ["./" + event.data.file + ".ogg"]});
                  audioPlayer.volume(0.5);
                  audioPlayer.play();
                }
            });
        </script>
    </head>
</html>
