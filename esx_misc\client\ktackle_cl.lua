-- 2018 <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>

local Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}

local PlayerData                = {}
local PoliceJob 				= 'police'
local banksecurityJob 		      		= 'agent'
local admin 		      		= 'admin'
MycurrentJobLeo = false
local isTackling				= false
local isGettingTackled			= false

local tackleLib					= 'missmic2ig_11'
local tackleAnim 				= 'mic_2_ig_11_intro_goon'
local tackleVictimAnim			= 'mic_2_ig_11_intro_p_one'

local lastTackleTime			= 0
local isRagdoll					= false


Citizen.CreateThread(function()
		
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end
	
	PlayerData = ESX.GetPlayerData()
	isLoeJob()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
	
	isLoeJob()
	
	TriggerServerEvent('esx_misc:updateCache', 'job', job)
	
end)

function isLoeJob()
	while PlayerData.job == nil do
		Citizen.Wait(500)
	end
	
	local check = false
	
	for i=1, #LeoJobs, 1 do
		if PlayerData.job.name == LeoJobs[i] then
			check = true
			break
		end
	end

	if check then
		MycurrentJobLeo = true
		--Config.cooldownCurrent = Config.cooldownPolice
	else
		MycurrentJobLeo = false
		--Config.cooldownCurrent = Config.cooldownCitizen
	end
end	
function StartPedRagdoll()
	Citizen.CreateThread(function()
		local ped = PlayerPedId()
		while isRagdoll do			
			SetPedToRagdoll(ped, 1000, 1000, 0, 0, 0, 0)
			Citizen.Wait(0)
		end
	end)
end

RegisterNetEvent('esx_misc:getTackled')
AddEventHandler('esx_misc:getTackled', function(target)
	isGettingTackled = true

	local playerPed = PlayerPedId()
	local targetPed = GetPlayerPed(GetPlayerFromServerId(target))

	RequestAnimDict(tackleLib)

	while not HasAnimDictLoaded(tackleLib) do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(PlayerPedId(), targetPed, 11816, 0.25, 0.5, 0.0, 0.5, 0.5, 180.0, false, false, false, false, 2, false)
	TaskPlayAnim(playerPed, tackleLib, tackleVictimAnim, 8.0, -8.0, 3000, 0, 0, false, false, false)

	Citizen.Wait(3000)
	DetachEntity(PlayerPedId(), true, false)

	isRagdoll = true
	StartPedRagdoll()
	Citizen.Wait(3000)
	isRagdoll = false

	isGettingTackled = false
end)

RegisterNetEvent('esx_misc:playTackle')
AddEventHandler('esx_misc:playTackle', function()
	local playerPed = PlayerPedId()

	RequestAnimDict(tackleLib)

	while not HasAnimDictLoaded(tackleLib) do
		Citizen.Wait(10)
	end

	TaskPlayAnim(playerPed, tackleLib, tackleAnim, 8.0, -8.0, 3000, 0, 0, false, false, false)

	Citizen.Wait(3000)

	isTackling = false

end)

-- Main thread
Citizen.CreateThread(function()
    local ShowNotificationDone1 = false
	while true do
		local sleep = 150

		if IsControlPressed(0, Keys['LEFTSHIFT']) and IsControlPressed(0, Keys['G']) and not isTackling and GetGameTimer() - lastTackleTime > 10 * 1000 and MycurrentJobLeo then
			sleep = 0
			--Citizen.Wait(10)
			local closestPlayer, distance = ESX.Game.GetClosestPlayer()

			if distance ~= -1 and distance <= Config.TackleDistance and not isTackling and not isGettingTackled and not IsPedInAnyVehicle(PlayerPedId()) and not IsPedInAnyVehicle(GetPlayerPed(closestPlayer)) then
				sleep = 0
				isTackling = true
				lastTackleTime = GetGameTimer()

				TriggerServerEvent('esx_misc:tryTackle', GetPlayerServerId(closestPlayer))
			else
			if not ShowNotificationDone1 then
			sleep = 0
			--ESX.ShowNotification("لايوجد لاعب قريب")
			ESX.ShowNotification("لايوجد لاعب قريب")
			ShowNotificationDone1 = true
			Citizen.Wait(1000)
			ShowNotificationDone1 = false
			end
			end
		end
		Wait(sleep)
	end
end)
