if not ESX then
	TriggerEvent('esx:getSharedObjectp23Njd23byabd', function(obj) ESX = obj end)
end
local ZonesTable = {}
local LocationZonesTable = {}

RegisterServerEvent("zahya_police_safe_zone:SV:CreateZone")
AddEventHandler("zahya_police_safe_zone:SV:CreateZone", function(ZoneCoords, ZoneDistance)
	table.insert(ZonesTable, { ["TableZoneCoords"] = ZoneCoords, ["TableZoneDistance"] = ZoneDistance, ["TableZoneName"] = ZoneName })
	TriggerClientEvent("zahya_police_safe_zone:UpdateZonesInfo", -1, ZonesTable, ZoneCoords, ZoneDistance)
end)

RegisterServerEvent("zahya_police_safe_zone:SV:AddZone")
AddEventHandler("zahya_police_safe_zone:SV:AddZone", function(ZoneCoords, ZoneName, ZoneDistance)
	table.insert(LocationZonesTable, { ["TableZoneCoords"] = ZoneCoords, ["TableZoneName"] = ZoneName, ["TableZoneDistance"] = ZoneDistance })
	TriggerClientEvent("zahya_police_safe_zone:addNewZone", -1, ZoneCoords, ZoneDistance,ZoneName)
end)

RegisterServerEvent("zahya_police_safe_zone:SV:RemoveZone")
AddEventHandler("zahya_police_safe_zone:SV:RemoveZone", function(ZoneCoords)
	if ZoneCoords ~= nil then
		for k,v in pairs(ZonesTable) do
			if v.TableZoneCoords == ZoneCoords then
				table.remove(ZonesTable,k)
				TriggerClientEvent("zahya_police_safe_zone:UpdateZonesInfo", -1, ZonesTable, ZoneCoords)
				TriggerClientEvent("zahya_police_safe_zone:UpdateStatsInfo", -1)
			end
		end
	end
end)

RegisterServerEvent("zahya_police_safe_zone:SV:RemoveLocationZone")
AddEventHandler("zahya_police_safe_zone:SV:RemoveLocationZone", function(ZoneCoords, ZoneName)
	if ZoneCoords ~= nil then
		for k,v in pairs(LocationZonesTable) do
			if v.TableZoneName == ZoneName then
				if v.TableZoneCoords == ZoneCoords then
					table.remove(LocationZonesTable,k)
					TriggerClientEvent("zahya_police_safe_zone:RemoveOldZone", -1, ZoneCoords)
					TriggerClientEvent("zahya_police_safe_zone:UpdateStatsInfo", -1)
				end
			end
		end
	end
end)

ESX.RegisterServerCallback('zahya_police_safe_zone:GetZonesInfo', function(source, cb)
	cb(ZonesTable, LocationZonesTable)
end)

-------------------

local ZonesTableBlack = {}
local LocationZonesTableBlack = {}

RegisterServerEvent("zahya_police_safe_black_zone:SV:CreateZone")
AddEventHandler("zahya_police_safe_black_zone:SV:CreateZone", function(ZoneCoords, ZoneDistance)
	table.insert(ZonesTableBlack, { ["TableZoneCoords"] = ZoneCoords, ["TableZoneDistance"] = ZoneDistance })
	TriggerClientEvent("zahya_police_safe_black_zone:UpdateZonesInfo", -1, ZonesTableBlack, ZoneCoords, ZoneDistance)
end)

RegisterServerEvent("zahya_police_safe_black_zone:SV:AddZone")
AddEventHandler("zahya_police_safe_black_zone:SV:AddZone", function(ZoneCoords, ZoneName, ZoneDistance)
	table.insert(LocationZonesTableBlack, { ["TableZoneCoords"] = ZoneCoords, ["TableZoneName"] = ZoneName, ["TableZoneDistance"] = ZoneDistance })
	TriggerClientEvent("zahya_police_safe_black_zone:addNewZone", -1, ZoneCoords, ZoneDistance)
end)

RegisterServerEvent("zahya_police_safe_black_zone:SV:RemoveZone")
AddEventHandler("zahya_police_safe_black_zone:SV:RemoveZone", function(ZoneCoords)
	if ZoneCoords ~= nil then
		for k,v in pairs(ZonesTableBlack) do
			if v.TableZoneCoords == ZoneCoords then
				table.remove(ZonesTableBlack,k)
				TriggerClientEvent("zahya_police_safe_black_zone:UpdateZonesInfo", -1, ZonesTableBlack, ZoneCoords)
				TriggerClientEvent("zahya_police_safe_black_zone:UpdateStatsInfo", -1)
			end
		end
	end
end)

RegisterServerEvent("zahya_police_safe_black_zone:SV:RemoveLocationZone")
AddEventHandler("zahya_police_safe_black_zone:SV:RemoveLocationZone", function(ZoneCoords, ZoneName)
	if ZoneCoords ~= nil then
		for k,v in pairs(LocationZonesTableBlack) do
			if v.TableZoneName == ZoneName then
				if v.TableZoneCoords == ZoneCoords then
					table.remove(LocationZonesTableBlack,k)
					TriggerClientEvent("zahya_police_safe_black_zone:RemoveOldZone", -1, ZoneCoords)
					TriggerClientEvent("zahya_police_safe_black_zone:UpdateStatsInfo", -1)
				end
			end
		end
	end
end)

ESX.RegisterServerCallback('zahya_police_safe_black_zone:GetZonesInfo', function(source, cb)
	cb(ZonesTableBlack, LocationZonesTableBlack)
end)