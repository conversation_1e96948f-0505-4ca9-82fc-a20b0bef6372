/*-----------------------------------------------------------------------------------------

	Wraith ARS 2X
	Created by <PERSON><PERSON><PERSON>
	
	For discussions, information on future updates, and more, join 
	my Discord: https://discord.gg/fD4e6WD 
	
	MIT License

	Copyright (c) 2020-2021 WolfKnight

	Permission is hereby granted, free of charge, to any person obtaining a copy
	of this software and associated documentation files (the "Software"), to deal
	in the Software without restriction, including without limitation the rights
	to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
	copies of the Software, and to permit persons to whom the Software is
	furnished to do so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.

-----------------------------------------------------------------------------------------*/

@font-face {
	font-family: "Seg-7";
	src: url( "fonts/Segment7Standard.otf" );
}

@font-face {
	font-family: "Heebo";
	src: url( "fonts/Heebo-Bold.ttf" );
}

@font-face {
	font-family: "Heebo-Regular";
	src: url( "fonts/Heebo-Regular.ttf" );
}

@font-face {
	font-family: "Plate-Font";
	src: url( "fonts/plate-font.ttf" );
}

@font-face {
	font-family: "Plate-Font-Hilite";
	src: url( "fonts/plate-font-hilite.ttf" );
}

@font-face {
	font-family: "Plate-Font-Lolite";
	src: url( "fonts/plate-font-lolite.ttf" );
}

@font-face {
	font-family: "Plate-Font-Shadow";
	src: url( "fonts/plate-font-Shadow.ttf" );
}

* {
	font-family: 'Heebo', Verdana, Geneva, Tahoma, sans-serif;
	font-size: 13px;
	box-sizing: border-box;
	user-select: none;
}

body {
	overflow: hidden;
}

/* Removes the outline when buttons have been clicked */
button:focus { outline: none; }

#radarFrame {
	width: 715px;
	height: 230px;

	position: absolute;
	margin: auto;
	
	top: calc( 100% - 230px );
	left: calc( 100% - 715px );

	background-image: url( "images/frame.png" );

	/* Settings for scaling */
	transform: scale( 1.0 );
	transform-origin: 0 0;
	
	transition: transform 0.5s;

	z-index: 2;
}

#radarFrame .frame_border {
	width: 685px;
	height: 210px;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	background-color: rgb( 20, 22, 18 );

	clip-path: polygon( 20px 2px, 665px 2px, 682px 30%, 682px 70%, 665px 208px, 20px 208px, 3px 70%, 3px 30% );
}

.radar_container {
	width: 675px;
	height: 200px;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	display: grid;
	grid-template-columns: 1fr 635px 1fr;
}

.panel_side {
	width: 100%;
	height: 100%;
	z-index: 5;
	box-shadow: inset 0px 20px 20px -15px rgba( 0, 0, 0, 0.4 );
}
	.panel_left {
		clip-path: polygon( 0 30%, 80% 0, 100% 0, 100% 100%, 80% 100%, 0 70% );
		transform: translateX( 1px ); 
		background-image: url( "images/bg_left.png" );
		background-repeat: no-repeat;
	}

	.panel_right {
		clip-path: polygon( 0 0, 20% 0, 100% 30%, 100% 70%, 20% 100%, 0 100% );   
		transform: translateX( -1px ); 
		background-image: url( "images/bg_right.png" );
		background-repeat: no-repeat;
	}
	
.plate_blue {
	color: rgb(0, 0, 163);
}

.plate_yellow {
	color: rgb(255, 255, 0);
}

.green {
	color: rgb( 0, 255, 0 );
}

.red {
	color: rgb( 255, 0, 0 );
}

.arrow {
	width: 11px;
	height: 15.4px;
	min-width: 11px;
	min-height: 15.4px;
	margin: 8px auto;
	background-color: rgb( 0, 0, 0 );
	clip-path: polygon( 50% 0, 100% 55%, 65% 55%, 65% 100%, 35% 100%, 35% 55%, 0 55% );
}
	.arrow_down {
		transform: rotate( 180deg );
	}

.active {
	color: rgb( 242, 70, 24 );
	text-shadow: 0 0 6px rgba( 235, 62, 15, 0.5 );
}
	.active_arrow {
		background-color: rgb( 242, 70, 24 );
	}

#radar {
	background-image: url( "images/bg.png" );
	background-repeat: no-repeat;

	height: 100%;
	width: 100%;

	display: grid;
	grid-template-columns: 45px 50px 150px 35px 50px 135px 35px 135px;
	gap: 0 0;

	box-shadow: inset 0px 20px 20px -15px rgba( 0, 0, 0, 0.4 );
}
	#radar .label {
		font-size: 15px;
		text-align: center; 
		width: 100%; 
		position: absolute;
		color: rgb( 255, 255, 255 );
	}
		#radar .label_top {
			top: 0;
			left: 0;
			padding-top: 3px;
		}

		#radar .label_bottom {
			bottom: 0;
			left: 0;
			padding-bottom: 3px;
		}

	#radar .pwr_button_container {
		display: flex;
		flex-direction: column;
	}
		#radar .pwr_button_container .pwr_button {
			width: 45px;
			height: 45px;
			margin: auto;
			background: linear-gradient( to bottom, rgba( 230, 230, 230, 0.8 ), rgb( 40, 168, 40 ) 10%, rgb( 0, 134, 0 ) );
			box-shadow: 0px 0px 3px 0px rgb( 80, 80, 80 );
			text-align: center;
			font-size: 14px;
			color: rgb( 34, 34, 34 );
			line-height: 45px;
			border-radius: 10px;
		}
			#radar .pwr_button_container .pwr_button:hover {
				background: linear-gradient( to bottom, rgba( 240, 240, 240, 0.8 ), rgb( 50, 178, 50 ) 10%, rgb( 0, 144, 0 ) );
			}

			#radar .pwr_button_container .pwr_button:active {
				background: linear-gradient( to bottom, rgba( 220, 220, 220, 0.8 ), rgb( 30, 158, 30 ) 10%, rgb( 0, 124, 0 ) );
				box-shadow: inset 0px 0px 3px 0px rgb( 80, 80, 80 );
			}

	#radar .modes_container {
		display: grid;
		grid-template-rows: 1fr 1fr;
	}
		#radar .modes_container .modes {
			display: flex;
			flex-direction: column;

			padding: 10px 0;
		}
			#radar .modes_container .modes p {
				margin: auto;
			}
		
		#radar .modes_container .fast_top {
			margin-top: 15px;
			margin-bottom: 10px;
			padding-top: 10px;
			padding-bottom: 0;
		}

		#radar .modes_container .fast_bottom {
			margin-top: 10px;
			margin-bottom: 15px;
			padding-top: 0;
			padding-bottom: 10px;
		}
	
	#radar .speeds_container {
		width: 100%;
		display: flex;
		flex-direction: column;
	}
		#radar .speeds_container .display {
			display: grid;
			grid-template-columns: 1fr;

			height: 70px;

			border: 2px solid rgb( 0, 0, 0 );
			
			margin: auto 0;

			/* If you would like your strong target window to be solid rather than have a gradient, swap the comment for the 2 lines below */
			background-color: rgb( 61, 18, 0 );
			/* background: linear-gradient( to bottom, rgb( 52, 13, 1 ), rgb( 57, 16, 0 ) 40%, rgb( 65, 25, 4 ) 85% ); */
		}   
			#radar .speeds_container .display p {
				grid-row-start: 1;
				grid-column-start: 1;

				font-size: 70px;
				text-align: center;

				line-height: 115%;

				margin: 0 auto;
				letter-spacing: 10px;

				margin-right: -9px;
			}
		
		#radar .speeds_container .fast {
			height: 60px;

			/* If you would like your fast target window to be solid rather than have a gradient, swap the comment for the 2 lines below */
			background-color: rgb( 50, 0, 0 );
			/* background: linear-gradient( to bottom, rgb( 40, 0, 0 ), rgb( 45, 0, 0 ) 40%, rgb( 50, 0, 0 ) 85% ); */
		}
			#radar .speeds_container .fast p {
				font-size: 60px;
				line-height: 110%;
				margin-right: -8px;
			}

		#radar .speeds_container .fast_top {
			margin-top: 27px;
		}

		#radar .speeds_container .fast_bottom {
			margin-bottom: 27px;
		}

	#radar .speed_arrows_container {
		display: flex;
		flex-direction: column;
	}
		#radar .speed_arrows_container .speed_arrows {
			height: 70px; 
	
			display: flex; 
			flex-direction: column; 
			align-items: center;
			justify-content: center;
			
			margin: auto 0; 
		}

		#radar .speed_arrows_container .fast_top {
			margin-top: 22px;
		}

		#radar .speed_arrows_container .fast_bottom {
			margin-bottom: 22px;
		}
	
	#radar .patrol_and_logo_container {
		display: grid;
		grid-template-rows: repeat( 3, 1fr );
		justify-items: center;
		align-items: center;
		height: 100%;
	}
		#radar .patrol_and_logo_container .logo {
			width: 75%;
			font-size: 18px;
			text-align: center;
			line-height: 100%;
			margin-top: 15px;
			font-style: italic;
			color: rgb( 255, 255, 255 );
		}
			#radar .patrol_and_logo_container .logo .name {
				font-size: 24px;
			}

		#radar .patrol_and_logo_container .display {
			/* If you would like your patrol speed window to be solid rather than have a gradient, swap the comment for the 2 lines below */
			background-color: rgb( 0, 57, 35 );
			/* background: linear-gradient( to bottom, rgb( 0, 40, 29 ), rgb( 0, 46, 32 ) 40%, rgb( 1, 64, 27 ) 85% ); */
		}

		#radar .patrol_and_logo_container .speed_label {
			color: rgb( 255, 255, 255 );
			margin-bottom: 40px;
		}
	
	/* Colours and fonts used in multiple areas that are similar */
	#radar .display p {
		font-family: 'Seg-7';
	}

	#radar .display .ghost_speed {
		color: rgb( 90, 35, 1 );
	}

	#radar .display .speed {
		color: rgb( 252, 113, 1 );
		text-shadow: 0 0 8px rgba( 252, 114, 1, 0.65 );
	}

	#radar .display .fast_ghost_speed {
		color: rgb( 70, 0, 0 );
	}

	#radar .display .fast_speed {
		color: rgb( 243, 12, 10 );
		text-shadow: 0 0 8px rgba( 243, 14, 10, 0.65 );
	}

	#radar .display .patrol_ghost_speed {
		color: rgb( 0, 91, 68 );
	}

	#radar .display .patrol_speed {
		color: rgb( 15, 244, 57 );
		text-shadow: 0 0 8px rgba( 15, 244, 57, 0.65 );
	}

#rc {
	width: 315px;
	height: 800px;
   
	position: absolute;
	top: calc( 50% - ( 800px / 2 ) );
	left: calc( 50% - ( 315px / 2 ) );
	margin: auto;
	padding: 65px 30px 50px 30px;

	display: grid;
	grid-template-rows: 50px 30px 200px 30px 60px 130px 50px 80px;
	justify-items: center;
	align-items: center;

	color: white;
	background-image: url( "images/rc_bg.png" );
	
	transition: transform 0.5s;
	
	/* Settings for scaling */
	transform: scale( 1.0 );
	transform-origin: 0 0;

	z-index: 3;
}
	/* Button template classes */
	#rc button {
		border: none;
		margin: 0;
		padding: 0;

		background-color: rgb( 200, 200, 200 ); 

		box-shadow: 2px 3px rgb( 100, 100, 100 );
	}

	#rc button:hover {
		background-color: rgb( 230, 230, 230 );
	}

	#rc button:active {
		box-shadow: none;
		transform: translateX( 2px ) translateY( 3px );
	}

	#rc .rounded_btn {
		border-radius: 30px;
	}

	#rc .circle_btn {
		border-radius: 100%;
	}

	#rc .zone_btn {
		width: 65px;
		height: 65px;
	}

	#rc .xmit_btn {
		width: 65px; 
		height: 80px;
		position: relative;
		background-color: rgb( 200, 200, 200 ); 
	}

	#rc .xmit_btn:hover {
		background-color: rgb( 230, 230, 230 );
	}
		#rc .xmit_top {
			clip-path: polygon( 0 15px, 50% 0, 100% 15px, 100% 100%, 0 100% );
			border-radius: 0 0 7px 7px;
		}

		#rc .xmit_bottom {
			clip-path: polygon( 0 65px, 0 0, 100% 0, 100% 65px, 50% 100% );
			border-radius: 7px 7px 0 0;
		}

	#rc .xmit_wrap {
		filter: drop-shadow( 2px 3px rgb( 100, 100, 100 ) );
	}

	#rc .xmit_wrap:active {
		filter: drop-shadow( 0 0 rgba( 100, 100, 100, 0.0 ) );
		transform: translateX( 2px ) translateY( 3px );
	}
		#rc .xmit_wrap button:active {
			transform: none;
		}

	#rc .xmit_btn .arrow {
		width: 40px;
		height: 20px;
		position: absolute;
		left: 0;
		right: 0;
		clip-path: polygon( 50% 0, 100% 60%, 70% 60%, 70% 100%, 30% 100%, 30% 60%, 0 60% );
		background-color: rgb( 0, 0, 0 );
	}
		#rc .xmit_btn .arrow_bottom {
			transform: rotate( 180deg );
			bottom: 0;
		}

	/* Buttons and remote control elements */
	#rc .toggle_display {
		width: 130px;
		height: 40px;
	}

	#rc .label {
		font-size: 20px;
		letter-spacing: 1px;
	}

	#rc .antenna_btns_container {
		width: 90%;
		height: 100%;
		display: grid;
		grid-template-rows: 45% 10% 45%;
		align-items: center;
	}
		#rc .antenna_btns_container .btns {
			display: flex; 
			justify-content: space-evenly;
		}
			#rc .antenna_btns_container .btns_top {
				align-items: flex-end;
			}

			#rc .antenna_btns_container .btns_bottom {
				align-items: flex-start;
			}

			/* Style for the button inside the div, this is so the button still works */
			#rc .antenna_btns_container .btns .xmit_btn button {
				width: 100%; 
				height: 100%; 
				position: absolute; 
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				margin: auto;
				background-color: transparent;
			}

			#rc .antenna_btns_container .btns .top_left {
				border-radius: 35px 7px 7px 7px;
			}

			#rc .antenna_btns_container .btns .xmit_btn .top_middle {
				padding-top: 25px;
			}

			#rc .antenna_btns_container .btns .top_right {
				border-radius: 7px 35px 7px 7px;
			}

			#rc .antenna_btns_container .btns .bottom_left {
				border-radius: 7px 7px 7px 35px;
			}

			#rc .antenna_btns_container .btns .xmit_btn .bottom_middle {
				padding-bottom: 25px;
			}

			#rc .antenna_btns_container .btns .bottom_right {
				border-radius: 7px 7px 35px 7px;
			}

		#rc .antenna_btns_container .breaker {
			width: 95%;
			height: 5px;
			margin: auto;
			background-color: white; 
		}
	
	#rc .menu {
		width: 60px;
		height: 60px;
		font-size: 17px;
		line-height: 62px;
	}

	#rc .plate_reader_and_help_container {
		width: 90%;
		display: grid;
		grid-template-columns: 1fr 1fr;
		justify-items: center;
	}
		#rc .plate_reader_and_help_container .plate_reader, 
		#rc .plate_reader_and_help_container .help {
			width: 80px;
			height: 55px;
			border-radius: 7px;
		}

		#rc .plate_reader_and_help_container .help {
			padding: 0 10px;
			font-size: 15px;
		}
	
	#rc .light {
		width: 120px; 
		height: 30px;
		font-size: 15px;
	}

	#rc .logo {
		font-size: 18px;
		font-style: italic;
	}
		#rc .logo .large {
			font-size: 25px;
		}

	#rc .blue {
		background-color: rgb( 84, 210, 255 );
	}
	
#plateReaderFrame {
	width: 500px;
	height: 200px;

	position: absolute;
	margin: auto;

	top: calc( 50% - ( 200px / 2 ) );
	left: calc( 100% - 500px );

	background-image: url( "images/pr_frame.png" );

	transition: transform 0.5s;

	/* Settings for scaling */
	transform: scale( 1.0 );
	transform-origin: 0 0;

	z-index: 1;
}
	#plateReaderFrame .frame_border {
		width: 465px;
		height: 175px;

		position: absolute;
		margin: auto;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;

		background-color: rgb( 20, 22, 18 );

		border-radius: 5px;
	}

#plateReader {
	width: 460px;
	height: 170px;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	background-image: url( "images/pr_bg.png" );

	box-shadow: inset 0px 20px 20px -15px rgba( 0, 0, 0, 0.4 ); 

	display: grid;
	grid-template-rows: 30px 1fr 30px;
	align-content: center;
}
	#plateReader .labels {
		display: grid;
		grid-template-columns: 1fr 1fr;
		align-content: center;
	}
		#plateReader .labels .title {
			color: rgb( 255, 255, 255 );
		}
		
		#plateReader .labels p {
			margin: 0;
			text-align: center;
			font-size: 18px;
		}

	#plateReader .plates {
		width: 100%;
		height: 100%;

		display: grid;
		grid-template-columns: 1fr 1fr;
		align-content: center;
		justify-items: center;
	}
		#plateReader .plates .plate_container {
			width: 90%;
			height: 100%;

			display: grid;
			grid-template-columns: 1fr;
			justify-content: center;
		}
			#plateReader .plates .plate_container .plate {
				display: block;
				max-width: 100%; 
				height: auto;
				grid-column: 1;
				grid-row: 1;
			}

			#plateReader .plates .plate_container .text_container {
				grid-column: 1;
				grid-row: 1;

				display: grid;
				grid-template-columns: 1fr;
				justify-content: center;
			}
				#plateReader .plates .plate_container .text_container p {
					display: block;
					font-family: "Plate-Font";
					font-size: 58px;
					text-align: center;
					letter-spacing: -3px;
					padding-top: 5px;
					margin: 0;
					grid-column: 1;
					grid-row: 1;
				}

				#plateReader .plates .plate_container .text_container .hilite {
					font-family: "Plate-Font-Hilite";
					color: rgb( 93, 65, 255 );
				}

				#plateReader .plates .plate_container .text_container .lolite {
					font-family: "Plate-Font-Lolite";
					color: rgb( 255, 255, 255 );
				}

				#plateReader .plates .plate_container .text_container .shadow {
					font-family: "Plate-Font-Shadow";
					color: rgb( 100, 100, 100 );
				}

.plate_hit {
	animation: plate_flash linear 0.6s infinite;
}

@keyframes plate_flash {
	0% { opacity: 1.0; }
	50% { opacity: 0.0; }
	100% { opacity: 1.0; }
}
 
#plateReaderBox {
	width: 225px;
	height: 330px;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	background: linear-gradient( to bottom, rgb( 70, 70, 70 ), rgb( 45, 45, 45 ) );
	border: 3px solid rgb( 0, 0, 0 );

	z-index: 4;
}
	#plateReaderBox .title {
		text-align: center;
		font-size: 20px;
		padding: 5px 0;
		margin: 0 auto;
		color: rgb( 255, 255, 255 );
		background-color: rgb( 20, 22, 18 );
	}

	#plateReaderBox .header {
		width: 100%;
	}

	#plateReaderBox .container {
		width: 100%;
		height: 200px;

		display: grid;
		grid-template-rows: 60px 70px 60px;
		align-items: center;
		justify-items: center;
	}
		#plateReaderBox .container .btn {
			width: 140px;
			height: 35px;
			border-radius: 10px;
			font-size: 16px;
			border: none;
		}
			#plateReaderBox .container .btn:hover {
				background-color: rgb( 255, 255, 255 );
			}

			#plateReaderBox .container .btn:active {
				background-color: rgb( 190, 190, 190 );
				padding: 0;
			}
		
		#plateReaderBox .container .plate_input {
			width: 75%;
			height: 50px;
			font-family: "Plate-Font";
			text-align: center;
			font-size: 38px;
			text-transform: uppercase;
			padding: 0;
			padding-bottom: 15px;
			margin-bottom: -15px;
		}

	#plateReaderBox .close {
		width: 80px;
		height: 20px;

		position: absolute;
		left: 0;
		right: 0;
		bottom: 10px;
		margin: auto;

		border-radius: 10px;
		border: none;
		background-color: rgb( 225, 225, 225 );
	}
		#plateReaderBox .close:hover {
			background-color: rgb( 255, 255, 255 );
		}

		#plateReaderBox .close:active {
			background-color: rgb( 190, 190, 190 );
			padding: 0;
		}

#uiSettingsBox {
	width: 250px;
	height: 400px;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	
	background: linear-gradient( to bottom, rgb( 70, 70, 70 ), rgb( 45, 45, 45 ) );
	border: 3px solid rgb( 0, 0, 0 );

	z-index: 3;
}
	#uiSettingsBox .title {
		text-align: center;
		font-size: 20px;
		padding: 5px 0;
		margin: 0 auto;
		color: rgb( 255, 255, 255 );
		background-color: rgb( 20, 22, 18 );
	}

	#uiSettingsBox .header {
		width: 100%;
	}
	
	#uiSettingsBox .scaling_container {
		height: 225px;
		display: grid;
		grid-template-rows: 1fr 1fr 1fr;
	}
	
	#uiSettingsBox .scaling {
		height: 70px;
		
		display: grid;
		grid-template-columns: 1fr 2fr 1fr;
		place-items: center center;
	}
		#uiSettingsBox .scaling p {
			font-size: 18px;
			margin: 0 auto;
			text-align: center;
			color: rgb( 255, 255, 255 );
		}
			#uiSettingsBox .scaling .multiplier {
				font-size: 16px;
			}

		#uiSettingsBox .scaling .symbol {
			width: 40px; 
			height: 40px;

			background-color: rgb( 225, 225, 225 );
		}
			#uiSettingsBox .scaling .symbol:hover {
				background-color: rgb( 255, 255, 255 );
			}

			#uiSettingsBox .scaling .symbol:active {
				background-color: rgb( 190, 190, 190 );
			}

		#uiSettingsBox .scaling .minus {
			clip-path: polygon( 0 35%, 100% 35%, 100% 65%, 0 65% );
		}

		#uiSettingsBox .scaling .plus {
			clip-path: polygon( 0 35%, 35% 35%, 35% 0, 65% 0, 65% 35%, 100% 35%, 100% 65%, 65% 65%, 65% 100%, 35% 100%, 35% 65%, 0 65% );
		}
		
	#uiSettingsBox .safezone_container {
		width: 85%;
		margin: 0 auto;
	}
		#uiSettingsBox .safezone_container p, 
		#uiSettingsBox .safezone_container span {
			font-size: 18px;
			margin: 0 auto;
			text-align: center;
			color: rgb( 255, 255, 255 );
		}

		#uiSettingsBox .safezone_container .slider {
			width: 100%;
			height: 10px;
			margin: 10px 0;

			border-radius: 5px;

			-webkit-appearance: none;

			background-color: rgb( 180, 180, 180 );
		}

		#uiSettingsBox .safezone_container .slider:focus {
			outline: none;
		}
			#uiSettingsBox .safezone_container .slider::-webkit-slider-thumb {
				-webkit-appearance: none;
				appearance: none;
				width: 15px;
				height: 25px;
				background: rgb( 84, 210, 255 );
				cursor: pointer;
			}
			  
			#uiSettingsBox .safezone_container .slider::-moz-range-thumb {
				width: 15px;
				height: 25px;
				background: rgb( 84, 210, 255 );
				cursor: pointer;
			}

	#uiSettingsBox .close {
		width: 80px;
		height: 20px;

		position: absolute;
		left: 0;
		right: 0;
		bottom: 30px;
		margin: auto;

		border-radius: 10px;
		border: none;
		background-color: rgb( 225, 225, 225 );
	}
		#uiSettingsBox .close:hover {
			background-color: rgb( 255, 255, 255 );
		}

		#uiSettingsBox .close:active {
			background-color: rgb( 190, 190, 190 );
			padding: 0;
		}

#keyLockLabel {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 300px;

	text-align: center;
	font-size: 30px;

	color: rgb(255, 255, 255);
	text-shadow: 3px 2px 5px rgb( 0, 0, 0 );

	z-index: 5;
}
	#keyLockLabel span {
		font-size: 30px;
	}

#helpWindow {
	width: 75%;
	height: 90%;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	display: grid;
	grid-template-rows: 90% 10%;
	justify-items: center;

	z-index: 6;
}
	#helpWindow iframe {
		width: 100%;
		height: 100%;

		display: block;
	}

	#helpWindow .close {
		width: 150px;
		height: 50px;
		
		margin: auto;

		font-size: 18px;

		border-radius: 10px;
		border: none;
		background-color: rgb( 225, 225, 225 );
	}
		#helpWindow .close:hover {
			background-color: rgb( 255, 255, 255 );
		}

		#helpWindow .close:active {
			background-color: rgb( 190, 190, 190 );
			padding: 0;
		}

@media ( max-width: 1024px ) {
	#helpWindow {
		width: 80%;
	}
}

#quickStart {
	width: 50%;
	height: 62.5%;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	display: grid;
	grid-template-rows: 80% 20%;

	z-index: 8;
}
	#quickStart iframe {
		width: 100%;
		height: 100%;
	}

	#quickStart .close {
		width: 150px;
		height: 50px;
		
		margin: auto;

		font-size: 18px;

		border-radius: 10px;
		border: none;
		background-color: rgb( 225, 225, 225 );
	}
		#quickStart .close:hover {
			background-color: rgb( 255, 255, 255 );
		}

		#quickStart .close:active {
			background-color: rgb( 190, 190, 190 );
			padding: 0;
		}

@media ( max-width: 1280px ) {
	#quickStart {
		width: 80%;
	}
}

#newUser {
	width: 400px;
	height: 150px;

	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;

	background: linear-gradient( to bottom, rgb( 70, 70, 70 ), rgb( 45, 45, 45 ) );
	border: 3px solid rgb( 0, 0, 0 );
	color: rgb( 255, 255, 255 );

	display: grid;
	grid-template-columns: 20% 30% 30% 20%;
	grid-template-rows: 65% 35%;
	justify-items: center;
	align-items: center;

	z-index: 7;
}
	#newUser .msg {
		grid-column: 1 / 5;
		font-size: 18px;
		text-align: center;
		margin: 10px;
	}

	#newUser button {
		width: 100px;
		height: 30px;
		font-size: 18px;
		border-radius: 10px;
		border: none;
		background-color: rgb( 225, 225, 225 );
	}
		#newUser button:hover {
			background-color: rgb( 255, 255, 255 );
		}

		#newUser button:active {
			background-color: rgb( 190, 190, 190 );
			padding: 0;
		}

	#newUser .btn_yes {
		grid-column: 2;
	}

	#newUser .btn_no {
		grid-column: 3;
	}