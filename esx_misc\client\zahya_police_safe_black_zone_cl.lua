Rip = {
	MaxSpeed = 25,
	DisabledKeys = { -- here you can choose what Keys you want to Disable -- هنا يمكنك اختيار المفاتيح التي تريد اطفائها
        {Index = 289, types = 0}, --(F2)
        {Index = 170, types = 0}, --(F3)
		{Index = 166, types = 0}, --(F5)
		{Index = 167, types = 0}, --(F6)
		{Index = 168, types = 0}, --(F7)
		{Index = 56, types = 0}, --(F9)
		{Index = 57, types = 0}, --(F10)
		{Index = 44, types = 0}, --(Q)
		{Index = 45, types = 0}, --(R)
		{Index = 245, types = 0}, --(T)
		{Index = 47, types = 0}, --(G)
		{Index = 74, types = 0}, --(H)
		{Index = 20, types = 0}, --(Z)
		{Index = 29, types = 0}, --(B)
		{Index = 244, types = 0}, --(M)
		{Index = 37, types = 0}, --(TAB)
		{Index = 137, types = 0}, --(CAPS)
		{Index = 21, types = 0}, --(LEFTSHIFT)
		{Index = 36, types = 0}, --(LEFTCTRL)
		{Index = 24, types = 0}, --(LEFTMOUSEBUTTON)
		{Index = 22, types = 0} --(SPACE)
    }
}

local myZonesInfoBlack = {}
local myLocationZonesInfoBlack = {}
local myLastZoneBlack = nil
local hasScreenEffectBlack = false
local MaxSpeedEnabledBlack = false
local lastremove = 0

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	PlayerData = ESX.GetPlayerData()
	ESX.TriggerServerCallback('zahya_police_safe_black_zone:GetZonesInfo', function(ZonesInfo, ZonesInfo2)
		myZonesInfoBlack = ZonesInfo
		myLocationZonesInfoBlack = ZonesInfo2
	end)
end)

RegisterNetEvent('zahya_police_safe_black_zone:CL:RemoveZones')
AddEventHandler('zahya_police_safe_black_zone:CL:RemoveZones', function(ZoneCoords)
	if ifZoneBlack(ZoneCoords) then
		TriggerServerEvent("zahya_police_safe_black_zone:SV:RemoveZone", ZoneCoords)
	elseif ifInAnyZoneBlack() then
		TriggerServerEvent("zahya_police_safe_black_zone:SV:RemoveZone", ifInAnyZoneBlack())
	end
end)

RegisterNetEvent('zahya_police_safe_black_zone:UpdateZonesInfo')
AddEventHandler('zahya_police_safe_black_zone:UpdateZonesInfo', function(ZonesInfo, ZoneCoords, ZoneDistance)
	myZonesInfoBlack = ZonesInfo
	local ped = PlayerPedId()
	local targetCoords = GetEntityCoords(ped)
	if ZoneDistance ~= nil then
		local targetdistance = GetDistanceBetweenCoords(targetCoords, ZoneCoords, true)
		if targetdistance <= ZoneDistance then
			myLastZoneBlack = ZoneCoords
		end
	end
end)

RegisterNetEvent('zahya_police_safe_black_zone:RemoveOldZone')
AddEventHandler('zahya_police_safe_black_zone:RemoveOldZone', function(ZoneCoords)
	print('remove black zonee '..ZoneCoords)
	for k,v in pairs(myLocationZonesInfoBlack) do
		if v["TableZoneCoords"] == ZoneCoords then
			table.remove(myLocationZonesInfoBlack,k)
		end
	end
end)

RegisterNetEvent('zahya_police_safe_black_zone:addNewZone')
AddEventHandler('zahya_police_safe_black_zone:addNewZone', function(ZoneCoords, ZoneDistance)
	table.insert(myLocationZonesInfoBlack, { ["TableZoneCoords"] = ZoneCoords, ["TableZoneDistance"] = ZoneDistance })
	local ped = PlayerPedId()
	local targetCoords = GetEntityCoords(ped)
	local targetdistance = GetDistanceBetweenCoords(targetCoords, ZoneCoords, true)
	if targetdistance <= ZoneDistance then
		myLastZoneBlack = ZoneCoords
	end
end)

RegisterNetEvent('zahya_police_safe_black_zone:UpdateStatsInfo')
AddEventHandler('zahya_police_safe_black_zone:UpdateStatsInfo', function()
	hasScreenEffectBlack = false
	MaxSpeedEnabledBlack = false
	ClearTimecycleModifier()
	ClearExtraTimecycleModifier()
	local ped = PlayerPedId()
	EnableAllControlActions(0)
	if IsPedInAnyVehicle(ped,true) then
		local Vehicle = GetVehiclePedIsIn(ped,false)
        SetEntityMaxSpeed(Vehicle,GetVehicleHandlingFloat(Vehicle,"CHandlingData","fInitialDriveMaxFlatVel"))
	end
end)

RegisterNetEvent('zahya_police_safe_black_zone:TogglePanicButton')
AddEventHandler('zahya_police_safe_black_zone:TogglePanicButton', function(ZoneCoords, ZoneName, ZoneDistance)
	if ifInAnyMyZoneBlack() then
		TriggerServerEvent("zahya_police_safe_black_zone:SV:RemoveLocationZone", ifInAnyMyZoneBlack(), ZoneName)
		TriggerServerEvent("esx_misc:TogglePanicButton", ZoneName, ifInAnyMyZoneBlack(), true)
	else
		TriggerServerEvent("zahya_police_safe_black_zone:SV:AddZone", ZoneCoords, ZoneName, ZoneDistance)
		TriggerServerEvent("esx_misc:TogglePanicButton", ZoneName, ZoneCoords)
	end
end)

function ZoneExist(ZoneCoords)
	if myLocationZonesInfoBlack ~= nil then
		for k,v in pairs(myLocationZonesInfoBlack) do
			print(ZoneCoords, v["TableZoneCoords"])
			if ZoneCoords == v["TableZoneCoords"] then
				return v["TableZoneCoords"]
			end
		end
		return false
	else
		return false
	end
end


RegisterNetEvent('zahya_police_safe_black_zone:TogglePort')
AddEventHandler('zahya_police_safe_black_zone:TogglePort', function(ZoneCoords, ZoneName, ZoneDistance)
	if ZoneExist(ZoneCoords) then
		TriggerServerEvent("zahya_police_safe_black_zone:SV:RemoveLocationZone", ZoneExist(ZoneCoords), ZoneName)
		-- TriggerServerEvent("esx_misc:TogglePanicButton", ZoneName, ZoneExist(ZoneCoords), true)
	else
		TriggerServerEvent("zahya_police_safe_black_zone:SV:AddZone", ZoneCoords, ZoneName, ZoneDistance)
		-- TriggerServerEvent("esx_misc:TogglePanicButton", ZoneName, ZoneCoords)
	end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
end)

Citizen.CreateThread(function()
	while true do
		if PlayerData.job.name ~= 'police' and PlayerData.job.name ~= 'ambulance' and PlayerData.job.name ~= 'admin' ~= PlayerData.job.name ~= 'agent' and PlayerData.job.name ~= 'mechanic' then
			if ifInAnyZoneBlack() then
				Citizen.Wait(120000)
				if ifInAnyZoneBlack() then
					if lastremove == 0 then
						lastremove = 40
					else
						lastremove = lastremove * 2
					end
					ESX.ShowNotification('<font color=red> تم خصم </font> <font color=yellow>'..lastremove..'</font> <font color=red> من خبرتك </br>لتواجدك داخل منطقة محظورة </font>')
					TriggerServerEvent('zahya_xplevel:updateCurrentPlayerXP_clientSide', 'remove', lastremove, 'داخل منطقة محظورة')
				end
			else
				lastremove = 0
			end
		end
		Citizen.Wait(5000)
	end
end)

Citizen.CreateThread(function()
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(200)
	end
	while true do
		local sleep = 1
		local ped = PlayerPedId()
		local Vehicle = GetVehiclePedIsIn(ped,false)
		-- print('in veh '..Vehicle)
		local ZCoords = IAZNBlack()
        if not ZCoords then
			sleep = 3000
		end
		if PlayerData.job.name == 'police' or PlayerData.job.name == 'ambulance' or PlayerData.job.name == 'admin' or PlayerData.job.name == 'agent' or PlayerData.job.name == 'mechanic' then
			if hasScreenEffectBlack then
				ClearTimecycleModifier()
				ClearExtraTimecycleModifier()
				hasScreenEffectBlack = false
			end
			if MaxSpeedEnabledBlack then
				if Vehicle == 0 then
					MaxSpeedEnabledBlack = false
				else
					SetEntityMaxSpeed(Vehicle,GetVehicleHandlingFloat(Vehicle,"CHandlingData","fInitialDriveMaxFlatVel"))
					MaxSpeedEnabledBlack = false
				end
			end
		else
			if ZCoords == false then
				myLastZoneBlack = nil
				if hasScreenEffectBlack then
					ClearTimecycleModifier()
					ClearExtraTimecycleModifier()
					hasScreenEffectBlack = false
				end
				if MaxSpeedEnabledBlack then
					if Vehicle == 0 then
						MaxSpeedEnabledBlack = false
					else
						SetEntityMaxSpeed(Vehicle,GetVehicleHandlingFloat(Vehicle,"CHandlingData","fInitialDriveMaxFlatVel"))
						MaxSpeedEnabledBlack = false
					end
				end
			else
				if myLastZoneBlack == ZCoords then
				else
					SetCurrentPedWeapon(PlayerPedId(),GetHashKey("WEAPON_UNARMED"),true)
					SetTimecycleModifier('DefaultColorCode')
					SetExtraTimecycleModifier('spectator4')


					for i, r in pairs(Rip.DisabledKeys) do
						DisableControlAction(r['types'], r['Index'], true)
					end
					hasScreenEffectBlack = true
					if Vehicle == 0 then
						MaxSpeedEnabledBlack = false
					else
						SetEntityMaxSpeed(Vehicle,(Rip.MaxSpeed/2.236936))
						MaxSpeedEnabledBlack = true
					end
				end
			end
		end
        Citizen.Wait(sleep)
	end
end)

function ifInAnyZoneBlack()
	if myZonesInfoBlack ~= nil then
		for k,v in pairs(myZonesInfoBlack) do
			local ped = PlayerPedId()
			local targetCoords = GetEntityCoords(ped)
			local targetdistance = GetDistanceBetweenCoords(targetCoords, v["TableZoneCoords"], true)
			if targetdistance <= v["TableZoneDistance"] then
				return v["TableZoneCoords"]
			end
		end
		return false
	else
		return false
	end
end

function ifInAnyMyZoneBlack()
	if myLocationZonesInfoBlack ~= nil then
		local ped = PlayerPedId()
		local targetCoords = GetEntityCoords(ped)
		for k,v in pairs(myLocationZonesInfoBlack) do
			local targetdistance = GetDistanceBetweenCoords(targetCoords, v["TableZoneCoords"], true)
			if targetdistance <= v["TableZoneDistance"] then
				return v["TableZoneCoords"]
			end
		end
		return false
	else
		return false
	end
end

function IAZNBlack()
	if ifInAnyMyZoneBlack() then
		return ifInAnyMyZoneBlack()
	elseif ifInAnyZoneBlack() then
		return ifInAnyZoneBlack()
	else
		return false
	end
end

function ifZoneBlack(ZoneCoords)
	if myZonesInfoBlack ~= nil then
		for k,v in pairs(myZonesInfoBlack) do
			if ZoneCoords == v["TableZoneCoords"] then
				return v["TableZoneCoords"]
			end
		end
		return false
	else
		return false
	end
end
