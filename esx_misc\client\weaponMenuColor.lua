local GUI                     = {}
GUI.Time                      = 0

function OpenWeaponsSkinsMenu()
    local elements = {}
    ESX.UI.Menu.CloseAll()

    --        table.insert(elements, {label = "------------------------"})
    --        table.insert(elements, {label = "|         كفرات         |"})
    --        table.insert(elements, {label = "------------------------"})
        table.insert(elements, {label = "الأساسي", istint = true, value = 0})
        table.insert(elements, {label = "اخضر", istint = true, value = 1})
        table.insert(elements, {label = "ذهبي", istint = true, value = 2})
        table.insert(elements, {label = "وردي", istint = true, value = 3})
        table.insert(elements, {label = "جيشي", istint = true, value = 4})
        table.insert(elements, {label = "شرطة", istint = true, value = 5})
        table.insert(elements, {label = "برتقالي", istint = true, value = 6})
        table.insert(elements, {label = "بلاتيني", istint = true, value = 7})
    ESX.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'esx_extraitems_skins',
        {
            title    = 'قائمة كفرات سلاح مجانا',
            align    = 'top-left',
            elements = elements,

        },
function(data, menu)
                SetPedWeaponTintIndex(PlayerPedId(), GetSelectedPedWeapon(PlayerPedId()), data.current.value)
        end,
        function(data, menu)
            menu.close()
        end
    )
end


