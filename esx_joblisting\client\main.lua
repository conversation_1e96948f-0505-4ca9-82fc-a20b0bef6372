local menuIsShowed, hasAlreadyEnteredMarker, isInMarker = false, false, false

local Cooldown_count = 0
	local function Cooldown(sec)
		CreateThread(function()
		Cooldown_count = sec 
		while Cooldown_count ~= 0 do
		Cooldown_count = Cooldown_count - 1
		Wait(1000)
		end	
		Cooldown_count = 0
	end)	
end

function ShowJobListingMenu()
	ESX.TriggerServerCallback('esx_joblisting:getJobsList', function(jobs)
		local elements = {}

		for i=1, #jobs, 1 do
			local job = jobs[i]
			local labelText = job.label
			local isEligible = true

			-- إذا كانت الوظيفة تحتاج خبرة، نضيف التنسيق والألوان
			if job.has_experience_requirement then
				isEligible = job.player_level >= job.required_level
				local color = isEligible and Config.ExperienceSystem.Colors.Eligible or Config.ExperienceSystem.Colors.NotEligible

				-- تنسيق النص ليظهر الوظيفة مع متطلبات الخبرة
				labelText = string.format("<span style='color:%s'>%s - (خبرة %d)</span>",
					color, job.label, job.required_level)
			end

			table.insert(elements, {
				label = labelText,
				job   = job.job,
				grade = job.grade,
				required_level = job.required_level,
				player_level = job.player_level,
				is_eligible = isEligible,
				has_experience_requirement = job.has_experience_requirement
			})
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'joblisting', {
			title    = string.format('مركز التوظيف - مستواك: %d', jobs[1] and jobs[1].player_level or 0),
			align    = 'top-left',
			elements = elements
		}, function(data, menu)
			-- التحقق من أهلية اللاعب للوظيفة (فقط للوظائف التي تحتاج خبرة)
			if data.current.has_experience_requirement and not data.current.is_eligible then
				local message = string.format('تحتاج إلى مستوى خبرة %d للتقديم على هذه الوظيفة. مستواك الحالي: %d',
					data.current.required_level, data.current.player_level)
				ESX.ShowNotification(message)
				return
			end

		    if data.current.job == 'admin' then
			Cooldown(10)
			TriggerServerEvent('esx_joblisting:setJob_admin_73alhdba762_dkab62dvbeme', data.current.job, data.current.grade)
			ESX.ShowNotification(_U('new_job_admin'))
			menu.close()
			elseif data.current.job == 'police' then
			Cooldown(10)
			TriggerServerEvent('esx_joblisting:setJob_police_da3oid63', data.current.job, data.current.grade)
			ESX.ShowNotification(_U('new_job_police'))
			menu.close()
			elseif data.current.job == 'agent' then -- ambulance
			Cooldown(10)
			TriggerServerEvent('esx_joblisting:setJob_agent_kaug362', data.current.job, data.current.grade)
			ESX.ShowNotification(_U('new_job_agent'))
			menu.close()
			elseif data.current.job == 'ambulance' then -- ambulance
			Cooldown(10)
			TriggerServerEvent('esx_joblisting:setJob_ambulance_d8labd3', data.current.job, data.current.grade)
			ESX.ShowNotification(_U('new_job_ambulance'))
			menu.close()
			elseif data.current.job == 'mechanic' then
			Cooldown(10)
			TriggerServerEvent('esx_joblisting:setJob_mechanic_a73kvgad3', data.current.job, data.current.grade)
			ESX.ShowNotification(_U('new_job_mechanic'))
			menu.close()
			else
			Cooldown(5)
			TriggerServerEvent('esx_joblisting:setJob_kaugy36', data.current.job, data.current.grade)
			ESX.ShowNotification(_U('new_job'))
			menu.close()
			end
		end, function(data, menu)
			menu.close()
		end)

	end)
end

AddEventHandler('esx_joblisting:hasExitedMarker', function(zone)
	ESX.UI.Menu.CloseAll()
end)

-- Activate menu when player is inside marker, and draw markers
Citizen.CreateThread(function()
	while true do
		local Sleep = 1500

		local coords = GetEntityCoords(PlayerPedId())
		isInMarker = false

		for i=1, #Config.Zones, 1 do
			local distance = GetDistanceBetweenCoords(coords, Config.Zones[i], true)

			if distance < Config.DrawDistance then
				Sleep = 0
				DrawMarker(Config.MarkerType, Config.Zones[i], 0.0, 0.0, 0.0, 0, 0.0, 0.0, Config.ZoneSize.x, Config.ZoneSize.y, Config.ZoneSize.z, Config.MarkerColor.r, Config.MarkerColor.g, Config.MarkerColor.b, 100, false, true, 2, false, false, false, false)
			end

			if distance < (Config.ZoneSize.x / 2) then
				isInMarker = true
				ESX.ShowHelpNotification(_U('access_job_center'))
			end
		end

		if isInMarker and not hasAlreadyEnteredMarker then
			hasAlreadyEnteredMarker = true
		end

		if not isInMarker and hasAlreadyEnteredMarker then
			hasAlreadyEnteredMarker = false
			TriggerEvent('esx_joblisting:hasExitedMarker')
		end
		Citizen.Wait(Sleep)
	end
end)

-- Create blips
Citizen.CreateThread(function()
	for i=1, #Config.Zones, 1 do
		local blip = AddBlipForCoord(Config.Zones[i])

		SetBlipSprite (blip, 407)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 1.2)
		SetBlipColour (blip, 27)
		SetBlipAsShortRange(blip, true)

		BeginTextCommandSetBlipName("STRING")
		AddTextComponentSubstringPlayerName(_U('job_center'))
		EndTextCommandSetBlipName(blip)
	end
end)

-- Menu Controls
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if IsControlJustReleased(0, 38) and isInMarker and not menuIsShowed then
		    if Cooldown_count == 0 then
		    Cooldown(5)
			ESX.UI.Menu.CloseAll()
			ShowJobListingMenu()
			else
			ESX.ShowNotification('<font color=red>يجب الأنتظار</font>. <font color=orange>'..Cooldown_count..' ثانية</font>')
		end
	  end
	end
end)