Locales['sv'] = {
  -- <PERSON><PERSON>nt
  ['marker_hint'] = 'Klicka ~INPUT_CONTEXT~ för att titta på ~g~kamerorna~s~',

  -- Meny
  ['securitycams_menu'] = 'Säkerhetskameror',
  ['bank_menu_selection'] = 'Pacific Standard Bank',
  ['police_menu_selection'] = 'Polisstation',

  -- I <PERSON>
  ['pacific_standard_bank'] = 'Pacific Standard Bank',
  ['police_station'] = 'Polisstation',

  -- Pacific Standard Bank Kameror
  ['bcam'] = 'Entré',
  ['bcam2'] = 'Lobby',
  ['bcam3'] = 'Mindre Entré',
  ['bcam4'] = 'Trappa Till Andra Våningen',
  ['bcam5'] = 'Trappa Ovanför Bankvalvet',
  ['bcam6'] = 'Kontorens Korridor #1',
  ['bcam7'] = 'Kontorens Korridor #2',
  ['bcam8'] = 'Andra Våningen #1',
  ['bcam9'] = 'Andra Våningen #2',
  ['bcam10'] = 'Trappa Till Bankvalvet',
  ['bcam11'] = 'Utanför Bankvalvet',

  -- Polisstationens Kameror
  ['pcam'] = 'Parkering',
  ['pcam2'] = 'Cell #1',
  ['pcam3'] = 'Cell #2',
  ['pcam4'] = 'Cell #3',
  ['pcam5'] = 'Polisens Parkering & Garage',
  ['pcam6'] = 'Utanför Entré',
  ['pcam7'] = 'Lobby',

  -- Settings
  ['next'] = 'NÄSTA KAMERA',
  ['previous'] = 'FÖRRA KAMERAN',
  ['close'] = 'STÄNG AV KAMERAN',

  -- Hack
  ['marker_hint_hacking_policestation'] = 'Klicka ~INPUT_CONTEXT~ för att hacka polisens ~g~kameror~s~',
  ['marker_hint_hacking_bank'] = 'Klicka ~INPUT_CONTEXT~ för att hacka bankens ~g~kameror~s~',
  ['broken_cameras'] = 'Kamerorna är ur funktion! <br />Åk till huvudanläggningen för att se så att allt står rätt till.',
  ['hacking_succeed'] = 'Hackning slutförd! <br />Poliserna kan inte längre se i kamerorna.',
  ['hacking_failed'] = 'Hackning misslyckad! <br />Polisen har blivit tillkallad.',
  ['infected_cameras'] = 'Kamerorna är infekterade! <br />Polisen kan inte längre se i kamerorna.',
  ['nothing_wrong'] = 'Det är inget fel på kamerorna?',
  ['removing_viruses'] = 'Virus hittat! Tar bort virus från serverdatorn...',
  ['unhack_policestation'] = 'Klicka ~INPUT_CONTEXT~ för att ta bort ~g~virus~s~',
  ['unhack_bank'] = 'Klicka ~INPUT_CONTEXT~ för att ta bort ~g~virus~s~',
}
