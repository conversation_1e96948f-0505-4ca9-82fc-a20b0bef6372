local setWeapon 		 	= ""
local www = ""
local weaponCount			= 0
local disablePedWeaponDraw 	= false  --- Set this to true and non-police players will be set to unarmed when exiting a vehicle. If you use a custom unholstering animation enable this to prevent players bypassing it.

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	ESX.PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)


function HandleWeaponCounter()
	if  IsPedInAnyVehicle(PlayerPedId(), false) and ESX.PlayerData.job.name == "admin" or ESX.PlayerData.job.name == "police" or ESX.PlayerData.job.name == "agent" then
		ReadyWeaponCounter()
    end
end

RegisterCommand('weaponCounter', function()
	--StartWeaponReadyThread()
    HandleWeaponCounter()
end)

RegisterKeyMapping('weaponCounter', 'NEX', 'keyboard', '1')



function StartWeaponReadyThread()
	Citizen.CreateThread(function()
		while PlayerData == nil do
			Citizen.Wait(500)
		end
		while true do
			Citizen.Wait(1)
			local ped = PlayerPedId()
			if IsPedInAnyVehicle(ped, false) then
				if ESX.PlayerData.job ~= nil then
					if ESX.PlayerData.job.name == "admin" or ESX.PlayerData.job.name == "police" or ESX.PlayerData.job.name == "agent" then
						if IsControlJustReleased(0, 75) then
							SetCurrentPedWeapon(ped, setWeapon, true)
							TriggerEvent('esx_hamada:setPolice', setWeapon)
							ClearPedSecondaryTask()
							Citizen.Wait(1000)
						end
					else
						if disablePedWeaponDraw then
							if IsControlJustReleased(0, 75) then
								SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"))
								Citizen.Wait(1000)
							end
						end
					end
				end
			else
				break
			end
		end
	end)
end
AddEventHandler('esx_hamada:justleft',function ()
	if ESX.PlayerData.job.name == "admin" or ESX.PlayerData.job.name == "police" or ESX.PlayerData.job.name == "agent" then
		SetCurrentPedWeapon(PlayerPedId(), setWeapon, true)
		TriggerEvent('esx_hamada:setPolice', setWeapon)
		ClearPedSecondaryTask()
	end
end)

function ReadyWeaponCounter()
	weaponCount = weaponCount + 1
	if weaponCount == 1 then
		www = "WEAPON_STUNGUN"
		setWeapon = GetHashKey("WEAPON_STUNGUN")
		ReadyWeaponSendNotification('صاعق')
	elseif weaponCount == 2 then
		www = "WEAPON_PISTOL"
		setWeapon = GetHashKey("WEAPON_PISTOL")
		ReadyWeaponSendNotification('مسدس')
	elseif weaponCount == 3 then
		www = "WEAPON_PUMPSHOTGUN"
		setWeapon = GetHashKey("WEAPON_PUMPSHOTGUN")
		ReadyWeaponSendNotification('شوزن')
	elseif weaponCount == 4 then
		www = "WEAPON_CARBINERIFLE"
		setWeapon = GetHashKey("WEAPON_CARBINERIFLE")
		ReadyWeaponSendNotification('رشاش')
	elseif weaponCount == 5 then
		setWeapon = GetHashKey("WEAPON_UNARMED")
		ReadyWeaponSendNotification('غير مسلح')
	elseif weaponCount <= 6 then
		weaponCount = 0
	end
end

function ReadyWeaponSendNotification(msg)
	exports.pNotify:SetQueueMax("left", 1)
	exports.pNotify:SendNotification({
	text = "<font size=6 ><center><b>"..msg.."</center></b></font>",
	type = "info",
	timeout = 600,
	layout = "centerLeft",
	queue = "left",
	killer = true
	})
end
