Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}


--Other
Config        = {}
Config.Locale = "en"


Config.pas = "khud267bs29mn"

--car wash
Config.EnablePrice = true
Config.Price = 50

Config.WashLocations = {
	vector3(26.5906, -1392.0261, 27.3634),
	vector3(167.1034, -1719.4704, 27.2916),
	vector3(-74.5693, 6427.8715, 29.4400),
	vector3(-699.6325, -932.7043, 17.0139)
}

-- esx_sit start
Config.MaxDistance = 1.5
Config.Debug = false

Config.Interactables = {
	'prop_bench_01a',
	'prop_bench_01b',
	'prop_bench_01c',
	'prop_bench_02',
	'prop_bench_03',
	'prop_bench_04',
	'prop_bench_05',
	'prop_bench_06',
	'prop_bench_05',
	'prop_bench_08',
	'prop_bench_09',
	'prop_bench_10',
	'prop_bench_11',
	'prop_fib_3b_bench',
	'prop_ld_bench01',
	'prop_wait_bench_01',
	'hei_prop_heist_off_chair',
	'hei_prop_hei_skid_chair',
	'prop_chair_01a',
	'prop_chair_01b',
	'prop_chair_02',
	'prop_chair_03',
	'prop_chair_04a',
	'prop_chair_04b',
	'prop_chair_05',
	'prop_chair_06',
	'prop_chair_05',
	'prop_chair_08',
	'prop_chair_09',
	'prop_chair_10',
	'v_club_stagechair',
	'prop_chateau_chair_01',
	'prop_clown_chair',
	'prop_cs_office_chair',
	'prop_direct_chair_01',
	'prop_direct_chair_02',
	'prop_gc_chair02',
	'prop_off_chair_01',
	'prop_off_chair_03',
	'prop_off_chair_04',
	'prop_off_chair_04b',
	'prop_off_chair_04_s',
	'prop_off_chair_05',
	'prop_old_deck_chair',
	'prop_old_wood_chair',
	'prop_rock_chair_01',
	'prop_skid_chair_01',
	'prop_skid_chair_02',
	'prop_skid_chair_03',
	'prop_sol_chair',
	'prop_wheelchair_01',
	'prop_wheelchair_01_s',
	'p_armchair_01_s',
	'p_clb_officechair_s',
	'p_dinechair_01_s',
	'p_ilev_p_easychair_s',
	'p_soloffchair_s',
	'p_yacht_chair_01_s',
	'v_club_officechair',
	'v_corp_bk_chair3',
	'v_corp_cd_chair',
	'v_corp_offchair',
	'v_ilev_chair02_ped',
	'v_ilev_hd_chair',
	'v_ilev_p_easychair',
	'v_ret_gc_chair03',
	'prop_ld_farm_chair01',
	'prop_table_04_chr',
	'prop_table_05_chr',
	'prop_table_06_chr',
	'v_ilev_leath_chr',
	'prop_table_01_chr_a',
	'prop_table_01_chr_b',
	'prop_table_02_chr',
	'prop_table_03b_chr',
	'prop_table_03_chr',
	'prop_torture_ch_01',
	'v_ilev_fh_dineeamesa',
	'v_ilev_fh_kitchenstool',
	'v_ilev_tort_stool',
	'v_ilev_fh_kitchenstool',
	'v_ilev_fh_kitchenstool',
	'v_ilev_fh_kitchenstool',
	'v_ilev_fh_kitchenstool',
	'hei_prop_yah_seat_01',
	'hei_prop_yah_seat_02',
	'hei_prop_yah_seat_03',
	'prop_waiting_seat_01',
	'prop_yacht_seat_01',
	'prop_yacht_seat_02',
	'prop_yacht_seat_03',
	'prop_hobo_seat_01',
	'prop_rub_couch01',
	'miss_rub_couch_01',
	'prop_ld_farm_couch01',
	'prop_ld_farm_couch02',
	'prop_rub_couch02',
	'prop_rub_couch03',
	'prop_rub_couch04',
	'p_lev_sofa_s',
	'p_res_sofa_l_s',
	'p_v_med_p_sofa_s',
	'p_yacht_sofa_01_s',
	'v_ilev_m_sofa',
	'v_res_tre_sofa_s',
	'v_tre_sofa_mess_a_s',
	'v_tre_sofa_mess_b_s',
	'v_tre_sofa_mess_c_s',
	'prop_roller_car_01',
	'prop_roller_car_02',
	-- Custom
	'v_ret_gc_chair02',
	'v_serv_ct_chair02',
}
-- esx_sit end