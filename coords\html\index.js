$(function () {
    function display(bool) {
    if (bool) {
        $("body").show();
        $("#Get").show();
    } else {
        $("body").hide();
        $("#Get").hide();
    }
}

display(false)

window.addEventListener('message', function(event) {
        var item = event.data;

        if (item.type === "ui") {
            if (item.status == true) {
                display(true)
            } else {
                display(false)
            }
        }
        GetCoords(event.data);
        GetnormalCoords(event.data);
        Getnvector3Coords(event.data);
        Getnvector4Coords(event.data);
    })
})

function GetClose(){
    $.post('http://coords/GetcloseButton');
}

function GetCopy(){
    const copyText = document.getElementById("GetCoords").textContent;
    const textArea = document.createElement('textarea');
    textArea.textContent = copyText;
    document.body.append(textArea);
    textArea.select();
    document.execCommand("copy");
}

function GetnormalCopy(){
    const copyText = document.getElementById("GetnormalCoords").textContent;
    const textArea = document.createElement('textarea');
    textArea.textContent = copyText;
    document.body.append(textArea);
    textArea.select();
    document.execCommand("copy");
}

function Getvector3Copy(){
    const copyText = document.getElementById("Getvector3Coords").textContent;
    const textArea = document.createElement('textarea');
    textArea.textContent = copyText;
    document.body.append(textArea);
    textArea.select();
    document.execCommand("copy");
}

function Getvector4Copy(){
    const copyText = document.getElementById("Getvector4Coords").textContent;
    const textArea = document.createElement('textarea');
    textArea.textContent = copyText;
    document.body.append(textArea);
    textArea.select();
    document.execCommand("copy");
}

function GetCoords(data) {
	if (data.type === 'Get') {
        GetcoordsStart(data);
    }
}

function GetnormalCoords(data) {
	if (data.type === 'normal') {
        GetcoordsnormalStart(data);
    }
}

function Getnvector3Coords(data) {
	if (data.type === 'vector3') {
        Getcoordsvector3Start(data);
    }
}

function Getnvector4Coords(data) {
    if (data.type === 'vector4') {
        Getcoordsvector4Start(data);
    }
}

function GetcoordsStart(data){
    document.querySelector("#GetCoords").textContent = data.text;
}

function GetcoordsnormalStart(data){
    document.querySelector("#GetnormalCoords").textContent = data.text;
}

function Getcoordsvector3Start(data){
    document.querySelector("#Getvector3Coords").textContent = data.text;
}

function Getcoordsvector4Start(data){
    document.querySelector("#Getvector4Coords").textContent = data.text;
}