@font-face {
  font-family: justsignature;
  src: url('../fonts/justsignature/JustSignature.woff');
}
body, html {
  width: 100%;
  height: 100%;
}
p {
  padding: 0;
  margin: 0;
  text-transform: uppercase;
  color: #9e9e9e;
  font-size: 16px;
  font-weight: bolder;
}
#id-card {
  position: relative;
  display: none;
  margin: 230px 50px 0 0;
  float: right;
  width: 457px;
  height: 272px;
  padding: 54px 17px 10px 144px;
  background: url("../images/idcard.png");
}
img {
  position: absolute;
  width: 105px;
  height: 130px;
  object-fit: cover;
  margin-top: 75px;
  margin-left: 23px;
  left:7px;
  top:5px;
  border-radius: 0 0 20% 20%;
}
#name {
  font-weight: bolder;
  font-size: 22px;
  color: black;
	direction: rtl;
	margin-right:10px;
	margin-top:-2px;
}
#inline {
  /* position: absolute; */
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  justify-content: flex-end;
  top: 109px;
}
#cardnumber {
  font-weight: bolder;
  font-size: 13px;
  color: black;
	direction: rtl;
	margin-right:90px;
	margin-top:86px;
}
#dob {
  font-weight: bolder;
  font-size: 13px;
  color: black;
	direction: rtl;
	margin-right:90px;
	margin-top:1px;
}
#sex {
  position: absolute;
  left: 90px;
}
#height {
  position: absolute;
  left: 150px;
  width: 60px;
}
#signature {
  font-weight: bolder;
  font-size: 15px;
  color: black;
	direction: rtl;
	margin-right:90px;
	margin-top:1px;
}
#role {
  position: absolute;
  bottom: 84px;
  left: 280px;
  color: #E55B00;
  font-size: 22px;
  text-transform:capitalize;
}
#namesponser {
	font-weight: bolder;
	position: absolute;
	bottom: 150px;
	left: 280px;
	color: #FFFFFF;
	font-size: 22px;
}
#jobsponser {
	font-weight: bolder;
	position: absolute;
	bottom: 70px;
	left: 90px;
	color: #000000;
	font-size: 22px;
	font-family: justsignature;
}
#licenses {
  position: absolute;
  top: 109px;
  right: 33px;
  width: 50px;
}


#unemployed { position: relative; display: block; margin: 230px 50px 0 0; float: right; width: 457px; height: 272px; padding: 54px 17px 10px 144px; background: url("../images/unemployed.png"); }
img { position: absolute; width: 105px; height: 130px; object-fit: cover; margin-top: 75px; margin-left: 23px; left: 7px; top: 5px; border-radius: 0 0 20% 20%; } #name { font-weight: bolder;
   font-size: 22px; color: black; direction: rtl; margin-right: 10px; margin-top: -2px; } #inline { display: flex; align-items: flex-end; flex-direction: column; justify-content: flex-end; 
    top: 109px; } #cardnumber { font-weight: bolder; font-size: 13px; color: black; direction: rtl; margin-right: 90px; margin-top: 86px; } #dob { font-weight: bolder; font-size: 13px;
       color: black; direction: rtl; margin-right: 90px; margin-top: 1px; } #sex { position: absolute; left: 90px; } #height { position: absolute; left: 150px; width: 60px; } 
       #signature { font-weight: bolder; font-size: 15px; color: black; direction: rtl; margin-right: 90px; margin-top: 1px; } #role { position: absolute; bottom: 84px; left: 280px; 
        color: #E55B00; font-size: 22px; text-transform: capitalize; } #namesponser { font-weight: bolder; position: absolute; bottom: 150px; left: 280px; color: #FFFFFF; 
        font-size: 22px; } #jobsponser { font-weight: bolder; position: absolute; bottom: 70px; left: 90px; color: #000000; font-size: 22px; font-family: justsignature; }
        #licenses { position: absolute; top: 109px; right: 33px; width: 50px; }