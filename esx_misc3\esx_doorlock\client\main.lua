local Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}

local GUI                     = {}
GUI.Time                      = 0

local DoorList = {

	--
	-- Mission Row First Floor
	--

	-- Entrance Doors
	{
		objName = 'v_ilev_ph_door01',
		objCoords  = {x = 434.747, y = -980.618, z = 30.839},
		textCoords = {x = 434.747, y = -981.50, z = 31.50},
		authorizedJobs = {'all'},
		locked = false,
		distance = 2.5,
		doorKey = 'E'
	},

	{
		objName = 'v_ilev_ph_door002',
		objCoords  = {x = 434.747, y = -983.215, z = 30.839},
		textCoords = {x = 434.747, y = -982.50, z = 31.50},
		authorizedJobs = {'all'},
		locked = false,
		distance = 2.5,
		doorKey = 'E'
	},


	{
		objName = 'v_ilev_bk_vaultdoor',
		objCoords  = vector3(255.2283, 223.976, 102.3932),
		textCoords = vector3(255.2283, 223.976, 102.3932),
		authorizedJobs = { 'all' },
		locked = true,
		distance = 2.5,
		doorKey = 'H'
	},

	{
		objName = 'hei_v_ilev_bk_gate2_pris',
		objCoords  = vector3(261.99899291992, 221.50576782227, 106.68346405029),
		textCoords = vector3(261.99899291992, 221.50576782227, 107.68346405029),
		authorizedJobs = { 'all' },
		locked = true,
		distance = 2.5,
		doorKey = 'E'
	},

	-- To locker room & roof
	{
		objName = 'v_ilev_ph_gendoor004',
		objCoords  = {x = 449.698, y = -986.469, z = 30.689},
		textCoords = {x = 450.104, y = -986.388, z = 31.739},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- Rooftop
	{
		objName = 'v_ilev_gtdoor02',
		objCoords = {x = 464.361, y = -984.678, z = 43.834},
		textCoords = {x = 464.361, y = -984.050, z = 44.834},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},


	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x = 752.2177, y = -2793.581, z = 7.178009},
		textCoords = {x = 752.2177, y = -2793.581, z = 7.178009},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},



	-- Hallway to roof
	{
		objName = 'v_ilev_arm_secdoor',
		objCoords  = {x = 461.286, y = -985.320, z = 30.839},
		textCoords = {x = 461.50, y = -986.00, z = 31.50},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- Armory
	{
		objName = 'v_ilev_arm_secdoor',
		objCoords  = {x = 452.618, y = -982.702, z = 30.689},
		textCoords = {x = 453.079, y = -982.600, z = 31.739},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- Captain Office
	{
		objName = 'v_ilev_ph_gendoor002',
		objCoords  = {x = 447.238, y = -980.630, z = 30.689},
		textCoords = {x = 447.200, y = -980.010, z = 31.739},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- To downstairs (double doors)
	{
		objName = 'v_ilev_ph_gendoor005',
		objCoords  = {x = 443.97, y = -989.033, z = 30.6896},
		textCoords = {x = 444.715, y = -989.445, z = 31.739},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},

	{
		objName = 'v_ilev_ph_gendoor005',
		objCoords  = {x = 445.37, y = -988.705, z = 30.6896},
		textCoords = {x = 444.715, y = -989.445, z = 31.739},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_gendoor006',
		objCoords  = {x = 465.56, y = -988.704, z = 25.0675},
		textCoords = {x = 465.56, y = -988.960, z = 25.1775},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_gendoor006',
		objCoords  = {x = 465.57, y = -991.3001, z = 25.0675},
		textCoords = {x = 465.57, y = -990.999, z = 25.1775},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'K'
	},
	{
		objName = 'v_ilev_ph_gendoor006',
		objCoords  = {x = 468.73, y = -992.7483, z = 25.1000},
		textCoords = {x = 467.55, y = -992.7483, z = 25.1775},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'K'
	},
	{
		objName = 'v_ilev_ph_gendoor006',
		objCoords  = {x = 469.46, y = -987.2479, z = 25.0866},
		textCoords = {x = 470.59, y = -987.4352, z = 25.1775},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_gendoor006',
		objCoords  = {x = 478.42, y = -987.2479, z = 25.0866},
		textCoords = {x = 477.22, y = -987.4552, z = 25.1775},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},

	-- 
	-- Mission Row Cells
	--

	--main jail interior--
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x= 1795.28, y=2479.2, z=-118.92},
		textCoords = {x= 1795.28, y=2479.2, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1792.13, y=2479.21, z=-118.92 },
		textCoords = {x=1792.13, y=2479.21, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1788.95, y=2479.18, z=-118.92 },
		textCoords = {x=1788.95, y=2479.18, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1785.79, y=2479.21, z=-118.92 },
		textCoords = {x=1785.79, y=2479.21, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1798.45, y=2487.12, z=-118.92 },
		textCoords = {x=1798.45, y=2487.12, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1795.3, y=2487.12, z=-118.92 },
		textCoords = {x=1795.3, y=2487.12, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1792.13, y=2487.12, z=-118.92 },
		textCoords = {x=1792.13, y=2487.12, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1788.96, y=2487.12, z=-118.92 },
		textCoords = {x=1788.96, y=2487.12, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1798.44, y=2479.21, z=-118.92 },
		textCoords = {x=1798.44, y=2479.21, z=-118.92},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1798.45, y=2487.14, z=-122.54},
		textCoords = {x=1798.45, y=2487.14, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1795.29, y=2487.12, z=-122.54},
		textCoords = {x=1795.29, y=2487.12, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1792.13, y=2487.1, z=-122.54},
		textCoords = {x=1792.13, y=2487.1, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1788.96, y=2487.11, z=-122.54},
		textCoords = {x=1788.96, y=2487.11, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1785.79, y=2479.21, z=-122.54},
		textCoords = {x=1785.79, y=2479.21, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1788.95, y=2479.18, z=-122.54},
		textCoords = {x=1788.95, y=2479.18, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1792.13, y=2479.21, z=-122.54},
		textCoords = {x=1792.13, y=2479.21, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1795.29, y=2479.18, z=-122.54},
		textCoords = {x=1795.29, y=2479.18, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1798.45, y=2479.15, z=-122.54},
		textCoords = {x=1798.45, y=2479.15, z=-122.54},
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1691.0, y=2579.78, z=-69.29},
		textCoords = {x=1691.0, y=2579.78, z=-69.29},
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_cd_entrydoor',
		objCoords  = {x=1693.57, y=2581.39, z=-69.29},
		textCoords = {x=1693.57, y=2581.39, z=-69.29},
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_cd_entrydoor',
		objCoords  = {x=1702.59, y=2577.39, z=-69.29},
		textCoords = {x=1702.59, y=2577.39, z=-69.29},
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_cd_entrydoor',
		objCoords  = {x=1708.88, y=2570.69, z=-69.29},
		textCoords = {x=1708.88, y=2570.69, z=-69.29},
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_cd_entrydoor',
		objCoords  = {x=1693.18, y=2577.34, z=-69.29},
		textCoords = {x=1693.18, y=2577.34, z=-69.29},
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x=1700.85, y=2576.34, z=-69.29},
		textCoords = {x=1700.85, y=2576.34, z=-69.29},
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	

	-- Main Cells
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 463.815, y = -992.686, z = 24.9149},
		textCoords = {x = 463.30, y = -992.686, z = 25.10},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- Cell 1
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 462.381, y = -993.651, z = 24.914},
		textCoords = {x = 461.806, y = -993.308, z = 25.064},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'K'
	},

	-- Cell 2
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 462.331, y = -998.152, z = 24.914},
		textCoords = {x = 461.806, y = -998.800, z = 25.064},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- Cell 3
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 462.704, y = -1001.92, z = 24.9149},
		textCoords = {x = 461.806, y = -1002.450, z = 25.064},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'K'
	},

	-- To Back
	{
		objName = 'v_ilev_gtdoor',
		objCoords  = {x = 463.478, y = -1003.538, z = 25.005},
		textCoords = {x = 464.00, y = -1003.50, z = 25.50},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	--
	-- Mission Row Back
	--

	-- Back (double doors)
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 467.371, y = -1014.452, z = 26.536},
		textCoords = {x = 468.09, y = -1014.452, z = 27.1362},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},

	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 469.967, y = -1014.452, z = 26.536},
		textCoords = {x = 469.35, y = -1014.452, z = 27.136},
		authorizedJobs = {'all'},
		locked = true,
		distance = 4,
		doorKey = 'E'
	},

	-- Back Gate
	{
		objName = 'hei_prop_station_gate',
		objCoords  = {x = 488.894, y = -1017.210, z = 27.146},
		textCoords = {x = 488.894, y = -1020.210, z = 30.00},
		authorizedJobs = {'all'},
		locked = true,
		distance = 14,
		size = 2,
		doorKey = 'E'
	},
	
	------policegatedoorgate
	{
		objName = 'cs3_07_mpgates_02',
		objCoords  = {x = 420.29, y = -1023.68, z = 29.81},
		textCoords = {x = 420.29, y = -1023.68, z = 29.81},
		authorizedJobs = {'all'},
		locked = true,
		distance = 14,
		size = 2,
		doorKey = 'E'
	},

	--
	-- ساندي شورز
	--

	-- Entrance
	{
		objName = 'v_ilev_shrfdoor',
		objCoords  = {x = 1855.105, y = 3683.516, z = 34.266},
		textCoords = {x = 1855.105, y = 3683.516, z = 35.00},
		authorizedJobs = {'all'},
		locked = false,
		doorKey = 'E'
	},

	-- Cell 1
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 1848.02, y = 3682.5, z = 34.20},
		textCoords = {x = 1847.69, y = 3682.5, z = 34.50},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 1846.34, y = 3685.5, z = 34.20},
		textCoords = {x = 1845.8, y = 3685.4, z = 34.4},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 1844.90, y = 3688.01, z = 34.20},
		textCoords = {x = 1844.60, y = 3687.46, z = 34.4},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 1857.21, y = 3689.77, z = 34.20},
		textCoords = {x = 1857.21, y = 3690.19, z = 34.50},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 1859.63, y = 3691.64, z = 34.23},
		textCoords = {x = 1859.76, y = 3691.68, z = 34.45},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 1847.16, y = 3689.91, z = 34.42},
		textCoords = {x = 1848.32, y = 3690.53, z = 34.42},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2.5,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 1849.41, y = 3691.21, z = 34.42},
		textCoords = {x = 1848.32, y = 3690.53, z = 34.42},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2.5,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 1849.99, y = 3684.14, z = 34.42},
		textCoords = {x = 1850.58, y = 3683.01, z = 34.40},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'K'
	},
	{
		objName = 'v_ilev_rc_door2',
		objCoords  = {x = 1851.26, y = 3681.87, z = 34.42},
		textCoords = {x = 1850.58, y = 3683.01, z = 34.40},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'K'
	},

	-- Cell
	
	--loker room
	{
		objName = 'v_ilev_ct_door03',
		objCoords  = {x = 1833.2, y = 3672.4, z = -118.6},
		textCoords = {x = 1833.2, y = 3672.4, z = -118.6},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	
	--Office
	{
		objName = '	v_ilev_cd_entrydoor',
		objCoords  = {x = 1847.8, y = 3681.0, z = -118.6},
		textCoords = {x = 1847.8, y = 3681.0, z = -118.6},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
	--
	-- بليتو
	--

	-- Entrance (double doors)
	{
		objName = 'v_ilev_shrf2door',
		objCoords  = {x = -443.14, y = 6015.685, z = 31.716},
		textCoords = {x = -443.14, y = 6015.685, z = 32.00},
		authorizedJobs = {'all'},
		locked = false,
		distance = 2.5,
		doorKey = 'E'
	},

	{
		objName = 'v_ilev_shrf2door',
		objCoords  = {x = -443.951, y = 6016.622, z = 31.716},
		textCoords = {x = -443.951, y = 6016.622, z = 32.00},
		authorizedJobs = {'all'},
		locked = false,
		distance = 2.5,
		doorKey = 'E'
	},
	
	-- Cell 1
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = -433.2, y = 5992.4, z = 31.71},
		textCoords = {x = -432.9, y = 5992.9, z = 31.91},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	-- Cell 2
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = -429.11, y = 5997.0, z = 31.71},
		textCoords = {x = -428.8, y = 5997.6, z = 31.91},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},

	--Cell 3
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = -432.2, y = 6000.1, z = 31.71},
		textCoords = {x = -431.8, y = 6000.6, z = 31.91},
		authorizedJobs = {'all'},
		locked = true,
		doorKey = 'E'
	},
		--Cell 3
	{
		objName = 'v_ilev_fingate',
		objCoords  = {x = -437.6, y = 5992.8, z = 31.93},
		textCoords = {x = -436.6, y = 5991.8, z = 31.93},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
	    --المدخل الداخلي
	{
		objName = 'v_ilev_bk_door2',
		objCoords  = {x = -442.8, y = 6010.9, z = 31.86},
		textCoords = {x = -441.87, y = 6011.7, z = 31.71},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
		    --المدخل الداخلي
	{
		objName = 'v_ilev_bk_door2',
		objCoords  = {x = -440.98, y = 6012.7, z = 31.86},
		textCoords = {x = -441.87, y = 6011.7, z = 31.71},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
			    --المدخل الداخلي
	{
		objName = 'v_ilev_ss_door7',
		objCoords  = {x = -448.55, y = 6007.78, z = 31.71},
		textCoords = {x = -448.55, y = 6007.78, z = 31.81},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
			    --المدخل الداخلي
	{
		objName = 'v_ilev_ss_door8',
		objCoords  = {x = -447.70, y = 6006.70, z = 31.80},
		textCoords = {x = -448.55, y = 6007.78, z = 31.81},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
			    --مدخل الهيليكوبتر
	{
		objName = 'v_ilev_gc_door01',
		objCoords  = {x = -450.97, y = 6006.07, z = 31.99},
		textCoords = {x = -451.65, y = 6006.97, z = 31.99},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
				    --مدخل الهيليكوبتر
	{
		objName = 'v_ilev_gc_door01',
		objCoords  = {x = -447.22, y = 6002.32, z = 31.99},
		textCoords = {x = -446.28, y = 6001.54, z = 31.88},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},
					    --غرفة التحقيق
	{
		objName = 'v_ilev_cd_entrydoor',
		objCoords  = {x = -454.53, y = 6011.25, z = 31.99},
		textCoords = {x = -453.32, y = 6011.51, z = 31.99},
		authorizedJobs = {'all'},
		locked = true,
		distance = 2,
		doorKey = 'E'
	},


	{
		objName = 'prop_facgate_07',
		objCoords  = {x = -433.1, y = 6036.9, z = 30.5},
		textCoords ={x = -434.6, y = 6040.5, z = 32.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},


	--
	-- Bolingbroke Penitentiary
	--

	-- Entrance (Two big gates)
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1844.998, y = 2604.810, z = 44.638},
		textCoords = {x = 1844.998, y = 2608.50, z = 48.00},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},

	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 817.9813, y = -2957.105, z = 6.18786},
		textCoords = {x = 817.9813, y = -2957.105, z = 6.18786},
		authorizedJobs = {'all'},
		locked = true,
		distance = 10,
		size = 1,
		doorKey = 'E'
	},

	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 816.3597, y = -2955.197, z = 6.18786},
		textCoords = {x = 816.3597, y = -2955.197, z = 6.18786},
		authorizedJobs = {'all'},
		locked = true,
		distance = 10,
		size = 1,
		doorKey = 'K'
	},

	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x = 813.7058, y = -2955.197, z = 6.18786},
		textCoords = {x = 813.7058, y = -2955.197, z = 6.18786},
		authorizedJobs = {'all'},
		locked = true,
		distance = 10,
		size = 1,
		doorKey = 'L'
	},
	

	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1818.542, y = 2604.812, z = 44.611},
		textCoords = {x = 1818.542, y = 2608.40, z = 48.00},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	--كراج سيارات الشرطة
	{
		objName = 'prop_facgate_07',
		objCoords  = {x = 1877.2, y = 3687.4, z = 32.7},
		textCoords = {x = 1873.2, y = 3685.4, z = 33.7},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},
		--كراج سيارات الشرطة
	{
		objName = 'prop_facgate_07',
		objCoords  = {x = 1858.04, y = 3719.9, z = 32.1},
		textCoords ={x = 1855.04, y = 3718.2, z = 33.7},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},


	--
	-- مابات السيرفر
			--الدواجن
			
			--البوابة الجانبية  
	--724.09, -2614.78, 16.9119034
	--G1
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 724.09, y = -2614.78, z = 16.9119034},
		textCoords ={x = 724.09, y = -2614.78, z = 16.9119034},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},
	--738.87, -2614.92, 16.8867989
	     --G1
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 738.87, y = -2614.92, z = 16.8867989},
		textCoords ={x = 738.87, y = -2614.92, z = 16.8867989},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
		--700.1705, -2777.014, 5.18705845
		     --G1
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 700.1705, y = -2777.014, z = 5.18705845},
		textCoords ={x = 700.1705, y = -2777.014, z = 5.18705845},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'H'
	},
	--758.128, -3055.1, 5.07225037
		     --بوابة الدخول
	{
		objName = 'prop_gate_military_01',
		objCoords  = {x = 758.128, y = -3055.1, z = 5.07225037},
		textCoords ={x = 758.128, y = -3055.1, z = 5.07225037},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
	--825.48, -2945.872, 4.905876
			     --الدخول الخروج
	{
		objName = 'prop_gate_military_01',
		objCoords  = {x = 825.48, y = -2945.872, z = 4.905876},
		textCoords ={x = 825.48, y = -2945.872, z = 4.905876},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},
	--825.2298, -3035.842, 4.738614
				     --G2
	{
		objName = 'prop_gate_military_01',
		objCoords  = {x = 825.2298, y = -3035.842, z = 4.738614},
		textCoords ={x = 825.2298, y = -3035.842, z = 4.738614},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},
	--634.46, -2491.768, 16.36876
					     --G2
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 634.46, y = -2491.768, z = 16.36876},
		textCoords ={x = 634.46, y = -2491.768, z = 16.36876},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
	--634.47, -2506.71362, 16.380455
						     --G2
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 634.47, y = -2506.71362, z = 16.380455},
		textCoords ={x = 634.47, y = -2506.71362, z = 16.380455},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
	---202.615051, -2515.30933, 5.047173
						     --G2
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = -202.615051, y = -2515.30933, z = 5.047173},
		textCoords ={x = -202.615051, y = -2515.30933, z = 5.047173},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
	---187.340591, -2515.30933, 5.047173
						     --G2
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = -187.340591, y = -2515.30933, z = 5.047173},
		textCoords ={x = -187.340591, y = -2515.30933, z = 5.047173},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
	--19.40451, -2529.702, 5.047173
						     --مدخل الميناء من {G2}
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 19.40451, y = -2529.702, z = 5.047173},
		textCoords ={x = 19.40451, y = -2529.702, z = 5.047173},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},
	--10.6441355, -2542.213, 5.047173
						     --مخرج الميناء من {G2}
	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 10.6441355, y = -2542.213, z = 5.047173},
		textCoords ={x = 10.6441355, y = -2542.213, z = 5.047173},
		authorizedJobs = {'agent','police','ambulance','admin'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},




	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 699.3339, y = -2779.973, z = 5.094645},
		textCoords = {x = 699.3339, y = -2779.973, z = 7.09},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},

	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 698.748, y = -2795.415, z = 5.094645},
		textCoords = {x = 698.748, y = -2795.415, z = 7.09},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},


	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 717.4511, y = -2817.141, z = 5.443544},
		textCoords = {x = 717.4511, y = -2817.141, z = 7.443544},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},


	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 758.0107, y = -2993.25, z = 4.73497},
		textCoords = {x = 758.0107, y = -2993.25, z = 7.0},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},

	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 779.3553, y = -3003.098, z = 4.73497},
		textCoords = {x = 779.3553, y = -3003.098, z = 6.73497},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},

							     --المدخل الجانبي
	 	{
		objName = 'prop_gate_airport_01',
		objCoords  = {x = 564.5, y = -2574.9, z = 5.4},
		textCoords ={x = 566.5, y = -2572.1, z = 8.9},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},
							     --المدخل الجانبي
	 	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 576.3, y = -2561.9, z = 5.4},
		textCoords ={x = 573.9, y = -2564.05, z = 8.9},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'K'
	},
		 	{
		objName = 'prop_gate_docks_ld',
		objCoords  = {x = 724.27, y = -2615.01, z = 17.9},
		textCoords ={x = 724.27, y = -2615.01, z = 17.9},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2.5,
		doorKey = 'E'
	},

							 {
								objName = 'prop_gate_prison_01',
								objCoords  = {x = 825.9023, y = -2950.115, z = 4.611897},
								textCoords ={x = 825.9023, y = -2950.115, z = 7.2},
								authorizedJobs = {'all'},
								locked = true,
								distance = 12,
								size = 2.5,
								doorKey = 'K'
							},

						     --مدخل مركز الرقابةوالتحقيق
	 	{
		objName = 'apa_prop_ss1_mpint_door_r',
		objCoords  = {x = 242.02, y = -1149.09, z = 31.06},
		textCoords ={x = 242.02, y = -1149.09, z = 31.06},
		authorizedJobs = {'admin'},
		locked = true,
		distance = 3,
		size = 1,
		doorKey = 'E'
	},
						     --مدخل مركز الرقابةوالتحقيق
	 	{
		objName = 'apa_prop_ss1_mpint_door_l',
		objCoords  = {x = 241.89, y = -1148.33, z = 31.8},
		textCoords ={x = 241.89, y = -1148.33, z = 31.8},
		authorizedJobs = {'admin'},
		locked = true,
		distance = 3,
		size = 1,
		doorKey = 'K'
	},
						     --مركز الرقابة والتحقيق المدخل الداخلي
{
		objName = 'apa_prop_ss1_mpint_door_l',
		objCoords  = {x = 256.5, y = -1151.6, z = 30.9},
		textCoords ={x = 256.3, y = -1151.1, z = 30.95},
		authorizedJobs = {'admin'},
		locked = true,
		distance = 3,
		size = 1,
		doorKey = 'K'
	},
						     --مركز الرقابة والتحقيق المدخل الداخلي
{
		objName = 'apa_prop_ss1_mpint_door_r',
		objCoords  = {x = 256.3, y = -1149.5, z = 30.9},
		textCoords ={x = 256.3, y = -1150.05, z = 30.95},
		authorizedJobs = {'admin'},
		locked = true,
		distance = 3,
		size = 1,
		doorKey = 'E'
	},
						     -- سجن الرقابة والتحقيق
	 	{
		objName = 'prop_ld_jail_door',
		objCoords  = {x = 263.7, y = -1164.3, z = 30.5},
		textCoords ={x = 263.7, y = -1165.4, z = 30.5},
		authorizedJobs = {'admin'},
		locked = true,
		distance = 3,
		size = 1.5,
		doorKey = 'K'
	},
						     -- الرقابة والتحقيق غرفة تسجيل الدخول 
	 	{
		objName = 'apa_p_mp_door_apart_door_black',
		objCoords  = {x = 266.5, y = -1167.3, z = 30.6},
		textCoords ={x = 265.4, y = -1167.3, z = 30.6},
		authorizedJobs = {'admin'},
		locked = true,
		distance = 3,
		size = 1.5,
		doorKey = 'E'
	},
			-- السجن
{
		objName = 'v_liev_ph_gendoor002',
		objCoords  = {x=1688.5, y= 2577.7, z=- 46.0 },
		textCoords = {x=1688.5, y= 2577.7, z=- 46.0 },
		authorizedJobs = {'all'},
		locked = false,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x=1704.8, y= 2568.7, z= 45.5 },
		textCoords = {x=1704.4, y= 2568.7, z= 45.5 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x=1700.2, y= 2568.4, z= 45.5 },
		textCoords = {x=1700.7, y= 2568.4, z= 45.7 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
		{
		objName = 'v_ilev_ph_door002',
		objCoords  = {x=1691.5, y= 2567.2, z= 45.5 },
		textCoords = {x=1691.5, y= 2566.6, z= 45.5 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x=1676.6, y= 2568.5, z= 45.5 },
		textCoords = {x=1677.2, y= 2568.5, z= 45.7 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x=1680.1, y= 2568.5, z= 45.5 },
		textCoords = {x=1680.6, y= 2568.5, z= 45.8 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x=1690.7, y= 2575.4, z= 45.9 },
		textCoords = {x=1690.7, y= 2575.9, z= 45.9 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
		{
		objName = 'v_ilev_ph_cellgate',
		objCoords  = {x=1690.6, y= 2581.1, z= 45.9 },
		textCoords = {x=1690.6, y= 2581.5, z= 45.9 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_door002',
		objCoords  = {x=1690.6, y= 2590.2, z= 45.9 },
		textCoords = {x=1690.6, y= 2590.7, z= 45.9 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'v_ilev_ph_door002',
		objCoords  = {x=1686.9, y= 2572.5, z= 50.4 },
		textCoords = {x=1686.9, y= 2573.2, z= 50.6 },
		authorizedJobs = {'all'},
		locked = true,
		distance = 1.5,
		size = 1.0,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1799.61, y = 2616.97, z = 44.6},
		textCoords = {x = 1795.85, y = 2616.97, z = 47.8},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1762.5, y = 2426.5, z = 44.4},
		textCoords = {x = 1755.8, y = 2423.3, z = 48.1},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1749.1, y = 2419.8, z = 44.4},
		textCoords = {x = 1755.8, y = 2423.3, z = 48.1},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1652.9, y = 2409.5, z = 44.4},
		textCoords = {x = 1660.4, y = 2408.7, z = 48.1},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1667.6, y = 2407.6, z = 44.4},
		textCoords = {x = 1660.4, y = 2408.7, z = 48.1},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1550.9, y = 2482.7, z = 44.3},
		textCoords = {x = 1555.2, y = 2476.5, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1558.2, y = 2469.3, z = 44.3},
		textCoords = {x = 1555.2, y = 2476.5, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1547.7, y = 2591.2, z = 44.5},
		textCoords = {x = 1548.1, y = 2583.8, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1546.9, y = 2576.1, z = 44.3},
		textCoords = {x = 1548.1, y = 2583.8, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1584.6, y = 2679.7, z = 44.5},
		textCoords = {x = 1580.8, y = 2672.9, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1575.7, y = 2667.1, z = 44.5},
		textCoords = {x = 1580.8, y = 2672.9, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1662.0, y = 2748.7, z = 44.4},
		textCoords = {x = 1655.7, y = 2744.4, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1648.4, y = 2741.6, z = 44.4},
		textCoords = {x = 1655.7, y = 2744.4, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1776.7, y = 2747.1, z = 44.4},
		textCoords = {x = 1769.2, y = 2749.0, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1762.1, y = 2752.4, z = 44.4},
		textCoords = {x = 1769.2, y = 2749.0, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1835.2, y = 2689.1, z = 44.4},
		textCoords = {x = 1831.9, y = 2695.9, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1830.1, y = 2703.5, z = 44.4},
		textCoords = {x = 1831.9, y = 2695.9, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1808.9, y = 2474.5, z = 44.4},
		textCoords = {x = 1810.5, y = 2481.8, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'prop_gate_prison_01',
		objCoords  = {x = 1813.7, y = 2488.9, z = 44.4},
		textCoords = {x = 1810.5, y = 2481.8, z = 48.3},
		authorizedJobs = {'all'},
		locked = true,
		distance = 12,
		size = 2,
		doorKey = 'E'
	},
	{
		objName = 'hei_prop_ss1_mpint_garage2',
		objCoords  = {x = 1465.591, y = 3748.255, z = 36.27},
		textCoords = {x = 1465.591, y = 3748.255, z = 36.27},
		authorizedJobs = {'all', 'mechanic'},
		locked = true,
		distance = 15,
		size = 3,
		doorKey = 'E'
	},
	{
		objName = 'hei_prop_ss1_mpint_garage2',
		objCoords  = {x = 1426.936, y = 3732.479, z = 36.28},
		textCoords = {x = 1426.936, y = 3732.479, z = 36.28},
		authorizedJobs = {'all', 'mechanic'},
		locked = true,
		distance = 15,
		size = 3,
		doorKey = 'H'
	},






	 
	
	--main jail interior--
	
	--[[
	-- Entrance Gate (Mission Row mod) https://www.gta5-mods.com/maps/mission-row-pd-ymap-fivem-v1
	{
		objName = 'prop_gate_airport_01',
		objCoords  = {x = 420.133, y = -1017.301, z = 28.086},
		textCoords = {x = 420.133, y = -1021.00, z = 32.00},
		authorizedJobs = {'police','admin'},
		locked = true,
		distance = 14,
		size = 2
	}
	--]]
}

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	ESX.PlayerData = ESX.GetPlayerData()

	-- Update the door list
	ESX.TriggerServerCallback('esx_doorlock:getDoorInfo', function(doorInfo)
		for k,v in pairs(doorInfo) do
			--DoorList[v.doorID].locked = v.state
			DoorList[k].locked = v.state
		end
	end)
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
	TriggerServerEvent('esx_doorlock:updateCache', job)
end)

Citizen.CreateThread(function()
	local draw = false
	
	while true do

		local wait = 1000
		
		local playerCoords = GetEntityCoords(PlayerPedId())
		draw = false
		
		for i=1, #DoorList do
			local doorID   = DoorList[i]
			local distance = GetDistanceBetweenCoords(playerCoords, doorID.objCoords.x, doorID.objCoords.y, doorID.objCoords.z, true)
			local isAuthorized = IsAuthorized(doorID)
			local doorKey = DoorList[i].doorKey
			
			local maxDistance = 1.25
			if doorID.distance then
				maxDistance = doorID.distance
			end
		
			if distance < 10 then
				wait = 10
				draw = true
				ApplyDoorState(doorID)

				local size = 1
				if doorID.size then
					wait = 10
					size = doorID.size
				end

				local displayText = _U('unlocked')
				if doorID.locked then
					wait = 10
					displayText = _U('locked')
				end

				if isAuthorized then
					displayText = '['..doorKey..'] '..displayText
				end
				
				if distance < maxDistance then
					ESX.Game.Utils.DrawText3D(doorID.textCoords, displayText, size)
					
					if IsControlJustReleased(0, Keys[doorKey]) then
						wait = 10
						if isAuthorized then
							doorID.locked = not doorID.locked
							ApplyDoorState(doorID)
	
							TriggerServerEvent('esx_doorlock:updateState', i, doorID.locked) -- Broadcast new state of the door to everyone
						end
					end
				end	
			end
			GUI.Time      = GetGameTimer()
		end
		
		if draw then
			Citizen.Wait(0)
		else
			Citizen.Wait(wait)
		end
	end
end)

function ApplyDoorState(doorID)
	local closeDoor = GetClosestObjectOfType(doorID.objCoords.x, doorID.objCoords.y, doorID.objCoords.z, 1.0, GetHashKey(doorID.objName), false, false, false)
	FreezeEntityPosition(closeDoor, doorID.locked)
end

function IsAuthorized(doorID)
	while ESX == nil do
		Citizen.Wait(100)
	end
	
	if ESX.PlayerData.job == nil then
		return false
	end

	for i=1, #doorID.authorizedJobs, 1 do
		if doorID.authorizedJobs[i] == ESX.PlayerData.job.name then
			return true
		elseif doorID.authorizedJobs[i] == "all" and isLoeJob() then
			return true
		end
	end

	return false
end

-- Set state for a door
RegisterNetEvent('esx_doorlock:setState')
AddEventHandler('esx_doorlock:setState', function(doorID, state)
	DoorList[doorID].locked = state
end)

function isLoeJob()
	while ESX.PlayerData.job == nil do
		Citizen.Wait(500)
	end
	
	for i=1, #LeoJobs, 1 do
		if ESX.PlayerData.job.name == LeoJobs[i] then
			check = true
			return true
		end
	end
	
	return false
end	