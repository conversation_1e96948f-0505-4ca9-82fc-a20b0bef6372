local seatsTaken = {} -- esx_sit

ESX.RegisterServerCallback('esx_carwash:canAfford', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.EnablePrice then
		if xPlayer.getMoney() >= Config.Price then
			xPlayer.removeMoney(Config.Price)
			cb(true)
		else
			cb(false)
		end
	else
		cb(true)
	end
end)

ESX.RegisterServerCallback('esx_misc2:carwash:canAfford', function(source, cb) -- car wash
	local xPlayer = ESX.GetPlayerFromId(source)

	if Config.EnablePrice then
		if xPlayer.getMoney() >= Config.Price then
			xPlayer.removeMoney(Config.Price)
			cb(true)
		else
			cb(false)
		end
	else
		cb(true)
	end
end)

-- ESX.RegisterUsableItem('fixkit', function(source) -- repair veh START
-- 	local _source = source
-- 	local xPlayer = ESX.GetPlayerFromId(_source)
-- 	local fixkitItem = xPlayer.getInventoryItem('fixkit')
-- 	if fixkitItem.count < 1 then
-- 		TriggerClientEvent('esx:showNotification', source, 'لا تمتلك عدة تصليح')
-- 	else
-- 		TriggerClientEvent('esx_misc2:repairveh', source)
-- 	end
-- end)

RegisterServerEvent('esx_misc2:RemoveItem_repair')
AddEventHandler('esx_misc2:RemoveItem_repair', function(ped, coords, veh)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)
	xPlayer.removeInventoryItem('fixkit', 1)
	TriggerClientEvent('esx_misc2:Start_repair', _source, ped, coords, veh)
end)