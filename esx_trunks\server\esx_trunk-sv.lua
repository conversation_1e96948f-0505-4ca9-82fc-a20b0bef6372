local arrayWeight = Config.localWeight
local VehicleList = { }
local VehicleInventory = {}


AddEventHandler('onMySQLReady', function ()
	MySQL.Async.execute( 'DELETE FROM `trunk_inventory` WHERE `owner` = 0', {})
end)

RegisterServerEvent('esx_trunk_inventory:getOwnedVehicule')
AddEventHandler('esx_trunk_inventory:getOwnedVehicule', function()
  local vehicules = {}
  local _source = source
  local xPlayer = ESX.GetPlayerFromId(_source)
  MySQL.Async.fetchAll(
      'SELECT * FROM owned_vehicles WHERE owner = @owner',
   		{
   			['@owner'] = xPlayer.identifier
   		},
    function(result)
      if result ~= nil and #result > 0 then
          for _,v in pairs(result) do
      			local vehicle = json.decode(v.vehicle)
      			table.insert(vehicules, {plate = vehicle.plate})
      		end
      end
    TriggerClientEvent('esx_trunk_inventory:setOwnedVehicule', _source, vehicules)
    end)
end)

function getItemWeight(item)
  local weight = 0
  local itemWeight = 0
    if item ~= nil then
      itemWeight = Config.DefaultWeight
      if arrayWeight[item] ~= nil then
        itemWeight = arrayWeight[item]
      end
    end
  return itemWeight
end

function getInventoryWeight(inventory)
  local weight = 0
  local itemWeight = 0
  if inventory ~= nil then
	  for i=1, #inventory, 1 do
	    if inventory[i] ~= nil then
	      itemWeight = Config.DefaultWeight
	      if arrayWeight[inventory[i].name] ~= nil then
	        itemWeight = arrayWeight[inventory[i].name]
	      end
	      weight = weight + (itemWeight * (inventory[i].count or 1))
	    end
	  end
  end
  return weight
end

function getTotalInventoryWeight(plate)
  local total
  TriggerEvent('esx_trunk:getSharedDataStore',plate,function(store)
    local W_weapons = getInventoryWeight(store.get('weapons') or {})
    local W_coffre = getInventoryWeight(store.get('coffre') or {})
    local W_blackMoney =0
    local blackAccount = (store.get('black_money')) or 0
    if blackAccount ~=0 then
      W_blackMoney = blackAccount /10
    end
    total = W_weapons + W_coffre + W_blackMoney
  end)
  return total
end

function getVOwnerName(plate)
	local ownername
	local resultTwo = MySQL.Sync.fetchAll('SELECT owner FROM owned_vehicles WHERE plate = @plate', {
		['@plate'] = plate
	})
	if resultTwo[1] ~= nil then
		local resultTwo2 = MySQL.Sync.fetchAll('SELECT firstname, lastname FROM users WHERE identifier = @identifier', {
			['@identifier'] = resultTwo[1].owner
		})
		if resultTwo2[1] ~= nil then
    		ownername = resultTwo2[1].firstname.. ' ' ..resultTwo2[1].lastname
		else
			ownername = 'لم يتم العثور على مالك للمركبة'
		end
	else
		ownername = 'لم يتم العثور على مالك للمركبة'
	end
  return ownername
end

ESX.RegisterServerCallback('esx_trunk:getInventoryV',function(source,cb,plate)
  TriggerEvent('esx_trunk:getSharedDataStore',plate,function(store)
    if store == nil then  return end
    local blackMoney = 0
     local items      = {}
     local weapons    = {}
    weapons = (store.get('weapons') or {})

    local blackAccount = (store.get('black_money')) or 0
    if blackAccount ~=0 then
      blackMoney = blackAccount
    end

    local coffre = (store.get('coffre') or {})
    for i=1,#coffre,1 do
      table.insert(items,{name=coffre[i].name,count=coffre[i].count,label=ESX.GetItemLabel(coffre[i].name)})
    end

    local weight = getTotalInventoryWeight(plate)
    cb({
    blackMoney = blackMoney,
    items      = items,
    weapons    = weapons,
    weight     = weight
    })
  end)
end)

RegisterServerEvent('esx_trunk:getItem')
AddEventHandler('esx_trunk:getItem', function(plate, type, item, count)

  local _source      = source
  local xPlayer      = ESX.GetPlayerFromId(_source)
  local xItem
  
  if type == 'item_standard' then

    TriggerEvent('esx_trunk:getSharedDataStore', plate, function(store)
      local coffre = (store.get('coffre') or {})
      for i=1, #coffre,1 do
        if coffre[i].name == item then
          if (coffre[i].count >= count and count > 0) then
            xItem = xPlayer.getInventoryItem(item)
              if xPlayer.canCarryItem(item, count) then 
                xPlayer.addInventoryItem(item, count)
                if (coffre[i].count - count) == 0 then
                  table.remove(coffre,i)
                else
                  coffre[i].count = coffre[i].count - count
                end
              else
                TriggerClientEvent('esx:showNotification', _source, "الحقيبة ممتلئة")
              end
            break
          else
            TriggerClientEvent('esx:showNotification', _source, _U('invalid_quantity'))
          end
        end
      end
      store.set('coffre',coffre)
			TriggerEvent("zahya-logs:server:SendLog", "trunkgetItem", "**سحب من مركبة ايتم**", "red", "`: لوحة المركبة` \n ".. plate .."\n \n `: صاحب المركبة` \n ".. getVOwnerName(plate) .."\n \n `: إسم المواطن` \n ".. xPlayer.name .."\n \n `: الايتم` \n ".. xItem.label .."\n \n `: الكميه` \n ".. count .."\n")
    end)
  end

  if type == 'item_account' then

    TriggerEvent('esx_trunk:getSharedDataStore', plate, function(store)

      local blackMoney = store.get('black_money')
      if (blackMoney >= count and count > 0) then
        blackMoney = blackMoney - count
        store.set('black_money', blackMoney)
        xPlayer.addAccountMoney(item, count)
        TriggerEvent("zahya-logs:server:SendLog", "trunkgetItem", "**سحب من مركبة اموال غير شرعية**", "red", "`: لوحة المركبة` \n ".. plate .."\n \n `: صاحب المركبة` \n ".. getVOwnerName(plate) .."\n \n `: إسم المواطن` \n ".. xPlayer.name .."\n \n `: الكميه` \n ".. count .."\n")
      else
        TriggerClientEvent('esx:showNotification', _source, _U('invalid_amount'))
      end
    end)

  end

  if type == 'item_weapon' then

    TriggerEvent('esx_trunk:getSharedDataStore',  plate, function(store)

      local storeWeapons = store.get('weapons')

      if storeWeapons == nil then
        storeWeapons = {}
      end

      local weaponName   = nil
      local ammo         = nil

      for i=1, #storeWeapons, 1 do
        if storeWeapons[i].name == item then

          weaponName = storeWeapons[i].name
          ammo       = storeWeapons[i].ammo

          table.remove(storeWeapons, i)

          break
        end
      end

      store.set('weapons', storeWeapons)

      xPlayer.addWeapon(weaponName, ammo)
	  TriggerEvent("zahya-logs:server:SendLog", "trunkgetItem", "**سحب من مركبة سلاح**", "red", "`: لوحة المركبة` \n ".. plate .."\n \n `: صاحب المركبة` \n ".. getVOwnerName(plate) .."\n \n `: إسم المواطن` \n ".. xPlayer.name .."\n \n `: السلاح` \n ".. weaponName .."\n \n `: الطلقات` \n ".. ammo .."\n")
      --exports.JD_logs:discord('🔫 أخذ '.. weaponName ..' مع ('.. ammo ..') طلقة من شنطة السيارة: ['.. plate ..']', xPlayer.source, 0, '15874618', 'removetrunk')
    end)

  end

end)

RegisterServerEvent('esx_trunk:putItem')
AddEventHandler('esx_trunk:putItem', function(plate, type, item, count,max, owner)

  local _source      = source
  local xPlayer      = ESX.GetPlayerFromId(_source)
  local xPlayerOwner = ESX.GetPlayerFromIdentifier(owner)

  if type.typed == 'item_standard' then

    local playerItemCount = xPlayer.getInventoryItem(item).count

    if (playerItemCount >= count and count > 0 )then


      TriggerEvent('esx_trunk:getSharedDataStore', plate, function(store)
        local found = false
        local coffre = (store.get('coffre') or {})


        for i=1,#coffre,1 do
          if coffre[i].name == item then
            coffre[i].count = coffre[i].count + count
            found = true
          end
        end
        if not found then
          table.insert(coffre, {
            name = item,
            count = count
          })
        end
        if (getTotalInventoryWeight(plate)+(getItemWeight(item)*count))>max then
            TriggerClientEvent('esx:showNotification', _source, _U('insufficient_space'))
        else
          -- Checks passed, storing the item.
		      xPlayer.removeInventoryItem(item, count)
          store.set('coffre', coffre)
		  local xItem = xPlayer.getInventoryItem(item)
		  TriggerEvent("zahya-logs:server:SendLog", "trunkputItem", "**ايداع في مركبة ايتم**", "red", "`: لوحة المركبة` \n ".. plate .."\n \n `: صاحب المركبة` \n ".. getVOwnerName(plate) .."\n \n `: إسم المواطن` \n ".. xPlayer.name .."\n \n `: الايتم` \n ".. xItem.label .."\n \n `: الكميه` \n ".. count .."\n")
          MySQL.Async.execute( 'UPDATE trunk_inventory SET owner = @owner WHERE plate = @plate',
          {
            ['@plate'] = plate,
            ['@owner'] = owner,
          })

        end
      end)

    else
      TriggerClientEvent('esx:showNotification', _source, _U('invalid_quantity'))
    end

  end

  if type.typed == 'item_account' then

    local playerAccountMoney = xPlayer.getAccount(item).money


    if (playerAccountMoney >= count and count > 0) then


      TriggerEvent('esx_trunk:getSharedDataStore', plate , function(store)
        if (getTotalInventoryWeight(plate)+type.money/10) > max then
          TriggerClientEvent('esx:showNotification', _source, _U('insufficient_space'))
        else
          
          xPlayer.removeAccountMoney(item, count)
		  -- Checks passed. Storing the item.
		  local myblackmoney = (store.get('black_money')) or 0
		  if myblackmoney ~=0 then
			store.set('black_money', type.money+myblackmoney)
		  else
		  store.set('black_money', type.money)
		  end
         TriggerEvent("zahya-logs:server:SendLog", "trunkputItem", "**ايداع في مركبة اموال غير شرعية**", "red", "`: لوحة المركبة` \n ".. plate .."\n \n `: صاحب المركبة` \n ".. getVOwnerName(plate) .."\n \n `: إسم المواطن` \n ".. xPlayer.name .."\n \n `: الكميه` \n ".. count .."\n")
          MySQL.Async.execute( 'UPDATE trunk_inventory SET owner = @owner WHERE plate = @plate',
          {
            ['@plate'] = plate,
            ['@owner'] = owner,
          })
        end
      end)

    else
      TriggerClientEvent('esx:showNotification', _source, _U('invalid_amount'))
    end

  end

  if type == 'item_weapon' then
    TriggerEvent('esx_trunk:getSharedDataStore', plate, function(store)
      local storeWeapons = store.get('weapons')

      if storeWeapons == nil then
        storeWeapons = {}
      end

      table.insert(storeWeapons, {
        name = item,
        ammo = count
      })
      if (getTotalInventoryWeight(plate)+(getItemWeight(item)))>max then
          TriggerClientEvent('esx:showNotification', _source, _U('invalid_amount'))
      else
        store.set('weapons', storeWeapons)
        xPlayer.removeWeapon(item)
        TriggerEvent("zahya-logs:server:SendLog", "trunkputItem", "**ايداع في مركبة سلاح**", "red", "`: لوحة المركبة` \n ".. plate .."\n \n `: صاحب المركبة` \n ".. getVOwnerName(plate) .."\n \n `: إسم المواطن` \n ".. xPlayer.name .."\n \n `: السلاح` \n ".. item .."\n \n `: الطلقات` \n ".. count .."\n")
        MySQL.Async.execute( 'UPDATE trunk_inventory SET owner = @owner WHERE plate = @plate',
        {
          ['@plate'] = plate,
          ['@owner'] = owner,
        })
      end
    end)

  end

end)

ESX.RegisterServerCallback('esx_trunk:getPlayerInventory', function(source, cb)

  local xPlayer    = ESX.GetPlayerFromId(source)
  local blackMoney = xPlayer.getAccount('black_money').money
  local items      = xPlayer.inventory

  cb({
    blackMoney = blackMoney,
    items      = items
  })

end)

function all_trim(s)
	if s then
		return s:match"^%s*(.*)":match"(.-)%s*$"
	else
		return 'noTagProvided'
	end
end