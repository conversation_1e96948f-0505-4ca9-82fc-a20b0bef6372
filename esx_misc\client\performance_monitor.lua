-- Performance Monitor for ESX Misc Modules
-- This script helps track performance improvements and identify bottlenecks

local performanceData = {
    frameTime = 0,
    lastFrameTime = 0,
    averageFrameTime = 0,
    frameCount = 0,
    maxFrameTime = 0,
    minFrameTime = 999,
    lastReportTime = 0,
    reportInterval = 30000, -- Report every 30 seconds
    samples = {}
}

local isMonitoringEnabled = false

-- Command to toggle performance monitoring
RegisterCommand('perfmon', function(source, args)
    if args[1] == 'on' then
        isMonitoringEnabled = true
        ESX.ShowNotification('Performance monitoring enabled')
        print('[PERFMON] Performance monitoring enabled')
    elseif args[1] == 'off' then
        isMonitoringEnabled = false
        ESX.ShowNotification('Performance monitoring disabled')
        print('[PERFMON] Performance monitoring disabled')
    elseif args[1] == 'report' then
        generatePerformanceReport()
    else
        ESX.ShowNotification('Usage: /perfmon [on|off|report]')
    end
end, false)

-- Performance monitoring thread
Citizen.CreateThread(function()
    while true do
        if isMonitoringEnabled then
            local currentTime = GetGameTimer()
            
            -- Calculate frame time
            if performanceData.lastFrameTime > 0 then
                performanceData.frameTime = currentTime - performanceData.lastFrameTime
                
                -- Update statistics
                performanceData.frameCount = performanceData.frameCount + 1
                performanceData.averageFrameTime = (performanceData.averageFrameTime * (performanceData.frameCount - 1) + performanceData.frameTime) / performanceData.frameCount
                
                if performanceData.frameTime > performanceData.maxFrameTime then
                    performanceData.maxFrameTime = performanceData.frameTime
                end
                
                if performanceData.frameTime < performanceData.minFrameTime then
                    performanceData.minFrameTime = performanceData.frameTime
                end
                
                -- Store sample for detailed analysis
                table.insert(performanceData.samples, performanceData.frameTime)
                if #performanceData.samples > 1000 then
                    table.remove(performanceData.samples, 1)
                end
                
                -- Generate report periodically
                if currentTime - performanceData.lastReportTime > performanceData.reportInterval then
                    generatePerformanceReport()
                    performanceData.lastReportTime = currentTime
                end
            end
            
            performanceData.lastFrameTime = currentTime
            Citizen.Wait(0)
        else
            Citizen.Wait(1000)
        end
    end
end)

function generatePerformanceReport()
    if performanceData.frameCount == 0 then
        print('[PERFMON] No performance data available')
        return
    end
    
    local fps = 1000 / performanceData.averageFrameTime
    local minFps = 1000 / performanceData.maxFrameTime
    local maxFps = 1000 / performanceData.minFrameTime
    
    -- Calculate frame time distribution
    local highFrameTimes = 0
    for _, frameTime in pairs(performanceData.samples) do
        if frameTime > 33.33 then -- More than 30 FPS
            highFrameTimes = highFrameTimes + 1
        end
    end
    
    local highFramePercentage = (highFrameTimes / #performanceData.samples) * 100
    
    print('=== PERFORMANCE REPORT ===')
    print(string.format('Average FPS: %.2f', fps))
    print(string.format('Min FPS: %.2f', minFps))
    print(string.format('Max FPS: %.2f', maxFps))
    print(string.format('Average Frame Time: %.2f ms', performanceData.averageFrameTime))
    print(string.format('Max Frame Time: %.2f ms', performanceData.maxFrameTime))
    print(string.format('Min Frame Time: %.2f ms', performanceData.minFrameTime))
    print(string.format('Frames > 33ms: %.2f%%', highFramePercentage))
    print(string.format('Total Frames Analyzed: %d', performanceData.frameCount))
    print('========================')
    
    -- Send notification with key metrics
    ESX.ShowNotification(string.format('Avg FPS: %.1f | Frame Time: %.1fms | Lag: %.1f%%', fps, performanceData.averageFrameTime, highFramePercentage))
end

-- Memory usage monitoring
Citizen.CreateThread(function()
    while true do
        if isMonitoringEnabled then
            collectgarbage("collect")
            local memUsage = collectgarbage("count")
            
            if memUsage > 50000 then -- More than 50MB
                print(string.format('[PERFMON] High memory usage detected: %.2f MB', memUsage / 1024))
            end
        end
        
        Citizen.Wait(10000) -- Check every 10 seconds
    end
end)

-- Export functions for other scripts to use
exports('GetAverageFPS', function()
    if performanceData.frameCount > 0 then
        return 1000 / performanceData.averageFrameTime
    end
    return 0
end)

exports('GetFrameTime', function()
    return performanceData.frameTime
end)

exports('IsPerformanceGood', function()
    local fps = 1000 / performanceData.averageFrameTime
    return fps > 30 -- Consider performance good if FPS > 30
end)
