
ESX = exports["es_extended"]:getSharedObject()

RegisterNetEvent('vehicle:unlockClient',function(netId)
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if DoesEntityExist(vehicle) then
        SetVehicleDoorsLocked(vehicle, 1) 
        SetVehicleDoorsLockedForAllPlayers(vehicle, false) 
    else
        ESX.ShowNotification("Failed to unlock the vehicle.")
    end
end)


function LockVehicle(LockFromInside)
    local ped = PlayerPedId()
    if (not LockFromInside and  IsPedInAnyVehicle(ped, false)) or (LockFromInside and not IsPedInAnyVehicle(ped, false))  then return end
    local vehicle, dist = ESX.Game.GetClosestVehicle()
    
    if vehicle and dist <= 2.0 then
        local plate = GetVehicleNumberPlateText(vehicle)
        
        ESX.TriggerServerCallback('IsVehicleOwned', function(isVehicleOwned)
            if isVehicleOwned then
                local status = GetVehicleDoorLockStatus(vehicle)
                PlayLockAnimation()
                
                
                
                if status <= 2 then
                    SetVehicleDoorsLocked(vehicle, 4)
                    SetVehicleDoorsLockedForAllPlayers(vehicle, true)
                    ESX.ShowNotification('<font color=red>تم قفل المركبة</font>')
                    TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 7, "lock", 0.1)
                elseif status > 2 then 
                    if DoesEntityExist(vehicle) then
                        local netId = NetworkGetNetworkIdFromEntity(vehicle)
						TriggerServerEvent("vehicle:unlock", netId)
                       -- print("Lock status before:", GetVehicleDoorLockStatus(vehicle))
                       -- SetVehicleDoorsLocked(vehicle, 1)
                       -- SetVehicleDoorsLockedForAllPlayers(vehicle, false)
                       -- print("Lock status after:", GetVehicleDoorLockStatus(vehicle))
                    else
                        print("Invalid vehicle entity.")
                    end
                    
                    ESX.ShowNotification('<font color=Green>تم فتح المركبة</font>')
                    TriggerServerEvent("InteractSound_SV:PlayWithinDistance", 7, "unlock",0.1)
                end
               
            end
        end, plate)
    end
end
RegisterCommand("lock", function()
    local playerPed = PlayerPedId()
    local vehicle = GetClosestVehicle(GetEntityCoords(playerPed), 5.0, 0, 71)
    if vehicle ~= 0 then
        local lockStatus = GetVehicleDoorLockStatus(vehicle)
        if lockStatus == 1 then
            SetVehicleDoorsLocked(vehicle, 2)
            ESX.ShowNotification("Locked!")
        else
            SetVehicleDoorsLocked(vehicle, 1)
            ESX.ShowNotification("Unlocked!")
        end
    else
        ESX.ShowNotification("No vehicle nearby!")
    end
end, false)


RegisterCommand('lockvehicles', function()
    LockVehicle(false)
end)
RegisterCommand('LockFromInside', function()
    LockVehicle(true)
end)
function PlayLockAnimation()
    local ped = PlayerPedId()
    local animDict = "anim@mp_player_intmenu@key_fob@"
    local animName = "fob_click"

    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(0)
    end

    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, 2000, 49, 0, false, false, false)
end

RegisterKeyMapping('lockvehicles', 'lock vehicles', 'keyboard', 'e')
RegisterKeyMapping('LockFromInside', 'Lock From Inside', 'keyboard', 'b')


