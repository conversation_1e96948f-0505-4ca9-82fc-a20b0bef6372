-- Get the ESX shared object
ESX = exports['es_extended']:getSharedObject()

-- Wait for ESX to be ready (optional but good practice)
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(100) -- Wait if ESX hasn't loaded yet
    end
    if Config.Debug then print("[nex-abandoned] ESX object ready.") end
end)

-- Table to store the last time a player exited a vehicle (using plate as key)
-- This table is populated by the 'Nex:leftVeh' event triggered FROM the server
local LastDrive = {}
-- Variable to keep track of the last vehicle the player was in (for the background thread)
local lastVehicle = nil

--[[
    Event Listener: Updates the local LastDrive table when triggered by the server.
    The server triggers this for clients after a player leaves a vehicle.
    @param plate The license plate of the vehicle the player left.
]]
RegisterNetEvent("Nex:leftVeh", function(plate)
    -- Check if the plate is valid before storing
    if plate and type(plate) == "string" and plate ~= "" then
        if Config.Debug then print("[nex-abandoned] Received Nex:leftVeh event for plate " .. plate .. ", updating LastDrive timer.") end
        LastDrive[plate] = GetGameTimer() -- Store the time the player left the vehicle
    else
        if Config.Debug then print("[nex-abandoned] Received Nex:leftVeh event with invalid plate.") end
    end
end)

--[[
    Background Thread: Detects when the local player leaves a vehicle and notifies the server.
]]
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Check every second

        local playerPed = PlayerPedId()
        -- Check if the player is valid before proceeding
        if DoesEntityExist(playerPed) then
            local currentVehicle = GetVehiclePedIsIn(playerPed, false) -- Get current vehicle (or 0 if none)

            -- Player was in a vehicle but isn't anymore
            if currentVehicle == 0 and lastVehicle ~= nil then
                if DoesEntityExist(lastVehicle) then
                    local plate = GetVehicleNumberPlateText(lastVehicle)
                    if Config.Debug then print("[nex-abandoned] Player left vehicle with plate: " .. (plate or "N/A") .. ". Notifying server.") end
                    -- Trigger a SERVER event to handle the logic.
                    if plate and plate ~= "" then
                        TriggerServerEvent('nex-abandoned:playerLeftVehicle', plate) -- Notify server
                    end
                else
                    if Config.Debug then print("[nex-abandoned] Player left a vehicle, but the entity no longer exists.") end
                end
                lastVehicle = nil -- Reset last vehicle

            -- Player entered a vehicle or is still in the same one
            elseif currentVehicle ~= 0 then
                if currentVehicle ~= lastVehicle then
                     if Config.Debug then print("[nex-abandoned] Player entered vehicle: " .. GetDisplayNameFromVehicleModel(GetEntityModel(currentVehicle))) end
                    lastVehicle = currentVehicle
                end
            end
        end
    end
end)


--[[
    Helper Function: Checks if a vehicle is occupied by a driver or passengers.
    @param vehicle The vehicle entity handle.
    @return boolean True if occupied, false otherwise.
]]
function IsVehicleOccupied(vehicle)
    if not DoesEntityExist(vehicle) then return false end -- Safety check
    if not IsVehicleSeatFree(vehicle, -1) then return true end -- Driver seat
    local maxPassengers = GetVehicleMaxNumberOfPassengers(vehicle)
    for i = 0, maxPassengers - 1 do
        if not IsVehicleSeatFree(vehicle, i) then return true end -- Passenger seat
    end
    return false
end

--[[
    Helper Function: Checks if a vehicle is considered abandoned based on LastDrive time and blacklist.
    @param vehicle The vehicle entity handle.
    @return boolean True if abandoned, false otherwise.
]]
function IsVehicleAbandoned(vehicle)
    if not DoesEntityExist(vehicle) then return false end

    local plate = GetVehicleNumberPlateText(vehicle)
    if not plate or not LastDrive[plate] then return false end -- No plate or not tracked

    local model = GetEntityModel(vehicle)
    local modelName = GetDisplayNameFromVehicleModel(model)
    if type(modelName) ~= "string" then return false end
    if Config.BlackListVehicles[modelName:lower()] then return false end -- Blacklisted

    local timeSinceLeft = GetGameTimer() - LastDrive[plate]
    local abandonTime = Config.LastDrive * 60 * 1000
    return timeSinceLeft >= abandonTime
end

--[[
    Exported Function: Opens the ESX Menu with a list of nearby abandoned vehicles.
]]
function AbandonedVehiclesMenu()
    if ESX == nil then -- Ensure ESX is ready before opening menu
        print("[nex-abandoned] Error: ESX object not ready for AbandonedVehiclesMenu.")
        ESX.ShowNotification("حدث خطأ في تحميل القائمة، حاول مرة أخرى.")
        return
    end

    local elements = {}
    local allVehicles = GetGamePool('CVehicle')
    local playerPed = PlayerPedId()
    local pedcoords = GetEntityCoords(playerPed)

    for _, vehicle in ipairs(allVehicles) do
        if DoesEntityExist(vehicle) then
            if not IsVehicleOccupied(vehicle) and IsVehicleAbandoned(vehicle) and not IsEntityPositionFrozen(vehicle) then
                local plate = GetVehicleNumberPlateText(vehicle)
                local vehcoords = GetEntityCoords(vehicle)
                if vehcoords then
                    local dist = Vdist(pedcoords.x, pedcoords.y, pedcoords.z, vehcoords.x, vehcoords.y, vehcoords.z)
                    local label = string.format(
                        'اللوحة: <span style="color:#3d7de5;">%s</span> | المسافة: <span style="color:#3d7de5;">%.1fم</span>',
                        plate or "لا يوجد", dist
                    )
                    table.insert(elements, { label = label, value = vehicle, vehcoords = vehcoords })
                end
            end
        end
    end

    table.sort(elements, function(a, b)
        if not a.vehcoords or not b.vehcoords then return false end
        local distA = Vdist(pedcoords.x, pedcoords.y, pedcoords.z, a.vehcoords.x, a.vehcoords.y, a.vehcoords.z)
        local distB = Vdist(pedcoords.x, pedcoords.y, pedcoords.z, b.vehcoords.x, b.vehcoords.y, b.vehcoords.z)
        return distA < distB
    end)

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'abandoned_vehicles_list',
    {
        title    = "المركبات المهجورة القريبة",
        align    = 'top-left',
        elements = elements
    },
    function(data, menu)
        if data and data.current and data.current.vehcoords then
            SetNewWaypoint(data.current.vehcoords.x, data.current.vehcoords.y)
            ESX.ShowNotification("تم تحديد نقطة للمركبة المحددة.")
        else
             ESX.ShowNotification("خطأ: لا يمكن تحديد موقع المركبة.")
        end
        menu.close()
    end,
    function(data, menu)
        menu.close()
    end)
end
-- Export the function so other resources can call it
exports('AbandonedVehiclesMenu', AbandonedVehiclesMenu)


--[[ =================================================================
    ZAHYA SCAN/DELETE LOGIC (Client Handlers)
   ================================================================= ]]

--[[ Event: zahya:requestAbandonedVehicles ]]
RegisterNetEvent('zahya:requestAbandonedVehicles')
AddEventHandler('zahya:requestAbandonedVehicles', function()
    if Config.Debug then print("[nex-abandoned] Received request, triggering scan for abandoned vehicles.") end
    TriggerEvent('zahya:scanAbandonedVehicles')
end)

--[[ Event: zahya:scanAbandonedVehicles ]]
RegisterNetEvent('zahya:scanAbandonedVehicles')
AddEventHandler('zahya:scanAbandonedVehicles', function()
    if Config.Debug then print("[nex-abandoned] Scanning for abandoned vehicles to delete...") end
    local abandonedVehiclesNetIds = {}
    local allVehicles = GetGamePool('CVehicle')

    for _, vehicle in ipairs(allVehicles) do
        if DoesEntityExist(vehicle) then
            if not IsVehicleOccupied(vehicle) and IsVehicleAbandoned(vehicle) then
                local netId = NetworkGetNetworkIdFromEntity(vehicle)
                if netId and netId ~= 0 and NetworkDoesNetworkIdExist(netId) then
                    table.insert(abandonedVehiclesNetIds, netId)
                end
            end
        end
    end

    local deleteCount = #abandonedVehiclesNetIds
    if Config.Debug then print("[nex-abandoned] Found " .. deleteCount .. " abandoned vehicles matching criteria for deletion.") end

    if deleteCount > 0 then
        ESX.ShowNotification("بدء حذف " .. deleteCount .. " مركبة مهجورة...")
        Citizen.CreateThread(function()
            for i, netId in ipairs(abandonedVehiclesNetIds) do
                if Config.Debug then print("[nex-abandoned] Attempting to delete vehicle " .. i .. "/" .. deleteCount .. " (NetID: " .. netId .. ")") end
                -- Trigger the delete event (which handles network control etc.)
                TriggerEvent('zahya:deleteVehicle', netId)
                Citizen.Wait(250) -- Wait between delete requests
            end
            ESX.ShowNotification("اكتمل حذف المركبات المهجورة.")
            if Config.Debug then print("[nex-abandoned] Finished deletion process.") end
        end)
    else
         ESX.ShowNotification("لم يتم العثور على مركبات مهجورة للحذف.")
    end
end)

--[[ Event: zahya:deleteVehicle ]]
RegisterNetEvent('zahya:deleteVehicle')
AddEventHandler('zahya:deleteVehicle', function(netId)
    if not netId or netId == 0 or not NetworkDoesNetworkIdExist(netId) then
        if Config.Debug then print("[nex-abandoned] Received zahya:deleteVehicle for invalid NetID: " .. tostring(netId)) end
        return
    end

    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if DoesEntityExist(vehicle) then
        if Config.Debug then print("[nex-abandoned] Attempting deletion for NetID: " .. netId) end
        NetworkRequestControlOfEntity(vehicle)
        local attempts = 0
        Citizen.CreateThread(function()
            while not NetworkHasControlOfEntity(vehicle) and attempts < 20 do
                Citizen.Wait(50)
                NetworkRequestControlOfEntity(vehicle)
                attempts = attempts + 1
            end

            if NetworkHasControlOfEntity(vehicle) then
                if Config.Debug then print("[nex-abandoned] Gained control. Deleting NetID: " .. netId) end
                SetEntityAsMissionEntity(vehicle, true, true)
                DeleteEntity(vehicle)
            else
                print("[nex-abandoned] Failed to gain control after " .. attempts .. " attempts for NetID: " .. netId .. ". Cannot delete.")
            end
        end)
    else
        if Config.Debug then print("[nex-abandoned] Received zahya:deleteVehicle, but entity for NetID " .. netId .. " does not exist.") end
    end
end)


-- Print load message once ESX is likely ready
Citizen.CreateThread(function()
    while ESX == nil do Citizen.Wait(500) end -- Wait for ESX one last time
    print("----------------------------------------")
    print("[nex-abandoned] Client Script Loaded & Initialized")
    print("----------------------------------------")
end)

