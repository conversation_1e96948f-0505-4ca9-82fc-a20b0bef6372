Rip = {
	MaxSpeed = 25,
	DisabledKeys = { -- here you can choose what Keys you want to Disable -- هنا يمكنك اختيار المفاتيح التي تريد اطفائها
        {Index = 288, types = 0}, --(F1)
        {Index = 289, types = 0}, --(F2)
        {Index = 170, types = 0}, --(F3)
		{Index = 166, types = 0}, --(F5)
		{Index = 167, types = 0}, --(F6)
		{Index = 168, types = 0}, --(F7)
		{Index = 56, types = 0}, --(F9)
		{Index = 57, types = 0}, --(F10)
		{Index = 44, types = 0}, --(Q)
		{Index = 45, types = 0}, --(R)
		{Index = 245, types = 0}, --(T)
		{Index = 47, types = 0}, --(G)
		{Index = 74, types = 0}, --(H)
		{Index = 20, types = 0}, --(Z)
		{Index = 29, types = 0}, --(B)
		{Index = 244, types = 0}, --(M)
		{Index = 37, types = 0}, --(TAB)
		{Index = 137, types = 0}, --(CAPS)
		{Index = 21, types = 0}, --(LEFTSHIFT)
		{Index = 36, types = 0}, --(LEFTCTRL)
		{Index = 24, types = 0}, --(LEFTMOUSEBUTTON)
		{Index = 22, types = 0} --(SPACE)
    }
}

local myZonesInfo = {}
local myLocationZonesInfo = {}
local myLastZone = nil
local hasScreenEffect = false
local MaxSpeedEnabled = false

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	PlayerData = ESX.GetPlayerData()
	ESX.TriggerServerCallback('zahya_police_safe_zone:GetZonesInfo', function(ZonesInfo, ZonesInfo2)
		myZonesInfo = ZonesInfo
		myLocationZonesInfo = ZonesInfo2
	end)
end)

RegisterNetEvent('zahya_police_safe_zone:CL:RemoveZones')
AddEventHandler('zahya_police_safe_zone:CL:RemoveZones', function(ZoneCoords,ZoneName)
	if ifZone(ZoneCoords,ZoneName) then
		TriggerServerEvent("zahya_police_safe_zone:SV:RemoveZone", ZoneCoords)
	elseif ifInAnyZone(ZoneName) then
		TriggerServerEvent("zahya_police_safe_zone:SV:RemoveZone", ifInAnyZone(ZoneName))
	end
end)

RegisterNetEvent('zahya_police_safe_zone:UpdateZonesInfo')
AddEventHandler('zahya_police_safe_zone:UpdateZonesInfo', function(ZonesInfo, ZoneCoords, ZoneDistance)
	myZonesInfo = ZonesInfo
	local ped = PlayerPedId()
	local targetCoords = GetEntityCoords(ped)
	if ZoneDistance ~= nil then
		local targetdistance = GetDistanceBetweenCoords(targetCoords, ZoneCoords, true)
		if targetdistance <= ZoneDistance then
			myLastZone = ZoneCoords
		end
	end
end)

RegisterNetEvent('zahya_police_safe_zone:RemoveOldZone')
AddEventHandler('zahya_police_safe_zone:RemoveOldZone', function(ZoneCoords)
	for k,v in pairs(myLocationZonesInfo) do
		if v["TableZoneCoords"] == ZoneCoords then
			table.remove(myLocationZonesInfo,k)
		end
	end
end)

RegisterNetEvent('zahya_police_safe_zone:addNewZone')
AddEventHandler('zahya_police_safe_zone:addNewZone', function(ZoneCoords, ZoneDistance,ZoneName)
	table.insert(myLocationZonesInfo, { ["TableZoneCoords"] = ZoneCoords, ["TableZoneDistance"] = ZoneDistance,["TableZoneName"] = ZoneName })
	local ped = PlayerPedId()
	local targetCoords = GetEntityCoords(ped)
	local targetdistance = GetDistanceBetweenCoords(targetCoords, ZoneCoords, true)
	if targetdistance <= ZoneDistance then
		myLastZone = ZoneCoords
	end
end)

RegisterNetEvent('zahya_police_safe_zone:UpdateStatsInfo')
AddEventHandler('zahya_police_safe_zone:UpdateStatsInfo', function()
	hasScreenEffect = false
	MaxSpeedEnabled = false
	ClearTimecycleModifier()
	local ped = PlayerPedId()
	EnableAllControlActions(0)
	if IsPedInAnyVehicle(ped,true) then
		local Vehicle = GetVehiclePedIsIn(ped,false)

        SetEntityMaxSpeed(Vehicle,GetVehicleHandlingFloat(Vehicle,"CHandlingData","fInitialDriveMaxFlatVel"))
	end
end)

RegisterNetEvent('zahya_police_safe_zone:TogglePanicButton')
AddEventHandler('zahya_police_safe_zone:TogglePanicButton', function(ZoneCoords, ZoneName, ZoneDistance)
	if ifInAnyMyZone(ZoneName) then

		TriggerServerEvent("zahya_police_safe_zone:SV:RemoveLocationZone", ifInAnyMyZone(ZoneName), ZoneName)
		TriggerServerEvent("esx_misc:TogglePanicButton", ZoneName, ifInAnyMyZone(ZoneName), true)
	else
		TriggerServerEvent("zahya_police_safe_zone:SV:AddZone", ZoneCoords, ZoneName, ZoneDistance)
		TriggerServerEvent("esx_misc:TogglePanicButton", ZoneName, ZoneCoords)
	end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
end)


Citizen.CreateThread(function()
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(200)
	end
	while true do
		local sleep = 1
		local ped = PlayerPedId()
		local Vehicle = GetVehiclePedIsIn(ped,false)
		local ZCoords = IAZN()
		if not ZCoords then
			sleep = 3000
		end
		if PlayerData.job.name == 'police' or PlayerData.job.name == 'ambulance' or PlayerData.job.name == 'admin' or PlayerData.job.name == 'agent' or PlayerData.job.name == 'mechanic' then
			if hasScreenEffect then
				ClearTimecycleModifier()
				hasScreenEffect = false
			end
			if MaxSpeedEnabled then
				if Vehicle == 0 then
					MaxSpeedEnabled = false
				else
					SetEntityMaxSpeed(Vehicle,GetVehicleHandlingFloat(Vehicle,"CHandlingData","fInitialDriveMaxFlatVel"))
					MaxSpeedEnabled = false
				end
			end
		else
			if ZCoords == false then
				myLastZone = nil
				if hasScreenEffect then
					ClearTimecycleModifier()
					hasScreenEffect = false
				end
				if MaxSpeedEnabled then
					if Vehicle == 0 then
						MaxSpeedEnabled = false
					else
						SetEntityMaxSpeed(Vehicle,GetVehicleHandlingFloat(Vehicle,"CHandlingData","fInitialDriveMaxFlatVel"))
						MaxSpeedEnabled = false
					end
				end
			else
				if myLastZone == ZCoords then
				else
					SetCurrentPedWeapon(PlayerPedId(),GetHashKey("WEAPON_UNARMED"),true)
					SetTimecycleModifier('damage')
					for i, r in pairs(Rip.DisabledKeys) do
						DisableControlAction(r['types'], r['Index'], true)
					end
					hasScreenEffect = true
					if Vehicle == 0 then
						MaxSpeedEnabled = false
					else
						SetEntityMaxSpeed(Vehicle,(Rip.MaxSpeed/2.236936))
						MaxSpeedEnabled = true
					end
				end
			end
		end
		Citizen.Wait(sleep)
	end
end)

function ifInAnyZone(ZoneName)
	if myZonesInfo ~= nil then
		for k,v in pairs(myZonesInfo) do
			local ped = PlayerPedId()
			local targetCoords = GetEntityCoords(ped)
			local targetdistance = GetDistanceBetweenCoords(targetCoords, v["TableZoneCoords"], true)
			if targetdistance <= v["TableZoneDistance"] then
				if ZoneName then
					if ZoneName == v["TableZoneName"] then
						return v["TableZoneCoords"]
					end
				else
					return v["TableZoneCoords"]
				end
			end
		end
		return false
	else
		return false
	end
end

function ifInAnyMyZone(ZoneName)
	if myLocationZonesInfo ~= nil then
		local ped = PlayerPedId()
		local targetCoords = GetEntityCoords(ped)
		for k,v in pairs(myLocationZonesInfo) do
			local targetdistance = GetDistanceBetweenCoords(targetCoords, v["TableZoneCoords"], true)
			if targetdistance <= v["TableZoneDistance"] then
				if ZoneName then
					if ZoneName == v["TableZoneName"] then
						return v["TableZoneCoords"]
					end
				else
					return v["TableZoneCoords"]
				end
			end
		end
		return false
	else
		return false
	end
end

function IAZN()
	if ifInAnyMyZone() then
		return ifInAnyMyZone()
	elseif ifInAnyZone() then
		return ifInAnyZone()
	else
		return false
	end
end

function ifZone(ZoneCoords,ZoneName)
	if myZonesInfo ~= nil then
		for k,v in pairs(myZonesInfo) do
			if ZoneCoords == v["TableZoneCoords"] then
				if ZoneName == v["TableZoneName"] then 
					return v["TableZoneCoords"]
				end
			end
		end
		return false
	else
		return false
	end
end
