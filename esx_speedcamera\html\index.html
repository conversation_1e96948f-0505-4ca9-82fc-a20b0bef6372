<html>

<style>
.speedcamera {
  display: none;
}
   
body {
  width: 100%;
  height: 100%;
  border: none;
  margin: 0px;
  overflow: hidden;
}

img {
  width: auto;
  height: auto;
  min-width: 100%;
  min-height: 100%;
} 
</style>

<head>
    <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
</head>

<body>
	<div class="speedcamera">
		<img src="https://www.macmillandictionary.com/external/slideshow/full/White_full.png">
	</div>
</body>
</html>

<script type="text/javascript">
	$(function() {
		window.addEventListener('message', function(event) {

			if (event.data.type == "openSpeedcamera"){
                $('.speedcamera').css('display', 'block')
				
			} else if (event.data.type == "closeSpeedcamera"){
                $('.speedcamera').css('display', 'none')
			} 
			
		});
	});
</script>