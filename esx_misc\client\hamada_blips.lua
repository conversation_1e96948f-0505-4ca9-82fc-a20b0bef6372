Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	ESX.PlayerData = ESX.GetPlayerData()
end)

---------Blips By hamada---------
RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    ESX.PlayerData.job = job
    if DutyBlips then
        for _, v in pairs(DutyBlips) do
            RemoveBlip(v)
        end
    end
    DutyBlips = {}
    if DutyAmbulanceBlips then
        for _, v in pairs(DutyAmbulanceBlips) do
            RemoveBlip(v)
        end
    end
    DutyAmbulanceBlips = {}
end)
------police and agent
local function CreateDutyBlips(playerId, playerLabel, playerJob, playerLocation, serverId)
    local ped = GetPlayerPed(playerId)
    local blip = GetBlipFromEntity(ped)
    if not DoesBlipExist(blip) then
        if NetworkIsPlayerActive(playerId) then
            blip = AddBlipForEntity(ped)
        else
            blip = AddBlipForCoord(playerLocation.x, playerLocation.y, playerLocation.z)
        end
		SetBlipCategory(blip, 10)
        SetBlipSprite(blip, 1)
        ShowHeadingIndicatorOnBlip(blip, true)
        SetBlipRotation(blip, math.ceil(playerLocation.w))
        SetBlipScale(blip, 1.0)
        if playerJob == 'police' then
            SetBlipColour(blip, 29)
        elseif playerJob == 'agent' then
            SetBlipColour(blip, 15)
        end
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('<FONT style="sharlock">'..playerLabel.." ID : </FONT>"..serverId)
        EndTextCommandSetBlipName(blip)
        DutyBlips[#DutyBlips+1] = blip
    end

    if GetBlipFromEntity(PlayerPedId()) == blip then
        RemoveBlip(blip)
    end
end


------ambulance and mechanic
local function CreateDutyAmbulanceBlips(playerId, playerLabel, playerJob, playerLocation)
    local ped = GetPlayerPed(playerId)
    local blip = GetBlipFromEntity(ped)
    if not DoesBlipExist(blip) then
        if NetworkIsPlayerActive(playerId) then
            blip = AddBlipForEntity(ped)
        else
            blip = AddBlipForCoord(playerLocation.x, playerLocation.y, playerLocation.z)
        end
		SetBlipCategory(blip, 10)
        SetBlipSprite(blip, 1)
        ShowHeadingIndicatorOnBlip(blip, true)
        SetBlipRotation(blip, math.ceil(playerLocation.w))
        SetBlipScale(blip, 1.0)
        if playerJob == 'ambulance' then
            SetBlipColour(blip, 17)
        elseif playerJob == 'mechanic' then
            SetBlipColour(blip, 22)
        end
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('<FONT style="sharlock">'..playerLabel.."</FONT>")
        EndTextCommandSetBlipName(blip)
        DutyAmbulanceBlips[#DutyAmbulanceBlips+1] = blip
    end

    if GetBlipFromEntity(PlayerPedId()) == blip then
        RemoveBlip(blip)
    end
end

RegisterNetEvent('hamada:client:UpdateBlips', function(players, players2)
    if ESX and ESX.PlayerData and ESX.PlayerData.job and (ESX.PlayerData.job.name == 'police' or ESX.PlayerData.job.name == 'agent' or ESX.PlayerData.job.name == 'admin') then
        if DutyBlips then
            for _, v in pairs(DutyBlips) do
                RemoveBlip(v)
            end
        end
        DutyBlips = {}
        if players then
            for _, data in pairs(players) do
                local id = GetPlayerFromServerId(data.source)
                CreateDutyBlips(id, data.job, data.job, data.location, data.source)

            end
        end
    end
    if DutyAmbulanceBlips then
        for _, v in pairs(DutyAmbulanceBlips) do
            RemoveBlip(v)
        end
    end
    DutyAmbulanceBlips = {}
    if players2 then
        for _, data in pairs(players2) do
            local id = GetPlayerFromServerId(data.source)
            CreateDutyAmbulanceBlips(id, data.job, data.job, data.location, data.source)
        end
    end
end)
