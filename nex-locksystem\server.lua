ESX = exports["es_extended"]:getSharedObject()

local vehicles = {}

AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return
    end
    MySQL.Async.fetchAll('SELECT plate, owner FROM owned_vehicles', {}, function(results)
        for _, vehicle in ipairs(results) do  
            if vehicle.plate ~= nil then 
              vehicles[alltrim(vehicle.plate)] = vehicle.owner
            end
        end
    end)
end)

RegisterServerEvent("vehicle:unlock")
AddEventHandler("vehicle:unlock", function(netId)
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    local owner = NetworkGetEntityOwner(vehicle)
    TriggerClientEvent('vehicle:unlockClient', owner, netId)
end)



ESX.RegisterServerCallback('IsVehicleOwned', function(source, cb, plate)
    local xPlayer = ESX.GetPlayerFromId(source)
    local isOwned = IsPlateOwned(plate,xPlayer.identifier)
    cb(isOwned)
end)

function IsPlateOwned(plate,identifier)
    if plate == nil then return false end
    return vehicles[alltrim(plate)] ~= nil and vehicles[alltrim(plate)] == identifier
end


function alltrim(s)
    return s:match("^%s*(.-)%s*$")
end


AddEventHandler('nex:AddOwnerPlate')
RegisterNetEvent('nex:AddOwnerPlate',function (plate,identifier)
    local identifier = identifier or ESX.GetPlayerFromId(source).identifier
    vehicles[alltrim(plate)] = identifier
end)