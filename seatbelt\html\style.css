html {
	overflow: hidden;
}

body {
	margin: 0px;
	padding: 0px;
}

#container {
	position: absolute;
	/*top: 17vh;*/
	bottom: -100%;
	left: 70%; /*25*/
	width: 80px;
	height: 80px;
	display: none;
	flex-direction: column;
}

header {
	width: 80px;
	height: 80px;
	/*background-color: #a519ea;*/
	opacity: 0.8;
	border-top-left-radius: 40px;
	border-top-right-radius: 40px;

}

header img {
	width: 80px;
	height: 80px;
	border-top-left-radius: 40px;
	border-top-right-radius: 40px;
}

/* Firefox old*/
@-moz-keyframes blink {
    0% {
        opacity:1;
    }
    50% {
        opacity:0;
    }
    100% {
        opacity:1;
    }
} 

@-webkit-keyframes blink {
    0% {
        opacity:1;
    }
    50% {
        opacity:0;
    }
    100% {
        opacity:1;
    }
}
/* IE */
@-ms-keyframes blink {
    0% {
        opacity:1;
    }
    50% {
        opacity:0;
    }
    100% {
        opacity:1;
    }
} 
/* Opera and prob css3 final iteration */
@keyframes blink {
    0% {
        opacity:1;
    }
    50% {
        opacity:0;
    }
    100% {
        opacity:1;
    }
} 
.blink-image {
    -moz-animation: blink normal 1s infinite ease-in-out; /* Firefox */
    -webkit-animation: blink normal 1s infinite ease-in-out; /* Webkit */
    -ms-animation: blink normal 1s infinite ease-in-out; /* IE */
    animation: blink normal 1s infinite ease-in-out; /* Opera and prob css3 final iteration */
}