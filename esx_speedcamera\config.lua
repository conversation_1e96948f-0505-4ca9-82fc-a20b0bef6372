Config = {}

Config.DelayBeforeCaughtAgain = 60 * 1000

Config.Fine = {
	Zone60 = {
	no1 = 50,
	no2 = 75,
	no3 = 100,
	no4 = 125,
	no5 = 150,
	},
	Zone80 = {
	no1 = 50,
	no2 = 100,
	no3 = 150,
	no4 = 200,
	no5 = 250,
	},
	Zone120 = {
	no1 = 50,
	no2 = 100,
	no3 = 150,
	no4 = 200,
	no5 = 250,
	no6 = 500,
	no7 = 1000,
	no8 = 3000,
	}
}

Config.Detector = {
	volume  	= 0.7,
	timeout 	= 5000,
	range60 	= 90.0,	
	range80 	= 100.0,	
	range120 	= 150.0,
	soundfile 	= "speedcamera.wav",
}

Config.ExtraSpeed = 15.0