-- Skrypt od strony Serwera

RegisterNetEvent('esx_misc:startAreszt')
AddEventHandler('esx_misc:startAreszt', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)

	TriggerClientEvent('esx_misc:aresztowany', targetPlayer.source, source)
	TriggerClientEvent('esx_misc:aresztuj', source)
end)

RegisterNetEvent('esx_misc:unhandcuff')
AddEventHandler('esx_misc:unhandcuff', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)
	
	TriggerClientEvent('esx_misc:unrestrain', targetPlayer.source, source)
	--TriggerClientEvent('esx_misc:aresztuj', source)
end)