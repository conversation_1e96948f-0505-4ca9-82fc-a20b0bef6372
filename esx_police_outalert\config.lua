framework = "esx" -- false   -    "esx"    -   "qb"
testCommand = true -- enable test command
dispatchKey = "z" -- Dispatch
dispatchHeadingKey = "y" -- Dispatch [Heading to The Police Call]
dispatchMarkKey = "e" -- Dispatch [Mark On Map]

leftClickControlCD = 2000 --cooldown each time a weapon is fired, script does not send fire notification when on cooldown
gunFiredNotifCD = 1500 --time to send notification again after weapon firing notification for player
-- edit client.lua line 453 for more detailed editing

jobs = {
    "police",
    "agent",
    "admin",
}
