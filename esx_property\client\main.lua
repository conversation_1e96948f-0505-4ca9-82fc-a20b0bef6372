local GUI = {}
GUI.Time = 0
local OwnedProperties, Blips, CurrentActionData = {}, {}, {}
local CurrentProperty, CurrentPropertyOwner, LastProperty, LastPart, CurrentAction, CurrentActionMsg
local firstSpawn, hasChest, hasAlreadyEnteredMarker = true, false, false


RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    ESX.TriggerServerCallback('esx_property:getProperties', function(properties)
        Config.Properties = properties
        CreateBlips()
    end)
    
    ESX.TriggerServerCallback('esx_property:getOwnedProperties', function(result)
        for k, v in ipairs(result) do
            SetPropertyOwned(v.name, true, v.rented)
        end
    end)
end)

-- only used when script is restarting mid-session
RegisterNetEvent('esx_property:sendProperties')
AddEventHandler('esx_property:sendProperties', function(properties)
    Config.Properties = properties
    CreateBlips()
    
    ESX.TriggerServerCallback('esx_property:getOwnedProperties', function(result)
        for k, v in ipairs(result) do
            SetPropertyOwned(v.name, true, v.rented)
        end
    end)
end)

function DrawSub(text, time)
    ClearPrints()
    BeginTextCommandPrint('STRING')
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandPrint(time, 1)
end

function CreateBlips()
    for i = 1, #Config.Properties, 1 do
        local property = Config.Properties[i]
        
        if property.entering then
            Blips[property.name] = AddBlipForCoord(property.entering.x, property.entering.y, property.entering.z)
            
            SetBlipSprite(Blips[property.name], 369)
            SetBlipDisplay(Blips[property.name], 4)
            SetBlipScale(Blips[property.name], 1.0)
            SetBlipAsShortRange(Blips[property.name], true)
            
            BeginTextCommandSetBlipName("STRING")
            --AddTextComponentString(_U('free_prop'))
            AddTextComponentString("<FONT FACE='A9eelsh'>ﻊﻴﺒﻠﻟ ﺭﺎﻘﻋ")
            EndTextCommandSetBlipName(Blips[property.name])
        end
    end
end

function GetProperties()
    return Config.Properties
end

function GetProperty(name)
    for i = 1, #Config.Properties, 1 do
        if Config.Properties[i].name == name then
            return Config.Properties[i]
        end
    end
end

function GetGateway(property)
    for i = 1, #Config.Properties, 1 do
        local property2 = Config.Properties[i]
        
        if property2.isGateway and property2.name == property.gateway then
            return property2
        end
    end
end

function GetGatewayProperties(property)
    local properties = {}
    
    for i = 1, #Config.Properties, 1 do
        if Config.Properties[i].gateway == property.name then
            table.insert(properties, Config.Properties[i])
        end
    end
    
    return properties
end

function EnterProperty(name, owner)
    local property = GetProperty(name)
    local playerPed = PlayerPedId()
    CurrentProperty = property
    CurrentPropertyOwner = owner
    
    for i = 1, #Config.Properties, 1 do
        if Config.Properties[i].name ~= name then
            Config.Properties[i].disabled = true
        end
    end
    
    TriggerServerEvent('esx_property:saveLastProperty', name)
    
    Citizen.CreateThread(function()
        DoScreenFadeOut(800)
        
        while not IsScreenFadedOut() do
            Citizen.Wait(0)
        end
        
        for i = 1, #property.ipls, 1 do
            RequestIpl(property.ipls[i])
            
            while not IsIplActive(property.ipls[i]) do
                Citizen.Wait(0)
            end
        end
        
        SetEntityCoords(playerPed, property.inside.x, property.inside.y, property.inside.z)
        DoScreenFadeIn(800)
        DrawSub(property.label, 5000)
    end)

end

function ExitProperty(name)
    local property = GetProperty(name)
    local playerPed = PlayerPedId()
    local outside = nil
    CurrentProperty = nil
    
    if property.isSingle then
        outside = property.outside
    else
        outside = GetGateway(property).outside
    end
    
    TriggerServerEvent('esx_property:deleteLastProperty')
    
    Citizen.CreateThread(function()
        DoScreenFadeOut(800)
        
        while not IsScreenFadedOut() do
            Citizen.Wait(0)
        end
        
        SetEntityCoords(playerPed, outside.x, outside.y, outside.z)
        
        for i = 1, #property.ipls, 1 do
            RemoveIpl(property.ipls[i])
        end
        
        for i = 1, #Config.Properties, 1 do
            Config.Properties[i].disabled = false
        end
        
        DoScreenFadeIn(800)
    end)
end

function SetPropertyOwned(name, owned, rented)
    local property = GetProperty(name)
    local entering = nil
    local enteringName = nil
    
    if property.isSingle then
        entering = property.entering
        enteringName = property.name
    else
        local gateway = GetGateway(property)
        entering = gateway.entering
        enteringName = gateway.name
    end
    
    if owned then
        OwnedProperties[name] = rented
        RemoveBlip(Blips[enteringName])
        
        Blips[enteringName] = AddBlipForCoord(entering.x, entering.y, entering.z)
        SetBlipSprite(Blips[enteringName], 357)
        SetBlipAsShortRange(Blips[enteringName], true)
        
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("<FONT FACE='A9eelsh'>ﻚﻤﺳﺄﺑ ﻞﺠﺴﻣ ﺭﺎﻘﻋ")
        EndTextCommandSetBlipName(Blips[enteringName])
    else
        OwnedProperties[name] = nil
        local found = false
        
        for k, v in pairs(OwnedProperties) do
            local _property = GetProperty(k)
            local _gateway = GetGateway(_property)
            
            if _gateway then
                if _gateway.name == enteringName then
                    found = true
                    break
                end
            end
        end
        
        if not found then
            RemoveBlip(Blips[enteringName])
            
            Blips[enteringName] = AddBlipForCoord(entering.x, entering.y, entering.z)
            SetBlipSprite(Blips[enteringName], 369)
            SetBlipAsShortRange(Blips[enteringName], true)
            
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString("<FONT FACE='A9eelsh'>ﻊﻴﺒﻠﻟ ﺭﺎﻘﻋ")
            EndTextCommandSetBlipName(Blips[enteringName])
        end
    end
end

function PropertyIsOwned(property)
    return OwnedProperties[property.name] ~= nil
end

function OpenPropertyMenu(property)
    local elements = {}
    
    if PropertyIsOwned(property) then
        table.insert(elements, {label = _U('enter'), value = 'enter'})
        
        -- add move out
        if not Config.EnablePlayerManagement then
            local leaveLabel = _U('move_out')
            
            if not OwnedProperties[property.name] then
                leaveLabel = _U('move_out_sold', ESX.Math.GroupDigits(ESX.Math.Round(property.price / Config.SellModifier)))
            end
            
            table.insert(elements, {label = _U('leave') .. '<br><span  style="color:#FF0E0E;font-size:15">تنبيه: <span style="color:gray;">إلغاء الملكية بمعنى بيع المكان بدون مقابل', value = 'leave'})
        end
    else
        if not Config.EnablePlayerManagement then
            table.insert(elements, {label = _U('buy', ESX.Math.GroupDigits(property.price)), value = 'buy'})
        
        -- display rent price
        --local rent = ESX.Math.Round(property.price / Config.RentModifier)
        --table.insert(elements, {label = _U('rent', ESX.Math.GroupDigits(rent)), value = 'rent'})
        end
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'property', {
        title = property.label,
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        menu.close()
        
        if data.current.value == 'enter' then
            TriggerEvent('instance:create', 'property', {property = property.name, owner = ESX.GetPlayerData().identifier})
        elseif data.current.value == 'leave' then
            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'confirm', {
                title = 'هل أنت متأكد من إلغاء ملكية هاذه العقار بدون مقابل؟',
                align = 'bottom-right',
                elements = {
                    {label = _U('no'), value = 'no'},
                    {label = _U('yes'), value = 'yes'},
                }
            }, function(data3, menu3)
                if data3.current.value == 'yes' then
                    menu3.close()
                    TriggerServerEvent('esx_property:removeOwnedProperty', property.name)
                elseif data3.current.value == 'no' then
                    menu3.close()
                end
            end)
        elseif data.current.value == 'buy' then
            TriggerServerEvent('esx_property:buyProperty', property.name)
        elseif data.current.value == 'rent' then
            TriggerServerEvent('esx_property:rentProperty', property.name)
        end
    end, function(data, menu)
        menu.close()
        
        CurrentAction = 'property_menu'
        CurrentActionMsg = _U('press_to_menu')
        CurrentActionData = {property = property}
    end)
end

function OpenGatewayMenu(property)
    if Config.EnablePlayerManagement then
        OpenGatewayOwnedPropertiesMenu(gatewayProperties)
    else
        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'gateway', {
            title = property.name,
            align = 'top-left',
            elements = {
                {label = _U('owned_properties'), value = 'owned_properties'},
                {label = _U('available_properties'), value = 'available_properties'}
            }}, function(data, menu)
            if data.current.value == 'owned_properties' then
                OpenGatewayOwnedPropertiesMenu(property)
            elseif data.current.value == 'available_properties' then
                OpenGatewayAvailablePropertiesMenu(property)
            end
            end, function(data, menu)
                menu.close()
                
                CurrentAction = 'gateway_menu'
                CurrentActionMsg = _U('press_to_menu')
                CurrentActionData = {property = property}
            end)
    end
end

function OpenGatewayOwnedPropertiesMenu(property)
    local gatewayProperties = GetGatewayProperties(property)
    local elements = {}
    
    for i = 1, #gatewayProperties, 1 do
        if PropertyIsOwned(gatewayProperties[i]) then
            table.insert(elements, {
                label = gatewayProperties[i].label,
                value = gatewayProperties[i].name
            })
        end
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'gateway_owned_properties', {
        title = property.name .. ' - ' .. _U('owned_properties'),
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        menu.close()
        local elements = {{label = _U('enter'), value = 'enter'}}
        
        if not Config.EnablePlayerManagement then
            table.insert(elements, {label = _U('leave'), value = 'leave'})
        end
        
        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'gateway_owned_properties_actions', {
            title = data.current.label,
            align = 'top-left',
            elements = elements
        }, function(data2, menu2)
            menu2.close()
            
            if data2.current.value == 'enter' then
                TriggerEvent('instance:create', 'property', {property = data.current.value, owner = ESX.GetPlayerData().identifier})
                ESX.UI.Menu.CloseAll()
            elseif data2.current.value == 'leave' then
                TriggerServerEvent('esx_property:removeOwnedProperty', data.current.value)
            end
        end, function(data2, menu2)
            menu2.close()
        end)
    end, function(data, menu)
        menu.close()
    end)
end

function OpenGatewayAvailablePropertiesMenu(property)
    local gatewayProperties = GetGatewayProperties(property)
    local elements = {}
    
    for i = 1, #gatewayProperties, 1 do
        if not PropertyIsOwned(gatewayProperties[i]) then
            table.insert(elements, {
                label = gatewayProperties[i].label,
                value = gatewayProperties[i].name,
                buyPrice = gatewayProperties[i].price,
                rentPrice = ESX.Math.Round(gatewayProperties[i].price / Config.RentModifier)
            })
        end
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'gateway_available_properties', {
        title = property.name .. ' - ' .. _U('available_properties'),
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'gateway_available_properties_actions', {
            title = property.label .. ' - ' .. _U('available_properties'),
            align = 'top-left',
            elements = {
                {label = _U('buy', ESX.Math.GroupDigits(data.current.buyPrice)), value = 'buy'},
            --{label = _U('rent', ESX.Math.GroupDigits(data.current.rentPrice)), value = 'rent'}
            }}, function(data2, menu2)
            menu.close()
            menu2.close()
            
            if data2.current.value == 'buy' then
                TriggerServerEvent('esx_property:buyProperty', data.current.value)
            elseif data2.current.value == 'rent' then
                TriggerServerEvent('esx_property:rentProperty', data.current.value)
            end
            end, function(data2, menu2)
                menu2.close()
            end)
    end, function(data, menu)
        menu.close()
    end)
end

function OpenRoomMenu(property, owner)
    local entering = nil
    local elements = {{label = _U('invite_player'), value = 'invite_player'}}
    
    if property.isSingle then
        entering = property.entering
    else
        entering = GetGateway(property).entering
    end
    
    if CurrentPropertyOwner == owner then
        table.insert(elements, {label = _U('player_clothes'), value = 'player_dressing'})
        table.insert(elements, {label = _U('remove_cloth'), value = 'remove_cloth'})
    end
    
    table.insert(elements, {label = _U('remove_object'), value = 'room_inventory'})
    table.insert(elements, {label = _U('deposit_object'), value = 'player_inventory'})
    
    ESX.UI.Menu.CloseAll()
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'room', {
        title = property.label,
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        
        if data.current.value == 'invite_player' then
            
            local playersInArea = ESX.Game.GetPlayersInArea(entering, 10.0)
            local elements = {}
            
            for i = 1, #playersInArea, 1 do
                if playersInArea[i] ~= PlayerId() then
                    table.insert(elements, {label = GetPlayerName(playersInArea[i]), value = playersInArea[i]})
                end
            end
            
            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'room_invite', {
                title = property.label .. ' - ' .. _U('invite'),
                align = 'top-left',
                elements = elements,
            }, function(data2, menu2)
                TriggerEvent('instance:invite', 'property', GetPlayerServerId(data2.current.value), {property = property.name, owner = owner})
                ESX.ShowNotification(_U('you_invited', GetPlayerName(data2.current.value)))
            end, function(data2, menu2)
                menu2.close()
            end)
        
        elseif data.current.value == 'player_dressing' then
            
            ESX.TriggerServerCallback('esx_property:getPlayerDressing', function(dressing)
                local elements = {}
                
                for i = 1, #dressing, 1 do
                    table.insert(elements, {
                        label = dressing[i],
                        value = i
                    })
                end
                
                ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'player_dressing', {
                    title = property.label .. ' - ' .. _U('player_clothes'),
                    align = 'top-left',
                    elements = elements
                }, function(data2, menu2)
                    TriggerEvent('skinchanger:getSkin', function(skin)
                        ESX.TriggerServerCallback('esx_property:getPlayerOutfit', function(clothes)
                            TriggerEvent('skinchanger:loadClothes', skin, clothes)
                            TriggerEvent('esx_skin:setLastSkin', skin)
                            
                            TriggerEvent('skinchanger:getSkin', function(skin)
                                TriggerServerEvent('esx_skin:save', skin)
                            end)
                        end, data2.current.value)
                    end)
                end, function(data2, menu2)
                    menu2.close()
                end)
            end)
        
        elseif data.current.value == 'remove_cloth' then
            
            ESX.TriggerServerCallback('esx_property:getPlayerDressing', function(dressing)
                local elements = {}
                
                for i = 1, #dressing, 1 do
                    table.insert(elements, {
                        label = dressing[i],
                        value = i
                    })
                end
                
                ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'remove_cloth', {
                    title = property.label .. ' - ' .. _U('remove_cloth'),
                    align = 'top-left',
                    elements = elements
                }, function(data2, menu2)
                    menu2.close()
                    TriggerServerEvent('esx_property:removeOutfit', data2.current.value)
                    ESX.ShowNotification(_U('removed_cloth'))
                end, function(data2, menu2)
                    menu2.close()
                end)
            end)
        
        elseif data.current.value == 'room_inventory' then
            OpenRoomInventoryMenu(property, owner)
        elseif data.current.value == 'player_inventory' then
            OpenPlayerInventoryMenu(property, owner)
        end
    
    end, function(data, menu)
        menu.close()
        
        CurrentAction = 'room_menu'
        CurrentActionMsg = _U('press_to_menu')
        CurrentActionData = {property = property, owner = owner}
    end)
end

function OpenRoomInventoryMenu(property, owner)
    ESX.TriggerServerCallback('esx_property:getPropertyInventory', function(inventory)
        local elements = {}
        print(json.encode(inventory, {indent = true}))
        
        if inventory.blackMoney > 0 then
            table.insert(elements, {
                label = ' أموال غير شرعية ' .. inventory.blackMoney .. '$',
                type = 'item_account',
                value = 'black_money'
            })
        end
        
        for i = 1, #inventory.items, 1 do
            local item = inventory.items[i]
            
            if item.count > 0 then
                table.insert(elements, {
                    label = item.label .. ' x' .. item.count,
                    type = 'item_standard',
                    value = item.name
                })
            end
        end
        
        for i = 1, #inventory.weapons, 1 do
            local weapon = inventory.weapons[i]
            weapon.count = weapon.count or 1
            table.insert(elements, {
                label = ESX.GetWeaponLabel(weapon.name) .. ' [عدد: ' .. weapon.count .. ']',
                type = 'item_weapon',
                value = weapon.name
            })
        end
        
        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'room_inventory', {
            title = property.label .. ' - ' .. _U('inventory'),
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            
            if data.current.type == 'item_account' then
                ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'get_account_count', {
                    title = _U('amount')
                }, function(data2, menu)
                    
                    local quantity = tonumber(data2.value)
                    if quantity == nil then
                        ESX.ShowNotification(_U('amount_invalid'))
                    else
                        menu.close()
                        local bartime = tonumber(quantity / 15)
                        local msg = '<font size=5 color=white>جاري التنفيذ'
                        TriggerEvent('pogressBar:drawBar', bartime, msg)
                        disableAllControlActions = true
                        Citizen.CreateThread(function()
                            while disableAllControlActions do
                                Citizen.Wait(0)
                                DisableAllControlActions(0)
                                EnableControlAction(0, 249, true)
                                EnableControlAction(0, 311, true)
                                EnableControlAction(0, 45, true)
                                EnableControlAction(0, 1, true)
                                EnableControlAction(0, 2, true)
                            end
                        end)
                        local playerPed = PlayerPedId()
                        FreezeEntityPosition(playerPed, true)
                        Wait(bartime)
                        FreezeEntityPosition(playerPed, false)
                        disableAllControlActions = false
                        TriggerServerEvent('esx_property:getItem', owner, data.current.type, data.current.value, quantity)
                        ESX.SetTimeout(300, function()
                            OpenRoomInventoryMenu(property, owner)
                        end)
                    end
                end, function(data2, menu)
                    menu.close()
                end)
            elseif data.current.type == 'item_weapon' then
                local weaponHash = GetHashKey(data.current.value) 
                local ped = PlayerPedId()  
            
                local _,maxAmmo = GetMaxAmmo(ped, weaponHash)            
              TriggerServerEvent('esx_property:getItem', owner, data.current.type, data.current.value, maxAmmo)
            
                ESX.SetTimeout(300, function()
                    OpenRoomInventoryMenu(property, owner)
                end)

            else

                ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'get_item_count', {
                    title = _U('amount')
                }, function(data2, menu)
                    
                    local quantity = tonumber(data2.value)
                    if quantity == nil then
                        ESX.ShowNotification(_U('amount_invalid'))
                    else
                        menu.close()
                        
                        TriggerServerEvent('esx_property:getItem', owner, data.current.type, data.current.value, quantity)
                        ESX.SetTimeout(300, function()
                            OpenRoomInventoryMenu(property, owner)
                        end)
                    end
                end, function(data2, menu)
                    menu.close()
                end)
            end
        end, function(data, menu)
            menu.close()
        end)
    end, owner)
end

function OpenPlayerInventoryMenu(property, owner)
    ESX.TriggerServerCallback('esx_property:getPlayerInventory', function(inventory)
        local elements = {}
        
        if inventory.blackMoney > 0 then
            table.insert(elements, {
                label = ' أموال غير شرعية ' .. inventory.blackMoney .. '$',
                type = 'item_account',
                value = 'black_money'
            })
        end
        
        for i = 1, #inventory.items, 1 do
            local item = inventory.items[i]
            if item.count > 0 then
                table.insert(elements, {
                    label = item.label .. ' x' .. item.count,
                    type = 'item_standard',
                    value = item.name
                })
            end
        end
        
        for i = 1, #inventory.weapons, 1 do
            local weapon = inventory.weapons[i]
            table.insert(elements, {
                label = weapon.label .. ' [طلق: ' .. weapon.ammo .. ']',
                type = 'item_weapon',
                value = weapon.name
            })
        end
        
        -- Open the inventory menu
        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'player_inventory', {
            title = property.label .. ' - ' .. _U('inventory'),
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            if data.current.type == 'item_weapon' then
                TriggerServerEvent('esx_property:putItem', owner, data.current.type, data.current.value, 1)
                ESX.SetTimeout(300, function()
                    OpenPlayerInventoryMenu(property, owner)
                end)
              --[[  ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'put_weapon_count', {
                    title = _U('amount')
                }, function(data2, menu2)
                    local ammoCount = tonumber(data2.value)
                    if ammoCount == nil then
                        ESX.ShowNotification(_U('amount_invalid'))
                    else
                        menu2.close()
                       
                    end
                end, function(data2, menu2)
                    menu2.close()
                end)]]--
            else
                ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'put_item_count', {
                    title = _U('amount')
                }, function(data2, menu2)
                    local quantity = tonumber(data2.value)
                    if quantity == nil then
                        ESX.ShowNotification(_U('amount_invalid'))
                    else
                        menu2.close()
                        TriggerServerEvent('esx_property:putItem', owner, data.current.type, data.current.value, tonumber(data2.value))
                        ESX.SetTimeout(300, function()
                            OpenPlayerInventoryMenu(property, owner)
                        end)
                    end
                end, function(data2, menu2)
                    menu2.close()
                end)
            end
        end, function(data, menu)
            menu.close()
        end)
    end)
end


AddEventHandler('instance:loaded', function()
    TriggerEvent('instance:registerType', 'property', function(instance)
        EnterProperty(instance.data.property, instance.data.owner)
    end, function(instance)
        ExitProperty(instance.data.property)
    end)
end)

AddEventHandler('esx:onPlayerSpawn', function()
    if firstSpawn then
        Citizen.CreateThread(function()
            while not ESX.IsPlayerLoaded() do
                Citizen.Wait(0)
            end
            
            ESX.TriggerServerCallback('esx_property:getLastProperty', function(propertyName)
                if propertyName then
                    if propertyName ~= '' then
                        local property = GetProperty(propertyName)
                        
                        for i = 1, #property.ipls, 1 do
                            RequestIpl(property.ipls[i])
                            
                            while not IsIplActive(property.ipls[i]) do
                                Citizen.Wait(0)
                            end
                        end
                        
                        TriggerEvent('instance:create', 'property', {property = propertyName, owner = ESX.GetPlayerData().identifier})
                    end
                end
            end)
        end)
        
        firstSpawn = false
    end
end)

AddEventHandler('esx_property:getProperties', function(cb)
    cb(GetProperties())
end)

AddEventHandler('esx_property:getProperty', function(name, cb)
    cb(GetProperty(name))
end)

AddEventHandler('esx_property:getGateway', function(property, cb)
    cb(GetGateway(property))
end)

RegisterNetEvent('esx_property:setPropertyOwned')
AddEventHandler('esx_property:setPropertyOwned', function(name, owned, rented)
    SetPropertyOwned(name, owned, rented)
end)

RegisterNetEvent('instance:onCreate')
AddEventHandler('instance:onCreate', function(instance)
    if instance.type == 'property' then
        TriggerEvent('instance:enter', instance)
    end
end)

RegisterNetEvent('instance:onEnter')
AddEventHandler('instance:onEnter', function(instance)
    if instance.type == 'property' then
        local property = GetProperty(instance.data.property)
        local isHost = GetPlayerFromServerId(instance.host) == PlayerId()
        local isOwned = false
        
        if PropertyIsOwned(property) == true then
            isOwned = true
        end
        
        if isOwned or not isHost then
            hasChest = true
        else
            hasChest = false
        end
    end
end)

RegisterNetEvent('instance:onPlayerLeft')
AddEventHandler('instance:onPlayerLeft', function(instance, player)
    if player == instance.host then
        TriggerEvent('instance:leave')
    end
end)

AddEventHandler('esx_property:hasEnteredMarker', function(name, part)
    local property = GetProperty(name)
    
    if part == 'entering' then
        if property.isSingle then
            CurrentAction = 'property_menu'
            CurrentActionMsg = _U('press_to_menu')
            CurrentActionData = {property = property}
        else
            CurrentAction = 'gateway_menu'
            CurrentActionMsg = _U('press_to_menu')
            CurrentActionData = {property = property}
        end
    elseif part == 'exit' then
        CurrentAction = 'room_exit'
        CurrentActionMsg = _U('press_to_exit')
        CurrentActionData = {propertyName = name}
    elseif part == 'roomMenu' then
        CurrentAction = 'room_menu'
        CurrentActionMsg = _U('press_to_menu')
        CurrentActionData = {property = property, owner = CurrentPropertyOwner}
    end
end)

AddEventHandler('esx_property:hasExitedMarker', function(name, part)
    ESX.UI.Menu.CloseAll()
    CurrentAction = nil
end)

-- Enter / Exit marker events & Draw markers
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        local coords = GetEntityCoords(PlayerPedId())
        local isInMarker, letSleep = false, true
        local currentProperty, currentPart
        
        for i = 1, #Config.Properties, 1 do
            local property = Config.Properties[i]
            
            -- Entering
            if property.entering and not property.disabled then
                local distance = GetDistanceBetweenCoords(coords, property.entering.x, property.entering.y, property.entering.z, true)
                
                if distance < Config.DrawDistance then
                    DrawMarker(Config.MarkerType, property.entering.x, property.entering.y, property.entering.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.MarkerSize.x, Config.MarkerSize.y, Config.MarkerSize.z, Config.MarkerColor.r, Config.MarkerColor.g, Config.MarkerColor.b, 100, false, true, 2, false, nil, nil, false)
                    if Config.MarkerText == true then
                        ESX.Game.Utils.DrawText3D(property.entering, property.name, 2)
                    end
                    letSleep = false
                end
                
                if distance < Config.MarkerSize.x then
                    isInMarker = true
                    currentProperty = property.name
                    currentPart = 'entering'
                end
            end
            
            -- Exit
            if property.exit and not property.disabled then
                local distance = GetDistanceBetweenCoords(coords, property.exit.x, property.exit.y, property.exit.z, true)
                
                if distance < Config.DrawDistance then
                    DrawMarker(Config.MarkerType, property.exit.x, property.exit.y, property.exit.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.MarkerSize.x, Config.MarkerSize.y, Config.MarkerSize.z, Config.MarkerColor.r, Config.MarkerColor.g, Config.MarkerColor.b, 100, false, true, 2, false, nil, nil, false)
                    letSleep = false
                end
                
                if distance < Config.MarkerSize.x then
                    isInMarker = true
                    currentProperty = property.name
                    currentPart = 'exit'
                end
            end
            
            -- Room menu
            if property.roomMenu and hasChest and not property.disabled then
                local distance = GetDistanceBetweenCoords(coords, property.roomMenu.x, property.roomMenu.y, property.roomMenu.z, true)
                
                if distance < Config.DrawDistance then
                    DrawMarker(Config.MarkerType, property.roomMenu.x, property.roomMenu.y, property.roomMenu.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.MarkerSize.x, Config.MarkerSize.y, Config.MarkerSize.z, Config.RoomMenuMarkerColor.r, Config.RoomMenuMarkerColor.g, Config.RoomMenuMarkerColor.b, 100, false, true, 2, false, nil, nil, false)
                    letSleep = false
                end
                
                if distance < Config.MarkerSize.x then
                    isInMarker = true
                    currentProperty = property.name
                    currentPart = 'roomMenu'
                end
            end
        end
        
        if isInMarker and not hasAlreadyEnteredMarker or (isInMarker and (LastProperty ~= currentProperty or LastPart ~= currentPart)) then
            hasAlreadyEnteredMarker = true
            LastProperty = currentProperty
            LastPart = currentPart
            
            TriggerEvent('esx_property:hasEnteredMarker', currentProperty, currentPart)
        end
        
        if not isInMarker and hasAlreadyEnteredMarker then
            hasAlreadyEnteredMarker = false
            TriggerEvent('esx_property:hasExitedMarker', LastProperty, LastPart)
        end
        
        if letSleep then
            Citizen.Wait(500)
        end
    end
end)

-- Key controls
--[[Citizen.CreateThread(function()
while true do
Citizen.Wait(0)

if CurrentAction then
ESX.ShowHelpNotification(CurrentActionMsg)

if IsControlJustReleased(0, 38) then
if CurrentAction == 'property_menu' then
OpenPropertyMenu(CurrentActionData.property)
elseif CurrentAction == 'gateway_menu' then
if Config.EnablePlayerManagement then
OpenGatewayOwnedPropertiesMenu(CurrentActionData.property)
else
OpenGatewayMenu(CurrentActionData.property)
end
elseif CurrentAction == 'room_menu' then
OpenRoomMenu(CurrentActionData.property, CurrentActionData.owner)
elseif CurrentAction == 'room_exit' then
TriggerEvent('instance:leave')
end

CurrentAction = nil
end
else
Citizen.Wait(500)
end
end
end)]]
-- Key controls
Citizen.CreateThread(function()
    while true do
        
        local wait = 1000
        
        if CurrentAction ~= nil then
            wait = 0
            
            SetTextComponentFormat('STRING')
            AddTextComponentString("<FONT FACE='A9eelsh'>" .. CurrentActionMsg)
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
            
            if IsControlPressed(0, 38) and (GetGameTimer() - GUI.Time) > 300 then
                wait = 0
                
                if CurrentAction == 'property_menu' then
                    wait = 0
                    OpenPropertyMenu(CurrentActionData.property)
                end
                
                if CurrentAction == 'gateway_menu' then
                    wait = 0
                    
                    if Config.EnablePlayerManagement then
                        wait = 0
                        OpenGatewayOwnedPropertiesMenu(CurrentActionData.property)
                    else
                        OpenGatewayMenu(CurrentActionData.property)
                    end
                
                end
                
                if CurrentAction == 'room_menu' then
                    wait = 0
                    OpenRoomMenu(CurrentActionData.property, CurrentActionData.owner)
                end
                
                if CurrentAction == 'room_exit' then
                    wait = 0
                    TriggerEvent('instance:leave')
                end
                
                CurrentAction = nil
                GUI.Time = GetGameTimer()
            
            end
        
        end
        Citizen.Wait(wait)
    
    end
end)
