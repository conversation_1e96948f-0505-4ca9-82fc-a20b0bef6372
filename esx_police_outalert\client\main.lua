--[[ sample usage
TriggerEvent("tgiann-policeAlert:alert", alertName, plate, alertCoord, vehicle)

alertName: "string" - the name of the notice that will appear in the notification ex: Gun Fired
plate: "false/true" - hide license plate 
alertCoord: "vector3" - special notice coordinate ex: GetEntityCoords(PlayerPedId()) or vector3(0.0 , 0.0, 0.0)
vehicle: "false/true" - hide vehicle information 

Client Events
    - tgiann-policeAlert:active
]]

local notifActive = true
local alertOn = false
local alertX = nil
local alertY = nil
local lastAlertId = nil
local PlayerData  = {
    job = {
        name = "unemployed"
    }
}
local toggleOld = false
local gpsName = ""
local myFramework = nil

if framework == "esx" then
    Citizen.CreateThread(function()
        --[[ while myFramework == nil do
            TriggerEvent('esx:getSharedObject', function(obj) myFramework = obj end)
            Citizen.Wait(0)
        end ]]
        myFramework = exports["es_extended"]:getSharedObject()
        while myFramework.GetPlayerData().job == nil do Citizen.Wait(100) end
        PlayerData = myFramework.GetPlayerData()
    end)
elseif framework == "qb" then
    Citizen.CreateThread(function()
        myFramework = exports['qb-core']:GetCoreObject()
        while myFramework.Functions.GetPlayerData().job == nil do Citizen.Wait(100) end
        PlayerData = myFramework.Functions.GetPlayerData()
    end)
end

local colors = {
    ['0'] = "Metallic Black",
    ['1'] = "Metallic Graphite Black",
    ['2'] = "Metallic Black Steal",
    ['3'] = "Metallic Dark Silver",
    ['4'] = "Metallic Silver",
    ['5'] = "Metallic Blue Silver",
    ['6'] = "Metallic Steel Gray",
    ['7'] = "Metallic Shadow Silver",
    ['8'] = "Metallic Stone Silver",
    ['9'] = "Metallic Midnight Silver",
    ['10'] = "Metallic Gun Metal",
    ['11'] = "Metallic Anthracite Grey",
    ['12'] = "Matte Black",
    ['13'] = "Matte Gray",
    ['14'] = "Matte Light Grey",
    ['15'] = "Util Black",
    ['16'] = "Util Black Poly",
    ['17'] = "Util Dark silver",
    ['18'] = "Util Silver",
    ['19'] = "Util Gun Metal",
    ['20'] = "Util Shadow Silver",
    ['21'] = "Worn Black",
    ['22'] = "Worn Graphite",
    ['23'] = "Worn Silver Grey",
    ['24'] = "Worn Silver",
    ['25'] = "Worn Blue Silver",
    ['26'] = "Worn Shadow Silver",
    ['27'] = "Metallic Red",
    ['28'] = "Metallic Torino Red",
    ['29'] = "Metallic Formula Red",
    ['30'] = "Metallic Blaze Red",
    ['31'] = "Metallic Graceful Red",
    ['32'] = "Metallic Garnet Red",
    ['33'] = "Metallic Desert Red",
    ['34'] = "Metallic Cabernet Red",
    ['35'] = "Metallic Candy Red",
    ['36'] = "Metallic Sunrise Orange",
    ['37'] = "Metallic Classic Gold",
    ['38'] = "Metallic Orange",
    ['39'] = "Matte Red",
    ['40'] = "Matte Dark Red",
    ['41'] = "Matte Orange",
    ['42'] = "Matte Yellow",
    ['43'] = "Util Red",
    ['44'] = "Util Bright Red",
    ['45'] = "Util Garnet Red",
    ['46'] = "Worn Red",
    ['47'] = "Worn Golden Red",
    ['48'] = "Worn Dark Red",
    ['49'] = "Metallic Dark Green",
    ['50'] = "Metallic Racing Green",
    ['51'] = "Metallic Sea Green",
    ['52'] = "Metallic Olive Green",
    ['53'] = "Metallic Green",
    ['54'] = "Metallic Gasoline Blue Green",
    ['55'] = "Matte Lime Green",
    ['56'] = "Util Dark Green",
    ['57'] = "Util Green",
    ['58'] = "Worn Dark Green",
    ['59'] = "Worn Green",
    ['60'] = "Worn Sea Wash",
    ['61'] = "Metallic Midnight Blue",
    ['62'] = "Metallic Dark Blue",
    ['63'] = "Metallic Saxony Blue",
    ['64'] = "Metallic Blue",
    ['65'] = "Metallic Mariner Blue",
    ['66'] = "Metallic Harbor Blue",
    ['67'] = "Metallic Diamond Blue",
    ['68'] = "Metallic Surf Blue",
    ['69'] = "Metallic Nautical Blue",
    ['70'] = "Metallic Bright Blue",
    ['71'] = "Metallic Purple Blue",
    ['72'] = "Metallic Spinnaker Blue",
    ['73'] = "Metallic Ultra Blue",
    ['74'] = "Metallic Bright Blue",
    ['75'] = "Util Dark Blue",
    ['76'] = "Util Midnight Blue",
    ['77'] = "Util Blue",
    ['78'] = "Util Sea Foam Blue",
    ['79'] = "Uil Lightning blue",
    ['80'] = "Util Maui Blue Poly",
    ['81'] = "Util Bright Blue",
    ['82'] = "Matte Dark Blue",
    ['83'] = "Matte Blue",
    ['84'] = "Matte Midnight Blue",
    ['85'] = "Worn Dark blue",
    ['86'] = "Worn Blue",
    ['87'] = "Worn Light blue",
    ['88'] = "Metallic Taxi Yellow",
    ['89'] = "Metallic Race Yellow",
    ['90'] = "Metallic Bronze",
    ['91'] = "Metallic Yellow Bird",
    ['92'] = "Metallic Lime",
    ['93'] = "Metallic Champagne",
    ['94'] = "Metallic Pueblo Beige",
    ['95'] = "Metallic Dark Ivory",
    ['96'] = "Metallic Choco Brown",
    ['97'] = "Metallic Golden Brown",
    ['98'] = "Metallic Light Brown",
    ['99'] = "Metallic Straw Beige",
    ['100'] = "Metallic Moss Brown",
    ['101'] = "Metallic Biston Brown",
    ['102'] = "Metallic Beechwood",
    ['103'] = "Metallic Dark Beechwood",
    ['104'] = "Metallic Choco Orange",
    ['105'] = "Metallic Beach Sand",
    ['106'] = "Metallic Sun Bleeched Sand",
    ['107'] = "Metallic Cream",
    ['108'] = "Util Brown",
    ['109'] = "Util Medium Brown",
    ['110'] = "Util Light Brown",
    ['111'] = "Metallic White",
    ['112'] = "Metallic Frost White",
    ['113'] = "Worn Honey Beige",
    ['114'] = "Worn Brown",
    ['115'] = "Worn Dark Brown",
    ['116'] = "Worn straw beige",
    ['117'] = "Brushed Steel",
    ['118'] = "Brushed Black steel",
    ['119'] = "Brushed Aluminium",
    ['120'] = "Chrome",
    ['121'] = "Worn Off White",
    ['122'] = "Util Off White",
    ['123'] = "Worn Orange",
    ['124'] = "Worn Light Orange",
    ['125'] = "Metallic Securicor Green",
    ['126'] = "Worn Taxi Yellow",
    ['127'] = "police car blue",
    ['128'] = "Matte Green",
    ['129'] = "Matte Brown",
    ['130'] = "Worn Orange",
    ['131'] = "Matte White",
    ['132'] = "Worn White",
    ['133'] = "Worn Olive Army Green",
    ['134'] = "Pure White",
    ['135'] = "Hot Pink",
    ['136'] = "Salmon pink",
    ['137'] = "Metallic Vermillion Pink",
    ['138'] = "Orange",
    ['139'] = "Green",
    ['140'] = "Blue",
    ['141'] = "Mettalic Black Blue",
    ['142'] = "Metallic Black Purple",
    ['143'] = "Metallic Black Red",
    ['144'] = "hunter green",
    ['145'] = "Metallic Purple",
    ['146'] = "Metaillic V Dark Blue",
    ['147'] = "MODSHOP BLACK1",
    ['148'] = "Matte Purple",
    ['149'] = "Matte Dark Purple",
    ['150'] = "Metallic Lava Red",
    ['151'] = "Matte Forest Green",
    ['152'] = "Matte Olive Drab",
    ['153'] = "Matte Desert Brown",
    ['154'] = "Matte Desert Tan",
    ['155'] = "Matte Foilage Green",
    ['156'] = "DEFAULT ALLOY COLOR",
    ['157'] = "Epsilon Blue",
}

if testCommand then
    local test = 0
    RegisterCommand("alertTest", function()
        test = test + 1
        TriggerEvent("tgiann-policeAlert:alert", "Test Alert "..test, false)
    end) 
end

if framework == "esx" then
	RegisterNetEvent('esx:playerLoaded')
	AddEventHandler('esx:playerLoaded', function()
		PlayerData = myFramework.GetPlayerData()
	end)

	RegisterNetEvent('esx:setJob')
	AddEventHandler('esx:setJob', function(job)
		PlayerData.job = job
	end)
elseif framework == "qb" then
    RegisterNetEvent('QBCore:Client:OnPlayerLoaded')
    AddEventHandler('QBCore:Client:OnPlayerLoaded', function()
        PlayerData = myFramework.Functions.GetPlayerData()
    end)

    RegisterNetEvent('QBCore:Client:OnJobUpdate')
    AddEventHandler('QBCore:Client:OnJobUpdate', function(job)
        PlayerData.job = job
    end)
else
    RegisterCommand("setjob", function(source, args)
        PlayerData.job = {}
        PlayerData.job.name = args[1]
    end)
end

RegisterCommand("setdispatchname", function(source, args)
    if args[1] then gpsName = table.concat(args, ' ') end
end)

RegisterNetEvent("tgiann-policeAlert:client:Normal")
AddEventHandler("tgiann-policeAlert:client:Normal", function(alertId, alertName, street, sex, x, y, date)
    if checkJobs() then
        mapblip(alertName, x, y)
        normal(alertId, alertName, street, sex, x, y, date)  
    end
end)

RegisterNetEvent("tgiann-policeAlert:client:full")
AddEventHandler("tgiann-policeAlert:client:full", function(alertId, alertName, street, arac, plate, renk, sex, x, y, date)
    if checkJobs() then
        mapblip(alertName, x, y)
        full(alertId, alertName, street, arac, plate, renk, sex, x, y, date)  
    end
end)

Citizen.CreateThread(function()
   -- RegisterKeyMapping('dispatch'..dispatchKey, 'Dispatch', 'keyboard', dispatchKey)
   -- RegisterKeyMapping('dispatchPressY'..dispatchHeadingKey, 'Dispatch [Heading to The Police Call]', 'keyboard', dispatchHeadingKey)
    RegisterKeyMapping('dispatchPressE'..dispatchMarkKey, 'Dispatch [Mark On Map]', 'keyboard', dispatchMarkKey)
end)

RegisterCommand('dispatch'..dispatchKey, function()
    if not toggleOld then
        if checkJobs() then
            toggleOld = true
            SendNUIMessage({action = 'showOld'})
            SetNuiFocus(true, true)
            Citizen.Wait(1000)
        end
    end
end, false)

local pressE = false
local pressY = false
RegisterCommand('dispatchPressY'..dispatchHeadingKey, function()
    if alertOn then
        if lastAlertId and pressY then
            TriggerServerEvent("tgiann-policeAlert:server:yonel", lastAlertId, gpsName)
            lastAlertId = nil
            pressY = false
        end
    end
end, false)

RegisterCommand('dispatchPressE'..dispatchMarkKey, function()
    if alertOn then
        if pressE then
            SetNewWaypoint(alertX, alertY)
            pressE = false
            alertX = nil
            alertY = nil
            showNotification("<FONT FACE='A9eelsh'>ﺔﻄﻳﺮﺨﻟﺍ ﻰﻠﻋ ﻊﻗﻮﻤﻟﺍ ﺪﻳﺪﺤﺗ ﻢﺗ") 
        end
    end
end, false)

RegisterNUICallback('setCoords', function(coords, cb)
    showNotification("<FONT FACE='A9eelsh'>ﺔﻄﻳﺮﺨﻟﺍ ﻰﻠﻋ ﻊﻗﻮﻤﻟﺍ ﺪﻳﺪﺤﺗ ﻢﺗ") 
    SetNewWaypoint(coords.x, coords.y)
end)

RegisterNUICallback('yonel', function(data, cb)
    TriggerServerEvent("tgiann-policeAlert:server:yonel", data.alertId, gpsName)
end)

RegisterNetEvent('tgiann-policeAlert:client:yonel')
AddEventHandler('tgiann-policeAlert:client:yonel', function(alertId, name)
    if checkJobs() then
        SendNUIMessage({ 
            action = "yonel",
            alertId = alertId,
            name = name
        })
    end
end)

RegisterNetEvent('tgiann-policeAlert:setGpsName')
AddEventHandler('tgiann-policeAlert:setGpsName', function(newGpsName)
    gpsName = newGpsName
end)

RegisterNUICallback('closeui', function()
	toggleOld = false
    SetNuiFocus(false, false)
    TriggerEvent("tgiann-hud:big-map", false)
end)

function normal(alertId, alertName, street, sex, x, y, date)
    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)
	SendNUIMessage({
		action = 'normal',
		alertName = alertName,
		street = street,
		arac = "yok",
		plate = "yok",
		renk = "yok",
        sex = sex,
        coords = {x, y},
        date = date,
        alertId = alertId,
        yonel = false,
    })
	timeReset(x, y, alertId)
end

function full(alertId, alertName, street, arac, plate, renk, sex, x, y, date)
    PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)
	SendNUIMessage({
		action = 'normal',
		alertName = alertName,
		street = street,
		arac = arac,
		plate = plate,
		renk = renk,
        sex = sex,
        coords = {x, y},
        date = date,
        alertId = alertId, 
        yonel = false,
    })
	timeReset(x, y, alertId)
end

function timeReset(x, y, alertId)
    PlaySoundFrontend(-1, "Event_Start_Text", "GTAO_FM_Events_Soundset", 0)
	alertOn = true
    pressE = true
    pressY = true
	alertX = x
	alertY = y
    lastAlertId = alertId
	Citizen.Wait(10000)
	if alertOn then
		alertOn = false
	end
end

function mapblip(alertName, x, y)
    if alertName == "Gun Fired" then
        TriggerEvent("tgiann-policeAlert:setBlip", x, y, 433, alertName)
    end
end

RegisterNetEvent('tgiann-policeAlert:setBlip')
AddEventHandler('tgiann-policeAlert:setBlip', function(x, y, icon, alertName)
    local alpha = 200
    local blip = AddBlipForCoord(x, y, 5.0)
	SetBlipSprite(blip, icon)
    SetBlipDisplay(blip, 2)
    SetBlipScale(blip, 1.60)
    SetBlipColour(blip, 75)
    SetBlipAsShortRange(blip, false)
    SetBlipAlpha(blip, alpha)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(alertName)
    EndTextCommandSetBlipName(blip)

    while alpha ~= 0 do
        Citizen.Wait(60 * 6)
        alpha = alpha - 1
        SetBlipAlpha(blip, alpha)

        if alpha == 0 then
            RemoveBlip(blip)
            break
        end
    end
end)

RegisterNetEvent('tgiann-policeAlert:alert')
AddEventHandler('tgiann-policeAlert:alert', function(alertName, plate, alertCoord, vehicle)
    if not notifActive then return end
    local vehActive = true
    if vehicle ~= nil then vehActive = vehicle end
    local hidePlate = false
    if plate ~= nil then hidePlate = plate end

    local playerPed = PlayerPedId()	
    local padCoords = GetEntityCoords(playerPed)
    local alertCoord = vector3(padCoords.x, padCoords.y, padCoords.z)

    if alertCoord then alertCoord = vector3(alertCoord.x, alertCoord.y, alertCoord.z) end

    local var1, var2 = GetStreetNameAtCoord(alertCoord.x, alertCoord.y, alertCoord.z, Citizen.ResultAsInteger(), Citizen.ResultAsInteger())
    local street = GetStreetNameFromHashKey(var1)
    local sokak2 = GetStreetNameFromHashKey(var2)
    if sokak2 ~= nil and sokak2 ~= '' then
        street = street .. ', ' .. sokak2
    end

    local sex = "Female"
    if GetEntityModel(playerPed) == 1885233650 then sex = "Male" end

    if IsPedInAnyVehicle(playerPed, false) and vehActive then
        local showPlate = math.random(1, 100)
        local vehicle = GetVehiclePedIsIn(playerPed)
        local color1 = colors[tostring(GetVehicleCustomPrimaryColour(vehicle))] or "Unknown"
        local color2 = colors[tostring(GetVehicleCustomSecondaryColour(vehicle))] or "Unknown"
        local plate = "Unknown"

        if showPlate < 50 and not hidePlate then plate = GetVehicleNumberPlateText(vehicle) end

        if color2 == color1 then
            colorName = color1
        else
            colorName = color1 ..', '.. color2
        end
        TriggerServerEvent("tgiann-policeAlert:server:full", alertName, street, GetDisplayNameFromVehicleModel(GetEntityModel(vehicle)), plate, colorName, sex, alertCoord.x, alertCoord.y)
    else
        TriggerServerEvent("tgiann-policeAlert:server:normal", alertName, street, sex, alertCoord.x, alertCoord.y)
    end	    
end)

Citizen.CreateThread(function()
    while true do
        local time = 1000
        local ped = PlayerPedId()
        if IsPedArmed(ped, 7) then
            time = 1
            if IsPedShooting(ped) then
                if math.random(1,2) == 1 then
                    local weaponSlec = GetSelectedPedWeapon(ped)
                    if weaponSlec ~= `WEAPON_SNOWBALL` then
                        if not checkJobs() then
                            if weaponSlec == `weapon_stungun` then
                                TriggerEvent("tgiann-policeAlert:alert", "Stinger S-200(Taser)", false)
                            else
                                TriggerEvent("tgiann-policeAlert:alert", "Gun Fired", false)
                            end
                            Citizen.Wait(gunFiredNotifCD)
                        end 
                    end
                else
                    Citizen.Wait(leftClickControlCD)
                end
            end
        end
        Citizen.Wait(time)
    end
end)

RegisterCommand("code", function(source, args)
    if checkJobs() then
        local codeLvl = args[1]
        if not codeLvl then
            showNotification('Specify the code level! ex: 0, 1, 2, 3, 99')
        else
            local alertName = gpsName.." Call For Help! Code / "..codeLvl
            TriggerEvent("tgiann-policeAlert:alert", alertName, false, false, false)
            TriggerServerEvent("tgiann-policeAlert:server:codeAlert", codeLvl)
            showNotification('A Call For Help Has Been Sent')
        end
    end
end)

function playCode(code99)
    PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
    if code99 then
        Wait(900)
        PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
        Wait(900)
        PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
    end
end

RegisterNetEvent('tgiann-policeAlert:client:codeAlert')
AddEventHandler('tgiann-policeAlert:client:codeAlert', function(codeLvl)
    if checkJobs() then
        if codeLvl == "99" or codeLvl == "0" then
            playCode(true)
        else
            playCode(false)
        end
    end
end)

RegisterNetEvent('tgiann-policeAlert:active')
AddEventHandler('tgiann-policeAlert:active', function(status)
    notifActive = status
end)

function showNotification(msg)
	BeginTextCommandThefeedPost('STRING')
	AddTextComponentSubstringPlayerName(msg)
	EndTextCommandThefeedPostTicker(0,1)
end

function checkJobs() 
    local returnVal = false
    for i=1, #jobs do
        if PlayerData.job and PlayerData.job.name == jobs[i] then
            returnVal = true
            break
        end
    end
    return returnVal
end