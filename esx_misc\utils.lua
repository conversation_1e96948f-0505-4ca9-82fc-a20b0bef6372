local NumberCharset = {}
local Charset = {}

for i = 48,  57 do table.insert(NumberCharset, string.char(i)) end

for i = 65,  90 do table.insert(Charset, string.char(i)) end
for i = 97, 122 do table.insert(Charset, string.char(i)) end

function GeneratePlate()
	local generatedPlate
	local doBreak = false

	while true do
		Citizen.Wait(2)
		math.randomseed(GetGameTimer())
		if Config.Main_esx_advancedvehicleshop.PlateUseSpace then
			generatedPlate = string.upper(GetRandomLetter(Config.Main_esx_advancedvehicleshop.PlateLetters) .. ' ' .. GetRandomNumber(Config.Main_esx_advancedvehicleshop.PlateNumbers))
		else
			generatedPlate = string.upper(GetRandomLetter(Config.Main_esx_advancedvehicleshop.PlateLetters) .. GetRandomNumber(Config.Main_esx_advancedvehicleshop.PlateNumbers))
		end

		MySQL.Async.fetchAll('SELECT 1 FROM owned_vehicles WHERE plate = @plate', {
			['@plate'] = generatedPlate
		}, function(result)
			if result[1] == nil then
				doBreak = true
			end
		end)

		if doBreak then
			break
		end
	end

	return generatedPlate
end

function GetRandomNumber(length)
	Citizen.Wait(0)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomNumber(length - 1) .. NumberCharset[math.random(1, #NumberCharset)]
	else
		return ''
	end
end

function GetRandomLetter(length)
	Citizen.Wait(0)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomLetter(length - 1) .. Charset[math.random(1, #Charset)]
	else
		return ''
	end
end