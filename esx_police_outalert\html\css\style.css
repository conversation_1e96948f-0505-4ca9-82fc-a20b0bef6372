body {
    background-color: transparent !important;
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Old versions of Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; 
}

::-webkit-scrollbar {
    width: 5px;
}
   
::-webkit-scrollbar-thumb {
    background: #3698d7; 
    border-radius: 10px;
}
  
::-webkit-scrollbar-thumb:hover {
    background: #27ae60; 
}

.notification .gizle {
    display: none;
}

#notifi {
    overflow: hidden;
    height: 85%;
    margin-top: 4.5%;
}

#top-bar-oldNotifi {
    display: none;
}

.notification {
    display: flex;
    font: caption;
    font-size: 13px;
    align-items: flex-end;
    flex-direction: column;
}

.notification-ic {
    background-color: #0f4e73d0;
    border-right: 5px solid #3698d7;
    color: #ffffff;
    border-radius: 2px;
    min-width: 290px;
    animation-name: fadeInOpacity;
	animation-iteration-count: 1;
	animation-timing-function: ease-in;
    animation-duration: .3s;
    margin-bottom: 5px;
}

@keyframes fadeInOpacity {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}

.line-1, .line-2, .line-3, .line-4, .line-5, .line-6, .line-8 {
    display: block;
    padding-left: 5px;
    padding-bottom: 2px;
}

.line-3, .line-4 {
    display: inline-block;
}

.ikon-sagi {
    display: inline-block;
    padding-left: 5px;
    font-size: 12px;
}

.i {
    display: inline-block;
}

.kirmizi {
    display: inline-block;
    margin-right: 5px;
    padding: 3px;
    font-size: 10px;
    background: #e74c3c;
    border-radius: 3px;
    margin-bottom: 5px;
}

.baslik {
    display: inline-block;
    font-size: 12px;
    padding-top: 2px;
}

.e-bas {
    background: #27ae60;
    padding: 5px 10px;
    font-size: 11px;
}

.yonelen {
    background: #f9aa33;
    padding: 5px 10px;
    font-size: 12.5px; 
    color: #111;
    display: none;
}

.notifData {
    padding: 10px;
}

.top-bar {
    display: flex;
    flex-direction: row;
    background-color: #0f4e73d0;
    border-right: 5px solid #3698d7;
    color: #ffffff;
    border-radius: 2px;
    min-width: 270px;
    justify-content: space-around;
    padding: 5px 10px;
    animation-name: fadeInOpacity;
	animation-iteration-count: 1;
	animation-timing-function: ease-in;
    animation-duration: .3s;
}