Config = {}

-- Time in minutes after a player leaves a vehicle for it to be considered abandoned
Config.LastDrive = 30 -- Default: 30 minutes

-- List of vehicle model names (lowercase) that should NOT be considered abandoned
-- Example: Config.BlackListVehicles = { ['police'] = true, ['ambulance'] = true }
Config.BlackListVehicles = {
    -- Add model names as keys with value true, e.g.:
    -- ['taco'] = true,
    -- ['adder'] = true,
}

-- Debug printing (set to true to enable extra console messages)
Config.Debug = false

print("[nex-abandoned] Config Loaded. Abandon time: " .. Config.LastDrive .. " minutes.")

