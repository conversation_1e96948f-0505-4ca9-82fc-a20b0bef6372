Locales['fr'] = {

	['robbery_cancelled'] = 'Le braquage a été annulé',
	['shop_robbery'] = 'Braquage de bijouterie',
	['press_to_rob'] = 'tirer pour commencer le braquage',
	['seconds_remaining'] = '~w~ secondes restantes',
	['robbery_cancelled_at'] = '~r~ Le braquage a été annulé à: ~b~',
	['robbery_has_cancelled'] = '~r~ Le braquage a été annulé: ~b~',
	['already_robbed'] = 'les bijoux ont déjà été volés. Veuillez attendre: ',
	['seconds'] = 'secondes.',
	['rob_in_prog'] = '~r~ Braquage en cours à: ~b~',
	['started_to_rob'] = 'Vous commencer le braquage ',
	['do_not_move'] = ', prenez les bijoux dans les vitrines !',
	['alarm_triggered'] = 'l\'alarme a été déclenchée',
	['hold_pos'] = 'Quand vous aurez récupéré le maximum de bijoux, fuyez !',
	['robbery_complete'] = '~r~ Le braquage a reussi !~s~ ~h~ enfuyez vous ! ',
	['robbery_complete_at'] = '~r~ Le braquage a réussi à: ~b~',
	['min_two_police'] = 'Il doit y avoir au moins ~b~',
	['min_two_police2'] = ' ~w~policiers en ville.',
	['robbery_already'] = '~r~Un braquage est déjà en cours.',
	['robbery_has_ended'] = 'Braquage terminé',
	['end'] = 'La bijouterie a été braquéée!',
	['notenoughgold'] = '~r~Vous n\'avez pas assez de bijoux !',
	['copsforsell'] = 'Il doit y avoir au moins ~b~',
	['copsforsell2'] = ' ~w~policiers en ville pour revendre les bijoux.',
	['goldsell'] = '~y~Bijoux~s~ vente en cours',
	['field'] = 'Pressez ~y~E~s~ pour ~o~voler~s~ les bijoux',
	['collectinprogress'] = 'récupération des bijoux en cours...',
	['lester'] = '~g~Vous avez braqué la bijouterie! Maintenant, revendez les bijoux !', --if lester map blips hide
	--['lester'] = '~g~Vous avez braqué la bijouterie! Maintenant, revendez les bijoux à ~r~Lester!',-- if Lester map blip enable
	['jewelsblipmap'] = 'Bijouterie',
	['press_to_collect'] = 'pour voler les bijoux',
	['smash_case'] = 'Briser la vitrine',
	['press_to_sell'] = 'Pressez ~INPUT_PICKUP~ pour vendre les bijoux',
	['need_bag'] = 'vous avez besoin d\'un sac ! allez dans un magasin de vêtements '

}
