local PlayerData       = {}

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
  PlayerData = xPlayer
	TriggerServerEvent("esx:playerconnected")


end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
  PlayerData.job = job
end)

local isJacking = true
local isStolen = true
Citizen.CreateThread(function()
	while true do
		local sleep = 1500

		if(IsPedInAnyVehicle(PlayerPedId()))then
			sleep = 0
			local playerPed = PlayerPedId()
			local coords    = GetEntityCoords(playerPed)
			vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 7.0, 0, 70)
			if(IsVehicleStolen(vehicle) and isStolen )then
				sleep = 1000
				TriggerServerEvent("esx:jackingcar",GetDisplayNameFromVehicleModel(GetEntityModel(vehicle)))
				isStolen = false
			end
		else
			isStolen = true
			vehicle = nil
		end

		if(IsPedJacking(PlayerPedId())) then
			sleep = 0
				if(settings_esx_discord_bot.LogPedJacking == true and isJacking) then
					sleep = 0
					local playerPed = PlayerPedId()
					local coords    = GetEntityCoords(playerPed)

					local vehicle = nil

					if IsPedInAnyVehicle(playerPed) then
						sleep = 0
						vehicle = GetVehiclePedIsIn(playerPed)

					else
						vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 7.0, 0, 70)

					end
					sleep = 1000
					TriggerServerEvent("esx:jackingcar",GetDisplayNameFromVehicleModel(GetEntityModel(vehicle)))

					isJacking = false
					vehicle = nil

				end
		else
			isJacking = true
		end



		Citizen.Wait(sleep)
	end
end)





local isIncarPolice = false
Citizen.CreateThread(function()
	while true do
		local sleep = 1500

		if(IsPedInAnyPoliceVehicle(PlayerPedId())) then
			sleep = 0
				if(settings_esx_discord_bot.LogEnterPoliceVehicle == true and not isIncarPolice and PlayerData.job.name ~= "police") then
					sleep = 500
					TriggerServerEvent("esx:enterpolicecar",GetDisplayNameFromVehicleModel(GetEntityModel(GetVehiclePedIsIn(PlayerPedId(), 0))))
					isIncarPolice = true
				end

		else
			isIncarPolice = false
		end
		Citizen.Wait(sleep)
	end
end)




local isIncar = false
Citizen.CreateThread(function()
	while true do
		local sleep = 1500


		if(IsPedInAnyVehicle(PlayerPedId()) and not IsPedInAnyPoliceVehicle(PlayerPedId())) then
			sleep = 0

				if(settings_esx_discord_bot.LogEnterPoliceVehicle == true and not isIncar) then
					sleep = 0

					for i=1, #blacklistedModels_esx_discord_bot, 1 do

						if(blacklistedModels_esx_discord_bot[i] == GetDisplayNameFromVehicleModel(GetEntityModel(GetVehiclePedIsIn(PlayerPedId(), 0))))then
							sleep = 0
							TriggerServerEvent("esx:enterblacklistedcar",GetDisplayNameFromVehicleModel(GetEntityModel(GetVehiclePedIsIn(PlayerPedId(), 0))))
							isIncar = true
						end
					end
				end
		else
			isIncar = false
		end
		Citizen.Wait(sleep)
	end
end)