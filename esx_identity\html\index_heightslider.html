<html>
	<head>
		<script src="nui://game/ui/jquery.js" type="text/javascript"></script>
		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
		<link href="css/style.css" rel="stylesheet" type="text/css" />
	</head>

	<body onkeydown="TriggeredKey(this)">
		<div class="dialog">
			<img id="logo" src="img/esx_identity.png" height="110px" width="332px">
			<form id="register" name="register" action="#">
				<input id="firstname" type="text" class="" name="firstname" placeholder="First Name (Max 16 Characters)" onKeyDown="if(this.value.length==16 && event.keyCode!=8) return false;" onkeypress="if (isNumber(event) == true) return false;" onKeyUp="checkFirstName()" onmouseup="checkFirstName()"><br>
				<input id="lastname" type="text" class="" name="lastname" placeholder="Last Name (Max 16 Characters)" onKeyDown="if(this.value.length==16 && event.keyCode!=8) return false;" onkeypress="if (isNumber(event) == true) return false;" onKeyUp="checkLastName()" onmouseup="checkLastName()"><br>
				<input id="dateofbirth" type="text" name="dateofbirth" class="" placeholder="Date of Birth (MM/DD/YYYY)" dateformat="M d y" min="01/01/1900" max="12/31/2020" onfocus="(this.type='date')" onkeypress="if ( isNaN(String.fromCharCode(event.keyCode) )) return false;" onchange="checkDOB()" onKeyUp="checkDOB()" onmouseup="checkDOB()"><br>
				<center>
					<div class="radio-toolbar">
						<input type="radio" id="radiom" name="sex" value="m" checked>
						<label for="radiom">Male</label>
					
						<input type="radio" id="radiof" name="sex" value="f">
						<label for="radiof">Female</label>
					</div>
				</center>
					<div class="range-wrap" style="width: 80%;">
						<input type="range" id="height" class="range" min="48" max="96" value="48">
						<output class="bubble"></output>
					</div>
				<button id="submit" type="submit"><font size="5px">Submit</font></button>
			</form>
			<center><font size="1px" color="red">If the submit button doesn't work, please ensure that you've entered the fields correctly.</font></center>
		</div>
		<script>

			const allRanges = document.querySelectorAll(".range-wrap");
			allRanges.forEach(wrap => {
				const range = wrap.querySelector(".range");
				const bubble = wrap.querySelector(".bubble");

				range.addEventListener("input", () => {
					setBubble(range, bubble);
				});
				setBubble(range, bubble);
			});

			function setBubble(range, bubble) {
				const val = range.value;
				const min = range.min ? range.min : 0;
				const max = range.max ? range.max : 100;
				const newVal = Number(((val - min) * 100) / (max - min));
				bubble.innerHTML = 'Height: ' + val;
				bubble.style.left = `calc(${newVal}% + (${8 - newVal * 0.15}px))`;
			}
		
			var myFirstName = document.getElementById('firstname');
			var myLastName = document.getElementById('lastname');
			var myDOB = document.getElementById('dateofbirth');
			var myHeight = document.getElementById('height');

			function isNumber(e) {
				var key=e.which || e.KeyCode;
				if ( key >=48 && key <= 57) {
					return true; 
				}
				else {
					return false;
				}
			}

			function checkFirstName() {
				var value = myFirstName.value;
				if(value.trim().match(/^[a-zA-Z][0-9a-zA-Z .,'-]*$/) == null) {
					myFirstName.style.backgroundColor = '#B6D7A8';
					myFirstName.style.color = 'black';
				}
				else {
					if(value.length > 0 && value.length < 17) {
						myFirstName.style.backgroundColor = '#B6D7A8';
						myFirstName.style.color = 'black';
					}
					else {
						myFirstName.style.backgroundColor = '#B6D7A8';
						myFirstName.style.color = 'black';
					}
				}
			}

			function checkLastName() {
				var value = myLastName.value;
				if(value.trim().match(/^[a-zA-Z][0-9a-zA-Z .,'-]*$/) == null) {
					myLastName.style.backgroundColor = '#B6D7A8';
					myLastName.style.color = 'black';
				}
				else {
					if(value.length > 0 && value.length < 17) {
						myLastName.style.backgroundColor = '#B6D7A8';
						myLastName.style.color = 'black';
					}
					else {
						myLastName.style.backgroundColor = '#B6D7A8';
						myLastName.style.color = 'black';
					}
				}
			}
			
			function checkDOB() {
				var date = new Date($('#dateofbirth').val());
				day = date.getDate();
				month = date.getMonth() + 1;
				year = date.getFullYear();
				if (isNaN(month) || isNaN(day) || isNaN(year)) {
					myDOB.style.backgroundColor = '#B6D7A8';
					myDOB.style.color = 'black';
				}
				else {
					var dateInput = [month, day, year].join('/');

					var regExp = /^(\d{1,2})(\/|-)(\d{1,2})(\/|-)(\d{4})$/;
					var dateArray = dateInput.match(regExp);

					if (dateArray == null){
						return false;
					}

					month = dateArray[1];
					day= dateArray[3];
					year = dateArray[5];        

					if (month < 1 || month > 12){
						myDOB.style.backgroundColor = '#B6D7A8';
						myDOB.style.color = 'black';
					}
					else if (day < 1 || day> 31) { 
						myDOB.style.backgroundColor = '#B6D7A8';
						myDOB.style.color = 'black';
					}
					else if ((month==4 || month==6 || month==9 || month==11) && day ==31) {
						myDOB.style.backgroundColor = '#B6D7A8';
						myDOB.style.color = 'black';
					}
					else if (month == 2) {
						var isLeapYear = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
						if (day> 29 || (day ==29 && !isLeapYear)){
							myDOB.style.backgroundColor = '#B6D7A8';
							myDOB.style.color = 'black';
						}
					}
					else {
						myDOB.style.backgroundColor = '#B6D7A8';
						myDOB.style.color = 'black';	
					}				
				}
			}

			function TriggeredKey(e) {
				var keycode;
				if (window.event) keycode = window.event.keyCode;
				if (window.event.keyCode == 13 || window.event.keyCode == 27) return false;
			}
		</script>
		<script src="js/script.js" type="text/javascript"></script>
	</body>
</html>
