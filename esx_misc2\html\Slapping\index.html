<html>
    <head>
        <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
        <script>
            var audioPlayer = null;
            window.addEventListener('message', function(event) {
                if (event.data.DemarrerLaMusique == "DemarrerLaMusique") {
                  if (audioPlayer != null) {
                    audioPlayer.pause();
                  }
                  audioPlayer = new Audio("./Giffle.ogg");
                  audioPlayer.volume = event.data.VolumeDeLaMusique;
                  audioPlayer.play();
                }
            });
        </script>
    </head>
</html>
