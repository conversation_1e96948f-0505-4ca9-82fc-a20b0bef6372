# نظام متطلبات الخبرة للوظائف المحددة - Selective Job Experience Requirements System

## الوصف
تم إضافة نظام متطلبات الخبرة للوظائف المحددة فقط في مركز التوظيف. النظام يعمل كالتالي:

### للوظائف المحددة في الكونفق (taxi, mechanic, ambulance, police, agent, admin):
- **اللون الأخضر**: للوظائف التي يمكنهم التقديم عليها (خبرتهم كافية)
- **اللون الأحمر**: للوظائف التي لا يمكنهم التقديم عليها (خبرتهم غير كافية)
- **تنسيق النص**: الوظيفة - (خبرة المطلوبة)

### للوظائف الأخرى:
- تظهر بشكل عادي بدون ذكر متطلبات الخبرة
- يمكن للجميع التقديم عليها

## التثبيت

### إعادة تشغيل المورد
```
restart esx_joblisting
```

## الإعدادات

### في ملف config.lua

```lua
-- نظام متطلبات الخبرة للوظائف
Config.ExperienceSystem = {
    Enabled = true, -- تفعيل/إلغاء تفعيل نظام الخبرة
    
    -- الألوان المستخدمة في القائمة
    Colors = {
        Eligible = '#00ff00',    -- أخضر للمؤهل
        NotEligible = '#ff0000', -- أحمر لغير المؤهل
        Default = '#ffffff'      -- أبيض افتراضي
    },
    
    -- رسائل النظام
    Messages = {
        NotEligible = 'تحتاج إلى مستوى خبرة %s للتقديم على هذه الوظيفة. مستواك الحالي: %s',
        CurrentLevel = 'مستوى خبرتك الحالي: %s'
    },
    
    -- متطلبات الخبرة للوظائف (يمكن تعديلها هنا بدلاً من قاعدة البيانات)
    JobRequirements = {
        ['unemployed'] = 0,
        ['taxi'] = 5,
        ['mechanic'] = 10,
        ['ambulance'] = 15,
        ['police'] = 20,
        ['agent'] = 25,
        ['admin'] = 50
    }
}
```

## كيفية إضافة وظيفة جديدة

### عبر config.lua
```lua
Config.ExperienceSystem.JobRequirements = {
    ['taxi'] = 5,
    ['mechanic'] = 10,
    ['new_job'] = 30, -- إضافة وظيفة جديدة تتطلب مستوى 30
}
```

**ملاحظة**: الوظائف غير المذكورة في هذه القائمة ستظهر بشكل عادي بدون متطلبات خبرة.

## المميزات

1. **مؤشرات بصرية**: ألوان مختلفة للوظائف حسب الأهلية
2. **عرض متطلبات الخبرة**: يظهر مستوى الخبرة المطلوب لكل وظيفة
3. **منع التقديم**: لا يمكن للاعب التقديم على وظيفة إذا لم تكن خبرته كافية
4. **رسائل واضحة**: إشعارات توضح سبب عدم القدرة على التقديم
5. **سهولة التخصيص**: يمكن تعديل المتطلبات من config.lua
6. **التوافق**: يعمل مع نظام الخبرة الموجود zahya_xplevel

## استكشاف الأخطاء

### المشكلة: النظام لا يعمل
- تأكد من أن `Config.ExperienceSystem.Enabled = true`
- تأكد من وجود مورد `zahya_xplevel` وأنه يعمل
- تحقق من أن اللاعب لديه بيانات خبرة في قاعدة البيانات

### المشكلة: الألوان لا تظهر
- تأكد من أن ESX UI يدعم HTML tags
- جرب تغيير الألوان في config.lua

### المشكلة: متطلبات الخبرة خاطئة
- تحقق من إعدادات `Config.ExperienceSystem.JobRequirements`
- إذا كنت تستخدم قاعدة البيانات، تحقق من عمود `required_level` في جدول `jobs`

## الدعم
إذا واجهت أي مشاكل، تأكد من:
1. إعادة تشغيل المورد بعد التعديل
2. التحقق من console للأخطاء
3. التأكد من أن نظام الخبرة zahya_xplevel يعمل بشكل صحيح
