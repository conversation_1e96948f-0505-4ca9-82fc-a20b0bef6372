-- Menu configuration, array of menus to display
menuConfigs = {
    ['emotes'] = {                                  -- Example menu for emotes when player is on foot
        enableMenu = function()                     -- Function to enable/disable menu handling
            local player = GetPlayerPed(-1)
            return IsPedOnFoot(player)
        end,
        data = {                                    -- Data that is passed to Javascript
            keybind = "~",                         -- Wheel keybind to use
            style = {                               -- Wheel style settings
                sizePx = 600,                       -- Wheel size in pixels
                slices = {                          -- Slice style settings
                    default = { ['fill'] = '#555555', 	['stroke'] = '#aaaaaa', 	['stroke-width'] = 2, ['opacity'] = 0.60 },
                    hover 	= { ['fill'] = '#0094FF', 	['stroke'] = '#aaaaaa', 	['stroke-width'] = 2, ['opacity'] = 0.75 },
                    selected = { ['fill'] = '#555555', 	['stroke'] = '#aaaaaa', 	['stroke-width'] = 2, ['opacity'] = 0.60 }
                },
                titles = {                          -- Text style settings
                    default = { ['fill'] = '#ffffff', 	['stroke'] = 'none', 	['font'] = 'Verdana', ['font-size'] = 20 },
                    hover 	= { ['fill'] = '#ffffff', 	['stroke'] = 'none', 	['font'] = 'Verdana', ['font-size'] = 20 },
                    selected = { ['fill'] = '#ffffff', 	['stroke'] = 'none', 	['font'] = 'Verdana', ['font-size'] = 20 }
                }
            },
            wheels = {                              -- Array of wheels to display
                {
                    navAngle = 270,                 -- Oritentation of wheel
                    minRadiusPercent = 0.3,         -- Minimum radius of wheel in percentage
                    maxRadiusPercent = 0.6,         -- Maximum radius of wheel in percentage
                    labels = {"إلغاء", "لا", "تشجيع", "تصفيق", "ضم اليد", "انتظر على الجدار", "نوم سرير اسعاف"},
                    commands = {"e cancel", "e no", "e cheer", "e slowclap", "e foldarms", "e leanwall", "getintostr"}
                },
                {
                    navAngle = 285,                 -- Oritentation of wheel
                    minRadiusPercent = 0.6,         -- Minimum radius of wheel in percentage
                    maxRadiusPercent = 0.9,         -- Maximum radius of wheel in percentage
                    labels = {"تحية", "حبتين", "ضرب الوجه", "اللعنة", "مرهق", "سيجار", "حارس", "سحب سلاح", "تنظيم جمهور", "حمل ع ظهرك", "حمل على كتفك", "تهديد رهينة"},
                    commands = {"e salute", "e peace", "e palm", "e damn", "e fail",'e cigar', "e copidle", "e holster", "e copcrowd2", "piggyback", "carry", "takehostage"}
                }
            }
        }
    },

    ['vehicles'] = {                                -- Example menu for emotes when player is in a vehicle
        enableMenu = function()                     -- Function to enable/disable menu handling
            local player = GetPlayerPed(-1)
            return IsPedInAnyVehicle(player, false)
        end,
        data = {                                    -- Data that is passed to Javascript
            keybind = "~",                         -- Wheel keybind to use
            style = {                               -- Wheel style settings
                sizePx = 400,                       -- Wheel size in pixels
                slices = {                          -- Slice style settings
                    default = { ['fill'] = '#555555', 	['stroke'] = '#aaaaaa', ['stroke-width'] = 3, ['opacity'] = 0.60 },
                    hover 	= { ['fill'] = '#0094FF', 	['stroke'] = '#aaaaaa', ['stroke-width'] = 3, ['opacity'] = 0.75 },
                    selected = { ['fill'] = '#555555', 	['stroke'] = '#aaaaaa', ['stroke-width'] = 3, ['opacity'] = 0.60 }
                },
                titles = {                          -- Text style settings
                    default = { ['fill'] = '#ffffff', 	['stroke'] = 'none', ['font'] = 'Verdana', ['font-size'] = 20 },
                    hover 	= { ['fill'] = '#ffffff', 	['stroke'] = 'none', ['font'] = 'Verdana', ['font-size'] = 20 },
                    selected = { ['fill'] = '#ffffff', 	['stroke'] = 'none', ['font'] = 'Verdana', ['font-size'] = 20 }
                }
            },
            wheels = {                              -- Array of wheels to display
                {
                    navAngle = 270,                 -- Oritentation of wheel
                    minRadiusPercent = 0.4,         -- Minimum radius of wheel in percentage
                    maxRadiusPercent = 0.8,         -- Maximum radius of wheel in percentage
                    labels = {"سائق","راكب امام","راكب يسار","راكب يمين", "شنطة", "كبوت", "سبوت لايت", "مقعد السائق"},
                    commands = {"door 1","door 2","door 3","door 4", "trunk", "hood", "spotlight", "shuff"}
                }
            }
        }
    }
}

LeoJobs = {'police','agent','admin'}

Config = {}

-- _holsterweapon
Config.UseESX 		  	= true
Config.cooldownPolice 	= 1000 -- Will work with ESX only
Config.cooldownCitizen 	= 3000
Config.cooldownCurrent 	= 0

--hide trunk
Config.hidetrunk = {
	blacklisted_model = {
		[1951180813] = 'taco',
		[*********] = 'mule',
		[-74027062] = 'newsvan',
		[79613282] = 'newsvan2',
		[-*********] = 'taxi',
		[**********] = 'taxi2',
		[*********] = 'forklift3_docker',
		--trailers
		[-**********] = 'trailerswb', 
		[-**********] = 'trailerswb2',
	}
}

-- _holsterweapon
-- Add/remove weapon hashes here to be added for holster checks.
Config.Weapons = {
	"WEAPON_PISTOL",
	"WEAPON_COMBATPISTOL",
	"WEAPON_APPISTOL",
	"WEAPON_PISTOL50",
	"WEAPON_SNSPISTOL",
	"WEAPON_HEAVYPISTOL",
    "WEAPON_CARBINERIFLE",
    "WEAPON_MICROSMG",
    "WEAPON_KNIFE",
	"WEAPON_VINTAGEPISTOL",
	"WEAPON_MARKSMANPISTOL",
	"WEAPON_MACHINEPISTOL",
	"WEAPON_VINTAGEPISTOL",
	"WEAPON_PISTOL_MK2",
	"WEAPON_SNSPISTOL_MK2",
	"WEAPON_FLAREGUN",
	"WEAPON_STUNGUN",
	"WEAPON_REVOLVER",
	"WEAPON_PUMPSHOTGUN",
    "WEAPON_PUMPSHOTGUN_MK2",
    "WEAPON_MACHETE"
}
