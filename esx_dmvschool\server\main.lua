AddEventHandler('esx:playerLoaded', function(source)
	TriggerEvent('esx_license:getLicenses', source, function(licenses)
		TriggerClientEvent('esx_dmvschool:loadLicenses', source, licenses)
	end)
end)
-- RegisterNetEvent('esx_dmvschool:reloadLicense')
-- AddEventHandler('esx_dmvschool:reloadLicense', function()
-- 	print('refresh car lisence')
-- 	TriggerEvent('esx_license:getLicenses', source, function(licenses)
-- 		TriggerClientEvent('esx_dmvschool:loadLicenses', source, licenses)
-- 	end)
-- end)

RegisterNetEvent('esx_dmvschool:removeLicense')
AddEventHandler('esx_dmvschool:removeLicense', function(type)
	print('refresh car lisence')
	local _source = source

	TriggerEvent('esx_license:removeLicense', _source, type, function()
		TriggerEvent('esx_license:getLicenses', _source, function(licenses)
			TriggerClientEvent('esx_dmvschool:loadLicenses', _source, licenses)
		end)
	end)
end)



RegisterNetEvent('hamada:schoolrefresh')
AddEventHandler('hamada:schoolrefresh', function(player)
	-- local _source = source
	TriggerEvent('esx_license:getLicenses', player, function(licenses)
		TriggerClientEvent('esx_dmvschool:loadLicenses', player, licenses)
	end)
end)

RegisterNetEvent('esx_dmvschool:addLicense')
AddEventHandler('esx_dmvschool:addLicense', function(type)
	local _source = source

	TriggerEvent('esx_license:addLicense', _source, type, function()
		TriggerEvent('esx_license:getLicenses', _source, function(licenses)
			TriggerClientEvent('esx_dmvschool:loadLicenses', _source, licenses)
		end)
	end)
end)

RegisterNetEvent('esx_dmvschool:pay')
AddEventHandler('esx_dmvschool:pay', function(price)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)

	xPlayer.removeMoney(price)
	TriggerClientEvent('esx:showNotification', _source, _U('you_paid', ESX.Math.GroupDigits(price)))
end)
