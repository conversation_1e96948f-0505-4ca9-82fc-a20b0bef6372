html {
	overflow: hidden;
	font-family: 'Open Sans', sans-serif;
}

.full-screen {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
}

.question-container {
	width: 70%;
	height: 700px;
	background-color: #fff;
	border: 1px solid #222;
	border-radius: 1px;
	margin-left: auto;
	margin-right: auto;
	overflow: hidden;
	z-index: 9999999;
	display: none;
}

.header {
	width: 100%;
	height: 20%;
	background-color: #eee;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: center;
}

.header .logo {
	width: 80%;
	max-height: 50%;
	margin: auto;
	margin-bottom: 0;
}

.header h1 {
	font-size: 2em;
	text-align: center;
	margin: auto;
}

.body {
	width: 100%;
	height: 80%;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	display: none;
}

.content {
	display: flex;
	flex-direction: column;
	align-self: center;
	width: 90%;
	height: 70%;
	margin: auto;
	border: none;
}

.content .logo {
	width: auto;
	max-height: auto;
	margin: auto;
	margin-bottom: 0;
}

.content h2, p {
	margin: 5px;
}

.buttonspot {
  display: flex;
  flex-direction: column;
  align-self: center;
  width: auto;
  height: 15%;
  margin: auto;
  border:none;
}

.buttonspot h2, p {
	margin: 5px;
}

.button {
  width: 30%;
  height: 45px;
  align-self: center;
  text-align: center;
  margin-left: 40px;
  margin-right: 40px;
  outline: none;
  border: none;
  background: #5ea9c7;
  background-image: linear-gradient(to bottom, #6699ff, #4d88ff);
  border-radius: 120px 120px 120px 120px;;
  color: #ffffff;
  font-size: 1.8em;
  text-decoration: none;
  text-shadow:
       1px 1px 0 #000,
     -1px -1px 0 #000,  
      1px -1px 0 #000,
      -1px 1px 0 #000,
       1px 1px 0 #000;
}

.form {
	display: flex;
	flex-direction: row;
	justify-content: center;
	flex-wrap: wrap;
	width: 100%;
	margin-top: 5px;
}

.form div {
	margin: auto;
	margin-top: 5px;
	margin-bottom: 5px;
	width: 100%;
}

.submit {
	align-items: center;
	height: 30px;
	text-align: center;
	width: 30%;
	background: #017ebc;
	background-image: linear-gradient(to bottom, #6699ff, #4d88ff);
	border-radius: 4px;
	color: #fff;
	font-size: 1.2em;
	margin-top: 10px;
}

.barre-progression {
	width: 50%;
	align-self: center;
	height: 10%;
	margin: auto;
	margin-top: 0;
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	padding: 0;
}

.barre-progression h2 {
	width: 100%;
	height: 60%;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0;
	color: #000;
  font-size: 1.8em;
  margin-top: 10px;
}

.progression {
	width: 80%;
	border: 0 none;
	background: #ddd;
	border-radius: 14px;
	box-shadow: inset 0px 1px 1px rgba(0, 0, 0, 0.5), 0px 1px 0px rgba(255, 255, 255, 0.2);
}

.progression::-moz-progress-bar {
	background: #FFF;
	border-radius: 14px;
	box-shadow: inset 0 -2px 4px rgba(0, 0, 0, 0.4), 0 2px 5px 0px rgba(0, 0, 0, 0.3);
}

.progression::-webkit-progress-bar {
	background: transparent;
}

.progression::-webkit-progress-value {
	background-image: linear-gradient(to bottom, #6699ff, #4d88ff);
	border-radius: 14px;
	box-shadow: inset 0 -2px 4px rgba(0, 0, 0, 0.4), 0 2px 5px 0px rgba(0, 0, 0, 0.3);
}

.bold-text {
	font-weight: bold;
	font-size: 1.3em;
}