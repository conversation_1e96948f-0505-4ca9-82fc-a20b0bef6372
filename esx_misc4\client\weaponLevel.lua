
Citizen.CreateThread(function()
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(500)
	end

	ESX.PlayerData = ESX.GetPlayerData()
end)
RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
	Citizen.Wait(50)
	-- Citizen.Wait(5000)
	--TriggerServerEvent('esx_adminjob:forceBlip')
	-- TriggerServerEvent('esx_adminjob:Check_me')
end)

function checkRequiredXPlevel(required)
	local level = exports.zahya_xplevel.ESXP_GetRank()
	if level >= required then
		return true
	else
		return false
	end
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		local sleep = true
		local ped = PlayerPedId()
		local _,pedWeapon = GetCurrentPedWeapon(ped, true)
		if  ESX.PlayerData.job.name == 'police' and  ESX.PlayerData.job.grade > 2 or  ESX.PlayerData.job.name == 'admin' or  ESX.PlayerData.job.name == 'agent' and  ESX.PlayerData.job.grade > 1 then
			local sdjvhbsdjvh = nil
		else
			if IsPedArmed(ped, 6) and GetHashKey('WEAPON_UNARMED') ~= pedWeapon then
				sleep = false
				for i=1, #Config.WeaponLevel, 1 do
					local item = Config.WeaponLevel[i]
					if pedWeapon == GetHashKey(item.item) then
						if not checkRequiredXPlevel(item.level) then 
							SetCurrentPedWeapon(ped, 'WEAPON_UNARMED', 1)
							ESX.ShowNotification('المستوى المطلوب لسلاح <font color=orange>'..item.label..'</font> <font color=red>'..item.level)
							blocked	 = false
							TriggerEvent('abdulrhman:esx_animations2:holsterweapon:fix_blocked') -- يصلح مشكلة ركوب السيارة و إخراج السلاح
						end
					end					
				end	
			end
		end
		if sleep then
			Citizen.Wait(500)
		end
	end
end)

--[[ Citizen.CreateThread(function()
	local sleep = 3000
	while true do
		sleep = 1000
		local ped = PlayerPedId()
		local _,pedWeapon = GetCurrentPedWeapon(ped, true)
		
		if IsPedArmed(ped, 6) and GetHashKey('WEAPON_UNARMED') ~= pedWeapon and GetHashKey('WEAPON__PETROLCAN') ~= pedWeapon then
			local panic = exports.esx_misc4:panicstate()
			if panic['peace_time'].state or panic['9eanh_time'].state then
				SetCurrentPedWeapon(ped, 'WEAPON_UNARMED', 1)
				blocked	 = false
			end
		end
		Citizen.Wait(sleep)
	end
end) ]]