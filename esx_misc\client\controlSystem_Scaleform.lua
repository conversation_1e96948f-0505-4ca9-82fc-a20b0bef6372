-- draw scaleform multi use
--local function DisplayScaleform(title, description, time)
function DisplayScaleform2(title, description, time)
    if time == nil then time = 4000 end
    Citizen.CreateThread(function()
      local scaleform = RequestScaleformMovie("mp_big_message_freemode")
      while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
      end
    
      BeginScaleformMovieMethod(scaleform, "SHOW_SHARD_WASTED_MP_MESSAGE")
      PushScaleformMovieMethodParameterString(title)
      PushScaleformMovieMethodParameterString(description)
      PushScaleformMovieMethodParameterInt(5)
      EndScaleformMovieMethod()
      
      local show = true
      Citizen.SetTimeout(6000, function()
        show = false
      end)
    
      while show do
        Citizen.Wait(0)
        DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255) -- Draw the scaleform fullscreen
      end
    end)
end

RegisterNetEvent('esx_misc:controlSystemScaleform_buyweapon')
AddEventHandler('esx_misc:controlSystemScaleform_buyweapon', function(money)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﻞﺑﺎﻘﻣ ﺀﺍﺮﺷ ﻢﺗ", "<FONT FACE='A9eelsh'>~g~$"..money.."</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_givemoney_toAll')
AddEventHandler('esx_misc:controlSystemScaleform_givemoney_toAll', function(money)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﻝﺎﻣ ﺀﺎﻄﻋﺇ", "<FONT FACE='A9eelsh'>~y~ﺎﻴﻟﺎﺣ ﻦﻴﻠﺼﺘﻤﻟﺍ ﻦﻴﺒﻋﻼﻟﺍ ﻊﻴﻤﺠﻟ ~g~$"..money.."~y~ ﻎﻠﺒﻣ ﺀﺎﻄﻋﺇ ﻢﺗ</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_giveXP_toAll')
AddEventHandler('esx_misc:controlSystemScaleform_giveXP_toAll', function(XP)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺓﺮﺒﺧ ﺀﺎﻄﻋﺇ", "<FONT FACE='A9eelsh'>~y~ﺎﻴﻟﺎﺣ ﻦﻴﻠﺼﺘﻤﻟﺍ ﻦﻴﺒﻋﻼﻟﺍ ﻊﻴﻤﺠﻟ ﺓﺮﺒﺧ ~b~"..XP.."~y~ ﺀﺎﻄﻋﺇ ﻢﺗ</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_give_money_toONE')
AddEventHandler('esx_misc:controlSystemScaleform_give_money_toONE', function(money)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺾﻳﻮﻌﺗ ﺭﺍﺮﻗ", "<FONT FACE='A9eelsh'>~g~ﻲﻓﺮﺼﻤﻟﺍ ﻚﺑﺎﺴﺣ ﻲﻓ ﻉﺍﺪﻳﻹﺍ ﻢﺗ ~b~$"..money.."~g~ ﻎﻠﺒﻣ</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_remove_money_toONE')
AddEventHandler('esx_misc:controlSystemScaleform_remove_money_toONE', function(money)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﻢﺼﺧ ﺔﺑﻮﻘﻋ ﺭﺍﺮﻗ", "<FONT FACE='A9eelsh'>~r~ﻲﻓﺮﺼﻤﻟﺍ ﻚﺑﺎﺴﺣ ﻦﻣ ~b~$"..money.."~r~ ﻎﻠﺒﻣ</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_mzadMabrokCar')
AddEventHandler('esx_misc:controlSystemScaleform_mzadMabrokCar', function(car_plate)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~دﺍﺰﻤﻟﺎﺑ ﺰﺋﺎﻓ", "<FONT FACE='A9eelsh'>~w~ﻚﺟﺍﺮﻛ ﻲﻓ ~g~"..car_plate.."~w~ ﺔﺒﻛﺮﻤﻟﺍ ﻞﻳﻮﺤﺗ ﻢﺗ</font>")
	--PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_giveblack_money_toONE')
AddEventHandler('esx_misc:controlSystemScaleform_giveblack_money_toONE', function(money)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺾﻳﻮﻌﺗ ﺭﺍﺮﻗ", "<FONT FACE='A9eelsh'>~g~ﻚﺘﺒﻴﻘﺣ ﻰﻟﺍ ﻲﻋﺮﺷ ﺮﻴﻏ ~r~$"..money.."~g~ ﻎﻠﺒﻣ</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_removeblack_money_toONE')
AddEventHandler('esx_misc:controlSystemScaleform_removeblack_money_toONE', function(money)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺔﺑﻮﻘﻋ ﺭﺍﺮﻗ", "<FONT FACE='A9eelsh'>~g~ﻲﻋﺮﺷ ﺮﻴﻏ ~r~$"..money.."~g~ ﻎﻠﺒﻣ</font>", 6000)
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystem_Scaleform')
AddEventHandler('esx_misc:controlSystem_Scaleform', function()
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ءﺎﻋﺪﺘﺳﺍ", "<FONT FACE='A9eelsh'>~y~ةﺪﻋﺎﺴﻤﻟﺍ و ﻢﻋﺪﻟﺍ دﺭﻮﻜﺳﺪﻟﺍ ﻰﻟﺍ ﻪﺟﻮﺗ ﻚﻴﻠﻋ</font>")
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_giveXP')
AddEventHandler('esx_misc:controlSystemScaleform_giveXP', function(XP)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺓﺮﺒﺧ ﺀﺎﻄﻋﺇ", "<FONT FACE='A9eelsh'>~y~ﻡﺎﻌﻟﺍ ﻡﺎﻈﻨﻟﺍ ﻭ ﻦﻴﻧﺍﻮﻘﻟﺎﺑ ﻚﻣﺍﺰﺘﻟﺃ ﻰﻠﻋ ﺍﺮﻜﺷ ﺓﺮﺒﺧ ~b~"..XP.."~y~ ﻚﺋﺎﻄﻋﺇ ﻢﺗ</font>")
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_bestactor')
AddEventHandler('esx_misc:controlSystemScaleform_bestactor', function(XP)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺓﺮﺒﺧ ﺀﺎﻄﻋﺇ", "<FONT FACE='A9eelsh'>~y~ﻡﺎﻌﻟﺍ ﻡﺎﻈﻨﻟﺍ ﻭ ﻦﻴﻧﺍﻮﻘﻟﺎﺑ ﻚﻣﺍﺰﺘﻟﺃ ﻰﻠﻋ ﺍﺮﻜﺷ ﺓﺮﺒﺧ ~b~"..XP.."~y~ ﻚﺋﺎﻄﻋﺇ ﻢﺗ</font>")
	PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	--PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)


RegisterNetEvent('esx_misc:controlSystemScaleform_removeXP')
AddEventHandler('esx_misc:controlSystemScaleform_removeXP', function(XP)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺓﺮﺒﺧ ﻢﺼﺧ", "<FONT FACE='A9eelsh'>~r~ﻡﺎﻌﻟﺍ ﻡﺎﻈﻟﺍ ﻭ ﻦﻴﻧﺍﻮﻘﻟﺎﺑ ﻡﺍﺰﻟﻹﺍ ﺀﺎﺟﺮﻟﺍ ﺓﺮﺒﺧ ~b~"..XP.."~r~ ﻢﺼﺧ ﻢﺗ</font>")
	--PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_WinnerMZAD')
AddEventHandler('esx_misc:controlSystemScaleform_WinnerMZAD', function(label)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~ﺩﺍﺰﻤﻟﺎﺑ ﺰﺋﺎﻓ", "<FONT FACE='A9eelsh'>~y~ﺪﻳﺪﺤﺗﻭ ﺩﺍﺰﻤﻟﺎﺑ ﺕﺰﻓ ﻙﻭﺮﺒﻣ "..label.." ﺔﻄﻳﺮﺨﻟﺍ ﻰﻠﻋ ﺮﻔﺻﻷﺍ ﻥﻮﻠﻟﺎﺑ</font>")
	--PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)

RegisterNetEvent('esx_misc:controlSystemScaleform_gift')
AddEventHandler('esx_misc:controlSystemScaleform_gift', function(money,XP)
	DisplayScaleform2("<FONT FACE='A9eelsh'>~w~نﻮﺘﻟﻭ ﺔﻌﻃﺎﻘﻣ ﻲﻓ ﻚﺑ ﺎﺒﺣﺮﻣ", "<FONT FACE='A9eelsh'>~y~ﺓﺮﺒﺧ ﻰﻠﻋ ﻯﻮﺘﺤﺗ "..XP.."ﻲﻟﺎﻣ ﻎﻠﺒﻣ ﻭ "..money)
	--PlaySoundFrontend(-1, "MEDAL_UP", "HUD_MINI_GAME_SOUNDSET", true)
	PlaySoundFrontend(-1, "LOOSE_MATCH", "HUD_MINI_GAME_SOUNDSET", true)
end)