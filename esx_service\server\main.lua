local InService    = {}
local MaxInService = {}


function GetInServiceCount(name)
	local count = 0

	for k,v in pairs(InService[name]) do
		if v == true then
			count = count + 1
		end
	end

	return count
end

function activateService(name, max)
	InService[name] = {}
	MaxInService[name] = max
end


activateService('police', 100)
activateService('agent', 100)
activateService('ambulance', 100)
activateService('admin', 100)

activateService('cement', 100)
activateService('farmer', 100)
activateService('fisherman', 100)
activateService('fueler', 100)
activateService('lumberjack', 100)
activateService('miner', 100)
activateService('slaughterer', 100)
activateService('mechanic', 100)
activateService('slaughterer', 100)
activateService('tailor', 100)
activateService('vegetables', 100)
-- activateService('khobs', 100)
-- activateService('fork', 5)
activateService('taxi', 100)
activateService('unemployed', 1000)
-- activateService('reporter', 5)

AddEventHandler('hamada:getjobservicecount', function(cb) 
	local jobsstatus = {
		Police = GetInServiceCount('police'),
		Ambulance = GetInServiceCount('ambulance'),
		Agent = GetInServiceCount('agent'),
		mechanic = GetInServiceCount('mechanic'),
		Mechanic = GetInServiceCount('mechanic'),
		slaughterer = GetInServiceCount('slaughterer'),
		lumberjack = GetInServiceCount('lumberjack'),
		fisherman = GetInServiceCount('fisherman'),
		tailor = GetInServiceCount('tailor'),
		fueler = GetInServiceCount('fueler'),
		miner = GetInServiceCount('miner'),
		vegetables = GetInServiceCount('vegetables'),
		-- fork = GetInServiceCount('fork'),
		taxi = GetInServiceCount('taxi'),
		-- khobs = GetInServiceCount('khobs'),
		-- reporter = GetInServiceCount('reporter'),
		-- unemployed = GetInServiceCount('unemployed'),
	}
	if jobsstatus then
		cb(jobsstatus)
	end
end)

AddEventHandler('hamada:ispoliceplayeronservice', function(cb, playerId, job) 
	-- print(playerId)
	if InService[job][playerId] == nil then
		cb(false)
	else
		cb(true)
	end
end)

ESX.RegisterServerCallback('hamada:isplayeronduty', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if InService[xPlayer.job.name][source] == nil then
		cb(false)
	else
		cb(true)
	end
end)


RegisterNetEvent('esx_service:disableService')
AddEventHandler('esx_service:disableService', function(name)
	local _source = source
	local inServiceCount = GetInServiceCount(name)
	local xTarget = ESX.GetPlayerFromId(_source)
	local user = xTarget.getName()
	local rpname = user	
	local notification = {
		title    = 'تسجيل خروج',
		subject  = xTarget.getJob().grade_label,
		msg      = rpname
		--iconType = 1
	}
	notifyAllInService(notification, name, source)
	--TriggerClientEvent("esx:showNotification", k, "<p align=center><font color=yellow>اعلان خدمة موظف</font></br><font color=gray>"..xTarget.getJob().grade_label.."<b></br><font color=#be0000> تسجيل خروج <font color=white>"..rpname.."<b>")
	InService[name][source] = nil
end)

function notifyAllInService(notification, name, source)
	for k,v in pairs(InService[name]) do
		if v == true then
			TriggerClientEvent('esx_service:notifyAllInService', k, notification, source)
		end
	end
end

ESX.RegisterServerCallback('esx_service:enableService', function(source, cb, name)
	local _source = source
	local inServiceCount = GetInServiceCount(name)
	local xTarget = ESX.GetPlayerFromId(_source)
	local user = xTarget.getName()
	local rpname = user	
	if inServiceCount >= MaxInService[name] then
		cb(false, MaxInService[name], inServiceCount)
	else
		InService[name][source] = true
		cb(true, MaxInService[name], inServiceCount)
		--Notify
		local notification = {
			title    = 'تسجيل دخول',
			subject  = xTarget.getJob().grade_label,
			msg      = rpname
			--iconType = 1
		}
		notifyAllInService(notification, name, source)
	end
end)

ESX.RegisterServerCallback('esx_service:isInService', function(source, cb, name)
	local isInService = false

	if InService[name] ~= nil then
		if InService[name][source] then
			isInService = true
		end
	else
		print(('[esx_service] [^3WARNING^7] A service "%s" is not activated'):format(name))
	end

	cb(isInService)
end)

ESX.RegisterServerCallback('esx_service:isPlayerInService', function(source, cb, name, target)
	local isPlayerInService = false
	local targetXPlayer = ESX.GetPlayerFromId(target)

	if InService[name][targetXPlayer.source] then
		isPlayerInService = true
	end

	cb(isPlayerInService)
end)

ESX.RegisterServerCallback('esx_service:getInServiceList', function(source, cb, name)
	cb(InService[name])
end)

AddEventHandler('playerDropped', function()
	local _source = source
	local xTarget = ESX.GetPlayerFromId(_source)
	if not xTarget then return end
	local user = xTarget.getName()
	local rpname = user	
	for k,v in pairs(InService) do
		if v[_source] == true then
			local notification = {
				title    = 'تسجيل خروج',
				subject  = xTarget.getJob().grade_label,
				msg      = rpname
				--iconType = 1
			}
			notifyAllInService(notification, k, _source)
			v[_source] = nil
		end
	end
end)

RegisterNetEvent('esx_service:notifyAllInServiceLeave')
AddEventHandler('esx_service:notifyAllInServiceLeave', function()
	local _source = source
	local xTarget = ESX.GetPlayerFromId(_source)
	local user = xTarget.getName()
	local rpname = user	
	for k,v in pairs(InService) do
		if v[_source] == true then
			local notification = {
				title    = 'تسجيل خروج',
				subject  = xTarget.getJob().grade_label,
				msg      = rpname
				--iconType = 1
			}
			notifyAllInService(notification, k, _source)
			v[_source] = nil
		end
	end
end)
